2025-07-08 19:44:44,935 - ERROR - 分析过程中出现错误: 'PerformanceAnalyzer' object has no attribute 'kperfviewer_path'
2025-07-08 19:45:06,433 - INFO - 环境变量验证通过:
2025-07-08 19:45:06,434 - INFO -   VIEWER_DIR: D:\feature_0603\debug\wps_build\WPSOffice\office6
2025-07-08 19:45:06,434 - INFO -   DAT_SOURCE_DIR: C:\Users\<USER>\Desktop\ANALYZE\src_dat
2025-07-08 19:45:06,434 - INFO -   DEST_DIR: C:\Users\<USER>\Desktop\ANALYZE\output
2025-07-08 19:45:06,434 - INFO -   TOP_NUM: 40
2025-07-08 19:45:06,435 - INFO - 创建输出目录: C:\Users\<USER>\Desktop\ANALYZE\output\stats
2025-07-08 19:45:06,444 - INFO - 创建输出目录: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse
2025-07-08 19:45:06,444 - INFO - 开始性能数据分析
2025-07-08 19:45:06,445 - INFO - 找到 30 个.dat文件
2025-07-08 19:45:06,445 - INFO - 处理进度: 1/30
2025-07-08 19:45:06,445 - INFO - 开始处理文件: perfmon-angle-20250707_095219-wpp文档绘制.dat
2025-07-08 19:45:06,445 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-angle-20250707_095219-wpp文档绘制.dat
2025-07-08 19:45:06,496 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-angle-20250707_095219-wpp文档绘制.dat.txt
2025-07-08 19:45:06,496 - INFO - 提取到 0 个event相关的callId
2025-07-08 19:45:06,496 - WARNING - 未找到event相关的callId: perfmon-angle-20250707_095219-wpp文档绘制.dat
2025-07-08 19:45:06,496 - INFO - 处理进度: 2/30
2025-07-08 19:45:06,496 - INFO - 开始处理文件: perfmon-angle-20250707_101003-wpp旋转.dat
2025-07-08 19:45:06,496 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-angle-20250707_101003-wpp旋转.dat
2025-07-08 19:45:06,778 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-angle-20250707_101003-wpp旋转.dat.txt
2025-07-08 19:45:06,778 - INFO - 提取到 0 个event相关的callId
2025-07-08 19:45:06,778 - WARNING - 未找到event相关的callId: perfmon-angle-20250707_101003-wpp旋转.dat
2025-07-08 19:45:06,778 - INFO - 处理进度: 3/30
2025-07-08 19:45:06,778 - INFO - 开始处理文件: perfmon-angle-20250707_101321_wpp缩放.dat
2025-07-08 19:45:06,778 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-angle-20250707_101321_wpp缩放.dat
2025-07-08 19:45:06,869 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-angle-20250707_101321_wpp缩放.dat.txt
2025-07-08 19:45:06,870 - INFO - 提取到 0 个event相关的callId
2025-07-08 19:45:06,870 - WARNING - 未找到event相关的callId: perfmon-angle-20250707_101321_wpp缩放.dat
2025-07-08 19:45:06,870 - INFO - 处理进度: 4/30
2025-07-08 19:45:06,870 - INFO - 开始处理文件: perfmon-angle-20250707_101853_wpp切换页面.dat
2025-07-08 19:45:06,870 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-angle-20250707_101853_wpp切换页面.dat
2025-07-08 19:45:06,970 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-angle-20250707_101853_wpp切换页面.dat.txt
2025-07-08 19:45:06,971 - INFO - 提取到 0 个event相关的callId
2025-07-08 19:45:06,971 - WARNING - 未找到event相关的callId: perfmon-angle-20250707_101853_wpp切换页面.dat
2025-07-08 19:45:06,971 - INFO - 处理进度: 5/30
2025-07-08 19:45:06,971 - INFO - 开始处理文件: perfmon-angle-20250707_102205_wpp缩略图生成.dat
2025-07-08 19:45:06,971 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-angle-20250707_102205_wpp缩略图生成.dat
2025-07-08 19:45:07,041 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-angle-20250707_102205_wpp缩略图生成.dat.txt
2025-07-08 19:45:07,041 - INFO - 提取到 0 个event相关的callId
2025-07-08 19:45:07,041 - WARNING - 未找到event相关的callId: perfmon-angle-20250707_102205_wpp缩略图生成.dat
2025-07-08 19:45:07,041 - INFO - 处理进度: 6/30
2025-07-08 19:45:07,041 - INFO - 开始处理文件: perfmon-angle-20250707_102653_wpp动画播放对象动画.dat
2025-07-08 19:45:07,041 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-angle-20250707_102653_wpp动画播放对象动画.dat
2025-07-08 19:45:11,237 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-angle-20250707_102653_wpp动画播放对象动画.dat.txt
2025-07-08 19:45:11,237 - INFO - 提取到 0 个event相关的callId
2025-07-08 19:45:11,237 - WARNING - 未找到event相关的callId: perfmon-angle-20250707_102653_wpp动画播放对象动画.dat
2025-07-08 19:45:11,238 - INFO - 处理进度: 7/30
2025-07-08 19:45:11,238 - INFO - 开始处理文件: perfmon-angle-20250707_103437_wpp动画页切换.dat
2025-07-08 19:45:11,238 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-angle-20250707_103437_wpp动画页切换.dat
2025-07-08 19:45:11,413 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-angle-20250707_103437_wpp动画页切换.dat.txt
2025-07-08 19:45:11,414 - INFO - 提取到 0 个event相关的callId
2025-07-08 19:45:11,414 - WARNING - 未找到event相关的callId: perfmon-angle-20250707_103437_wpp动画页切换.dat
2025-07-08 19:45:11,414 - INFO - 处理进度: 8/30
2025-07-08 19:45:11,414 - INFO - 开始处理文件: perfmon-angle-20250707_103855_wpp动画测试荧光笔.dat
2025-07-08 19:45:11,414 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-angle-20250707_103855_wpp动画测试荧光笔.dat
2025-07-08 19:45:11,876 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-angle-20250707_103855_wpp动画测试荧光笔.dat.txt
2025-07-08 19:45:11,876 - INFO - 提取到 0 个event相关的callId
2025-07-08 19:45:11,876 - WARNING - 未找到event相关的callId: perfmon-angle-20250707_103855_wpp动画测试荧光笔.dat
2025-07-08 19:45:11,876 - INFO - 处理进度: 9/30
2025-07-08 19:45:11,876 - INFO - 开始处理文件: perfmon-angle-20250707_155842_wps文档绘制.dat
2025-07-08 19:45:11,876 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-angle-20250707_155842_wps文档绘制.dat
2025-07-08 19:45:12,000 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-angle-20250707_155842_wps文档绘制.dat.txt
2025-07-08 19:45:12,000 - INFO - 提取到 0 个event相关的callId
2025-07-08 19:45:12,000 - WARNING - 未找到event相关的callId: perfmon-angle-20250707_155842_wps文档绘制.dat
2025-07-08 19:45:12,000 - INFO - 处理进度: 10/30
2025-07-08 19:45:12,000 - INFO - 开始处理文件: perfmon-angle-20250707_160551_wps块选.dat
2025-07-08 19:45:12,000 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-angle-20250707_160551_wps块选.dat
2025-07-08 19:45:12,252 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-angle-20250707_160551_wps块选.dat.txt
2025-07-08 19:45:12,252 - INFO - 提取到 0 个event相关的callId
2025-07-08 19:45:12,252 - WARNING - 未找到event相关的callId: perfmon-angle-20250707_160551_wps块选.dat
2025-07-08 19:45:12,252 - INFO - 处理进度: 11/30
2025-07-08 19:45:12,253 - INFO - 开始处理文件: perfmon-angle-20250707_160824_wps插入.dat
2025-07-08 19:45:12,253 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-angle-20250707_160824_wps插入.dat
2025-07-08 19:45:12,445 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-angle-20250707_160824_wps插入.dat.txt
2025-07-08 19:45:12,445 - INFO - 提取到 0 个event相关的callId
2025-07-08 19:45:12,445 - WARNING - 未找到event相关的callId: perfmon-angle-20250707_160824_wps插入.dat
2025-07-08 19:45:12,445 - INFO - 处理进度: 12/30
2025-07-08 19:45:12,445 - INFO - 开始处理文件: perfmon-angle-20250707_161351_wps滚动文档.dat
2025-07-08 19:45:12,445 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-angle-20250707_161351_wps滚动文档.dat
2025-07-08 19:45:12,939 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-angle-20250707_161351_wps滚动文档.dat.txt
2025-07-08 19:45:12,940 - INFO - 提取到 0 个event相关的callId
2025-07-08 19:45:12,940 - WARNING - 未找到event相关的callId: perfmon-angle-20250707_161351_wps滚动文档.dat
2025-07-08 19:45:12,940 - INFO - 处理进度: 13/30
2025-07-08 19:45:12,940 - INFO - 开始处理文件: perfmon-angle-20250707_161508_wps缩放文档.dat
2025-07-08 19:45:12,940 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-angle-20250707_161508_wps缩放文档.dat
2025-07-08 19:45:13,166 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-angle-20250707_161508_wps缩放文档.dat.txt
2025-07-08 19:45:13,166 - INFO - 提取到 0 个event相关的callId
2025-07-08 19:45:13,166 - WARNING - 未找到event相关的callId: perfmon-angle-20250707_161508_wps缩放文档.dat
2025-07-08 19:45:13,166 - INFO - 处理进度: 14/30
2025-07-08 19:45:13,166 - INFO - 开始处理文件: perfmon-raster-20250707_095208-wpp文档绘制.dat
2025-07-08 19:45:13,166 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-raster-20250707_095208-wpp文档绘制.dat
2025-07-08 19:45:13,188 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-raster-20250707_095208-wpp文档绘制.dat.txt
2025-07-08 19:45:13,189 - INFO - 提取到 0 个event相关的callId
2025-07-08 19:45:13,189 - WARNING - 未找到event相关的callId: perfmon-raster-20250707_095208-wpp文档绘制.dat
2025-07-08 19:45:13,189 - INFO - 处理进度: 15/30
2025-07-08 19:45:13,189 - INFO - 开始处理文件: perfmon-raster-20250707_101228-wpp旋转.dat
2025-07-08 19:45:13,189 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-raster-20250707_101228-wpp旋转.dat
2025-07-08 19:45:13,227 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-raster-20250707_101228-wpp旋转.dat.txt
2025-07-08 19:45:13,227 - INFO - 提取到 0 个event相关的callId
2025-07-08 19:45:13,227 - WARNING - 未找到event相关的callId: perfmon-raster-20250707_101228-wpp旋转.dat
2025-07-08 19:45:13,227 - INFO - 处理进度: 16/30
2025-07-08 19:45:13,227 - INFO - 开始处理文件: perfmon-raster-20250707_101348_wpp缩放.dat
2025-07-08 19:45:13,227 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-raster-20250707_101348_wpp缩放.dat
2025-07-08 19:45:13,241 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-raster-20250707_101348_wpp缩放.dat.txt
2025-07-08 19:45:13,241 - INFO - 提取到 0 个event相关的callId
2025-07-08 19:45:13,241 - WARNING - 未找到event相关的callId: perfmon-raster-20250707_101348_wpp缩放.dat
2025-07-08 19:45:13,241 - INFO - 处理进度: 17/30
2025-07-08 19:45:13,242 - INFO - 开始处理文件: perfmon-raster-20250707_101933_wpp切换页面.dat
2025-07-08 19:45:13,242 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-raster-20250707_101933_wpp切换页面.dat
2025-07-08 19:45:13,258 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-raster-20250707_101933_wpp切换页面.dat.txt
2025-07-08 19:45:13,258 - INFO - 提取到 0 个event相关的callId
2025-07-08 19:45:13,258 - WARNING - 未找到event相关的callId: perfmon-raster-20250707_101933_wpp切换页面.dat
2025-07-08 19:45:13,258 - INFO - 处理进度: 18/30
2025-07-08 19:45:13,259 - INFO - 开始处理文件: perfmon-raster-20250707_102233_wpp缩略图生成.dat
2025-07-08 19:45:13,259 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-raster-20250707_102233_wpp缩略图生成.dat
2025-07-08 19:45:13,273 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-raster-20250707_102233_wpp缩略图生成.dat.txt
2025-07-08 19:45:13,273 - INFO - 提取到 0 个event相关的callId
2025-07-08 19:45:13,273 - WARNING - 未找到event相关的callId: perfmon-raster-20250707_102233_wpp缩略图生成.dat
2025-07-08 19:45:13,273 - INFO - 处理进度: 19/30
2025-07-08 19:45:13,273 - INFO - 开始处理文件: perfmon-raster-20250707_102721_wpp动画播放对象动画.dat
2025-07-08 19:45:13,274 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-raster-20250707_102721_wpp动画播放对象动画.dat
2025-07-08 19:45:13,292 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-raster-20250707_102721_wpp动画播放对象动画.dat.txt
2025-07-08 19:45:13,292 - INFO - 提取到 0 个event相关的callId
2025-07-08 19:45:13,292 - WARNING - 未找到event相关的callId: perfmon-raster-20250707_102721_wpp动画播放对象动画.dat
2025-07-08 19:45:13,292 - INFO - 处理进度: 20/30
2025-07-08 19:45:13,292 - INFO - 开始处理文件: perfmon-raster-20250707_103507_wpp动画页切换.dat
2025-07-08 19:45:13,292 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-raster-20250707_103507_wpp动画页切换.dat
2025-07-08 19:45:13,313 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-raster-20250707_103507_wpp动画页切换.dat.txt
2025-07-08 19:45:13,313 - INFO - 提取到 0 个event相关的callId
2025-07-08 19:45:13,313 - WARNING - 未找到event相关的callId: perfmon-raster-20250707_103507_wpp动画页切换.dat
2025-07-08 19:45:13,313 - INFO - 处理进度: 21/30
2025-07-08 19:45:13,313 - INFO - 开始处理文件: perfmon-raster-20250707_103924_wpp动画测试荧光笔.dat
2025-07-08 19:45:13,313 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-raster-20250707_103924_wpp动画测试荧光笔.dat
2025-07-08 19:45:13,329 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-raster-20250707_103924_wpp动画测试荧光笔.dat.txt
2025-07-08 19:45:13,329 - INFO - 提取到 0 个event相关的callId
2025-07-08 19:45:13,330 - WARNING - 未找到event相关的callId: perfmon-raster-20250707_103924_wpp动画测试荧光笔.dat
2025-07-08 19:45:13,330 - INFO - 处理进度: 22/30
2025-07-08 19:45:13,330 - INFO - 开始处理文件: perfmon-raster-20250707_155912_wps文档绘制.dat
2025-07-08 19:45:13,330 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-raster-20250707_155912_wps文档绘制.dat
2025-07-08 19:45:13,345 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-raster-20250707_155912_wps文档绘制.dat.txt
2025-07-08 19:45:13,345 - INFO - 提取到 0 个event相关的callId
2025-07-08 19:45:13,345 - WARNING - 未找到event相关的callId: perfmon-raster-20250707_155912_wps文档绘制.dat
2025-07-08 19:45:13,345 - INFO - 处理进度: 23/30
2025-07-08 19:45:13,345 - INFO - 开始处理文件: perfmon-raster-20250707_160622_wps块选.dat
2025-07-08 19:45:13,346 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-raster-20250707_160622_wps块选.dat
2025-07-08 19:45:13,367 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-raster-20250707_160622_wps块选.dat.txt
2025-07-08 19:45:13,367 - INFO - 提取到 0 个event相关的callId
2025-07-08 19:45:13,367 - WARNING - 未找到event相关的callId: perfmon-raster-20250707_160622_wps块选.dat
2025-07-08 19:45:13,367 - INFO - 处理进度: 24/30
2025-07-08 19:45:13,368 - INFO - 开始处理文件: perfmon-raster-20250707_161004_wps插入.dat
2025-07-08 19:45:13,368 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-raster-20250707_161004_wps插入.dat
2025-07-08 19:45:13,405 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-raster-20250707_161004_wps插入.dat.txt
2025-07-08 19:45:13,405 - INFO - 提取到 0 个event相关的callId
2025-07-08 19:45:13,405 - WARNING - 未找到event相关的callId: perfmon-raster-20250707_161004_wps插入.dat
2025-07-08 19:45:13,405 - INFO - 处理进度: 25/30
2025-07-08 19:45:13,406 - INFO - 开始处理文件: perfmon-raster-20250707_161420_wps滚动文档.dat
2025-07-08 19:45:13,406 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-raster-20250707_161420_wps滚动文档.dat
2025-07-08 19:45:13,472 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-raster-20250707_161420_wps滚动文档.dat.txt
2025-07-08 19:45:13,472 - INFO - 提取到 0 个event相关的callId
2025-07-08 19:45:13,472 - WARNING - 未找到event相关的callId: perfmon-raster-20250707_161420_wps滚动文档.dat
2025-07-08 19:45:13,472 - INFO - 处理进度: 26/30
2025-07-08 19:45:13,472 - INFO - 开始处理文件: perfmon-raster-20250707_161533_wps缩放文档.dat
2025-07-08 19:45:13,472 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-raster-20250707_161533_wps缩放文档.dat
2025-07-08 19:45:13,500 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-raster-20250707_161533_wps缩放文档.dat.txt
2025-07-08 19:45:13,500 - INFO - 提取到 0 个event相关的callId
2025-07-08 19:45:13,500 - WARNING - 未找到event相关的callId: perfmon-raster-20250707_161533_wps缩放文档.dat
2025-07-08 19:45:13,500 - INFO - 处理进度: 27/30
2025-07-08 19:45:13,500 - INFO - 开始处理文件: perfmon首页angle.dat
2025-07-08 19:45:13,500 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon首页angle.dat
2025-07-08 19:45:13,519 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon首页angle.dat.txt
2025-07-08 19:45:13,519 - INFO - 提取到 0 个event相关的callId
2025-07-08 19:45:13,519 - WARNING - 未找到event相关的callId: perfmon首页angle.dat
2025-07-08 19:45:13,520 - INFO - 处理进度: 28/30
2025-07-08 19:45:13,520 - INFO - 开始处理文件: perfmon首页raster.dat
2025-07-08 19:45:13,520 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon首页raster.dat
2025-07-08 19:45:13,530 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon首页raster.dat.txt
2025-07-08 19:45:13,530 - INFO - 提取到 0 个event相关的callId
2025-07-08 19:45:13,531 - WARNING - 未找到event相关的callId: perfmon首页raster.dat
2025-07-08 19:45:13,531 - INFO - 处理进度: 29/30
2025-07-08 19:45:13,531 - INFO - 开始处理文件: perfmon首页热启动angle.dat
2025-07-08 19:45:13,531 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon首页热启动angle.dat
2025-07-08 19:45:13,579 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon首页热启动angle.dat.txt
2025-07-08 19:45:13,579 - INFO - 提取到 0 个event相关的callId
2025-07-08 19:45:13,579 - WARNING - 未找到event相关的callId: perfmon首页热启动angle.dat
2025-07-08 19:45:13,579 - INFO - 处理进度: 30/30
2025-07-08 19:45:13,579 - INFO - 开始处理文件: perfmon首页热启动raster.dat
2025-07-08 19:45:13,579 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon首页热启动raster.dat
2025-07-08 19:45:13,593 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon首页热启动raster.dat.txt
2025-07-08 19:45:13,593 - INFO - 提取到 0 个event相关的callId
2025-07-08 19:45:13,593 - WARNING - 未找到event相关的callId: perfmon首页热启动raster.dat
2025-07-08 19:45:13,593 - INFO - 性能数据分析完成
2025-07-08 19:45:57,076 - INFO - 环境变量验证通过:
2025-07-08 19:45:57,076 - INFO -   VIEWER_DIR: D:\feature_0603\debug\wps_build\WPSOffice\office6
2025-07-08 19:45:57,076 - INFO -   DAT_SOURCE_DIR: C:\Users\<USER>\Desktop\ANALYZE\src_dat
2025-07-08 19:45:57,076 - INFO -   DEST_DIR: C:\Users\<USER>\Desktop\ANALYZE\output
2025-07-08 19:45:57,076 - INFO -   TOP_NUM: 40
2025-07-08 19:45:57,076 - INFO - 创建输出目录: C:\Users\<USER>\Desktop\ANALYZE\output\stats
2025-07-08 19:45:57,076 - INFO - 创建输出目录: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse
2025-07-08 19:45:57,076 - INFO - 提取到 0 个event相关的callId
2025-07-08 19:46:27,391 - INFO - 环境变量验证通过:
2025-07-08 19:46:27,391 - INFO -   VIEWER_DIR: D:\feature_0603\debug\wps_build\WPSOffice\office6
2025-07-08 19:46:27,391 - INFO -   DAT_SOURCE_DIR: C:\Users\<USER>\Desktop\ANALYZE\src_dat
2025-07-08 19:46:27,392 - INFO -   DEST_DIR: C:\Users\<USER>\Desktop\ANALYZE\output
2025-07-08 19:46:27,392 - INFO -   TOP_NUM: 40
2025-07-08 19:46:27,392 - INFO - 创建输出目录: C:\Users\<USER>\Desktop\ANALYZE\output\stats
2025-07-08 19:46:27,392 - INFO - 创建输出目录: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse
2025-07-08 19:46:27,392 - INFO - 找到event函数: E:\wpsskia\qt\source\qtbase\src\widgets\kernel\qwidget.cpp:9682 event
2025-07-08 19:46:27,392 - INFO - 提取callId: 1652, callTime: 29.603594ms
2025-07-08 19:46:27,392 - INFO - 提取callId: 7611, callTime: 19.396407ms
2025-07-08 19:46:27,393 - INFO - 提取callId: 3194, callTime: 18.933281ms
2025-07-08 19:46:27,393 - INFO - 提取callId: 5638, callTime: 10.284688ms
2025-07-08 19:46:27,393 - INFO - 提取callId: 11048, callTime: 6.920782ms
2025-07-08 19:46:27,393 - INFO - 提取callId: 8932, callTime: 6.484844ms
2025-07-08 19:46:27,393 - INFO - 提取callId: 10100, callTime: 4.2475ms
2025-07-08 19:46:27,393 - INFO - 提取callId: 8779, callTime: 2.310156ms
2025-07-08 19:46:27,393 - INFO - 提取callId: 47, callTime: 0.002188ms
2025-07-08 19:46:27,393 - INFO - 提取callId: 50, callTime: 0.000938ms
2025-07-08 19:46:27,393 - INFO - 提取到 10 个event相关的callId
2025-07-08 19:46:33,040 - INFO - 环境变量验证通过:
2025-07-08 19:46:33,041 - INFO -   VIEWER_DIR: D:\feature_0603\debug\wps_build\WPSOffice\office6
2025-07-08 19:46:33,041 - INFO -   DAT_SOURCE_DIR: C:\Users\<USER>\Desktop\ANALYZE\src_dat
2025-07-08 19:46:33,041 - INFO -   DEST_DIR: C:\Users\<USER>\Desktop\ANALYZE\output
2025-07-08 19:46:33,041 - INFO -   TOP_NUM: 40
2025-07-08 19:46:33,041 - INFO - 创建输出目录: C:\Users\<USER>\Desktop\ANALYZE\output\stats
2025-07-08 19:46:33,041 - INFO - 创建输出目录: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse
2025-07-08 19:46:33,041 - INFO - 开始性能数据分析
2025-07-08 19:46:33,042 - INFO - 找到 30 个.dat文件
2025-07-08 19:46:33,042 - INFO - 处理进度: 1/30
2025-07-08 19:46:33,042 - INFO - 开始处理文件: perfmon-angle-20250707_095219-wpp文档绘制.dat
2025-07-08 19:46:33,042 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-angle-20250707_095219-wpp文档绘制.dat
2025-07-08 19:46:33,090 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-angle-20250707_095219-wpp文档绘制.dat.txt
2025-07-08 19:46:33,090 - INFO - 找到event函数: E:\wpsskia\qt\source\qtbase\src\widgets\kernel\qwidget.cpp:9682 event
2025-07-08 19:46:33,090 - INFO - 提取callId: 21776, callTime: 29.943282ms
2025-07-08 19:46:33,090 - INFO - 提取callId: 4075, callTime: 28.224688ms
2025-07-08 19:46:33,090 - INFO - 提取callId: 12787, callTime: 28.138437ms
2025-07-08 19:46:33,090 - INFO - 提取callId: 1642, callTime: 28.05875ms
2025-07-08 19:46:33,090 - INFO - 提取callId: 37605, callTime: 17.621875ms
2025-07-08 19:46:33,090 - INFO - 提取callId: 25490, callTime: 15.752031ms
2025-07-08 19:46:33,090 - INFO - 提取callId: 10697, callTime: 11.7525ms
2025-07-08 19:46:33,090 - INFO - 提取callId: 8195, callTime: 10.756719ms
2025-07-08 19:46:33,090 - INFO - 提取callId: 6306, callTime: 9.559687ms
2025-07-08 19:46:33,091 - INFO - 提取callId: 24569, callTime: 9.542657ms
2025-07-08 19:46:33,091 - INFO - 提取callId: 28222, callTime: 9.159844ms
2025-07-08 19:46:33,091 - INFO - 提取callId: 26685, callTime: 8.270937ms
2025-07-08 19:46:33,091 - INFO - 提取callId: 9748, callTime: 8.268125ms
2025-07-08 19:46:33,091 - INFO - 提取callId: 37888, callTime: 8.001719ms
2025-07-08 19:46:33,091 - INFO - 提取callId: 33163, callTime: 7.5425ms
2025-07-08 19:46:33,091 - INFO - 提取callId: 42924, callTime: 7.052188ms
2025-07-08 19:46:33,091 - INFO - 提取callId: 41288, callTime: 6.884218ms
2025-07-08 19:46:33,091 - INFO - 提取callId: 43156, callTime: 6.735ms
2025-07-08 19:46:33,091 - INFO - 提取callId: 34233, callTime: 6.624531ms
2025-07-08 19:46:33,091 - INFO - 提取callId: 43468, callTime: 6.301406ms
2025-07-08 19:46:33,091 - INFO - 提取callId: 43936, callTime: 6.2325ms
2025-07-08 19:46:33,091 - INFO - 提取callId: 37732, callTime: 6.199531ms
2025-07-08 19:46:33,091 - INFO - 提取callId: 41444, callTime: 6.191093ms
2025-07-08 19:46:33,091 - INFO - 提取callId: 41132, callTime: 6.039063ms
2025-07-08 19:46:33,092 - INFO - 提取callId: 43624, callTime: 6.002187ms
2025-07-08 19:46:33,092 - INFO - 提取callId: 39728, callTime: 5.921719ms
2025-07-08 19:46:33,092 - INFO - 提取callId: 40664, callTime: 5.901406ms
2025-07-08 19:46:33,092 - INFO - 提取callId: 39416, callTime: 5.875469ms
2025-07-08 19:46:33,092 - INFO - 提取callId: 44716, callTime: 5.869531ms
2025-07-08 19:46:33,092 - INFO - 提取callId: 44248, callTime: 5.776875ms
2025-07-08 19:46:33,092 - INFO - 提取callId: 44404, callTime: 5.752813ms
2025-07-08 19:46:33,092 - INFO - 提取callId: 31470, callTime: 5.739688ms
2025-07-08 19:46:33,092 - INFO - 提取callId: 42768, callTime: 5.636562ms
2025-07-08 19:46:33,092 - INFO - 提取callId: 39260, callTime: 5.625312ms
2025-07-08 19:46:33,092 - INFO - 提取callId: 39572, callTime: 5.582344ms
2025-07-08 19:46:33,092 - INFO - 提取callId: 31977, callTime: 5.574219ms
2025-07-08 19:46:33,092 - INFO - 提取callId: 41600, callTime: 5.415938ms
2025-07-08 19:46:33,092 - INFO - 提取callId: 42612, callTime: 5.365ms
2025-07-08 19:46:33,093 - INFO - 提取callId: 38848, callTime: 5.362968ms
2025-07-08 19:46:33,093 - INFO - 提取callId: 43312, callTime: 5.355937ms
2025-07-08 19:46:33,093 - INFO - 提取到 40 个event相关的callId
2025-07-08 19:46:33,093 - INFO - 处理 callId=21776, callTime=29.943282ms, rank=1
2025-07-08 19:46:33,093 - INFO - 执行详细分析: callId=21776
2025-07-08 19:46:33,225 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\1-21776.txt
2025-07-08 19:46:33,225 - INFO - 处理 callId=4075, callTime=28.224688ms, rank=2
2025-07-08 19:46:33,225 - INFO - 执行详细分析: callId=4075
2025-07-08 19:46:33,287 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\2-4075.txt
2025-07-08 19:46:33,287 - INFO - 处理 callId=12787, callTime=28.138437ms, rank=3
2025-07-08 19:46:33,287 - INFO - 执行详细分析: callId=12787
2025-07-08 19:46:33,552 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\3-12787.txt
2025-07-08 19:46:33,552 - INFO - 处理 callId=1642, callTime=28.05875ms, rank=4
2025-07-08 19:46:33,552 - INFO - 执行详细分析: callId=1642
2025-07-08 19:46:33,621 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\4-1642.txt
2025-07-08 19:46:33,621 - INFO - 处理 callId=37605, callTime=17.621875ms, rank=5
2025-07-08 19:46:33,621 - INFO - 执行详细分析: callId=37605
2025-07-08 19:46:33,668 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\5-37605.txt
2025-07-08 19:46:33,668 - INFO - 处理 callId=25490, callTime=15.752031ms, rank=6
2025-07-08 19:46:33,668 - INFO - 执行详细分析: callId=25490
2025-07-08 19:46:33,725 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\6-25490.txt
2025-07-08 19:46:33,725 - INFO - 处理 callId=10697, callTime=11.7525ms, rank=7
2025-07-08 19:46:33,725 - INFO - 执行详细分析: callId=10697
2025-07-08 19:46:33,743 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\7-10697.txt
2025-07-08 19:46:33,743 - INFO - 处理 callId=8195, callTime=10.756719ms, rank=8
2025-07-08 19:46:33,743 - INFO - 执行详细分析: callId=8195
2025-07-08 19:46:33,791 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\8-8195.txt
2025-07-08 19:46:33,791 - INFO - 处理 callId=6306, callTime=9.559687ms, rank=9
2025-07-08 19:46:33,791 - INFO - 执行详细分析: callId=6306
2025-07-08 19:46:33,851 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\9-6306.txt
2025-07-08 19:46:33,851 - INFO - 处理 callId=24569, callTime=9.542657ms, rank=10
2025-07-08 19:46:33,851 - INFO - 执行详细分析: callId=24569
2025-07-08 19:46:33,895 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\10-24569.txt
2025-07-08 19:46:33,896 - INFO - 处理 callId=28222, callTime=9.159844ms, rank=11
2025-07-08 19:46:33,896 - INFO - 执行详细分析: callId=28222
2025-07-08 19:46:33,938 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\11-28222.txt
2025-07-08 19:46:33,938 - INFO - 处理 callId=26685, callTime=8.270937ms, rank=12
2025-07-08 19:46:33,938 - INFO - 执行详细分析: callId=26685
2025-07-08 19:46:34,009 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\12-26685.txt
2025-07-08 19:46:34,009 - INFO - 处理 callId=9748, callTime=8.268125ms, rank=13
2025-07-08 19:46:34,009 - INFO - 执行详细分析: callId=9748
2025-07-08 19:46:34,046 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\13-9748.txt
2025-07-08 19:46:34,047 - INFO - 处理 callId=37888, callTime=8.001719ms, rank=14
2025-07-08 19:46:34,047 - INFO - 执行详细分析: callId=37888
2025-07-08 19:46:34,085 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\14-37888.txt
2025-07-08 19:46:34,085 - INFO - 处理 callId=33163, callTime=7.5425ms, rank=15
2025-07-08 19:46:34,085 - INFO - 执行详细分析: callId=33163
2025-07-08 19:46:34,142 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\15-33163.txt
2025-07-08 19:46:34,142 - INFO - 处理 callId=42924, callTime=7.052188ms, rank=16
2025-07-08 19:46:34,142 - INFO - 执行详细分析: callId=42924
2025-07-08 19:46:34,184 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\16-42924.txt
2025-07-08 19:46:34,184 - INFO - 处理 callId=41288, callTime=6.884218ms, rank=17
2025-07-08 19:46:34,185 - INFO - 执行详细分析: callId=41288
2025-07-08 19:46:34,225 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\17-41288.txt
2025-07-08 19:46:34,225 - INFO - 处理 callId=43156, callTime=6.735ms, rank=18
2025-07-08 19:46:34,226 - INFO - 执行详细分析: callId=43156
2025-07-08 19:46:34,268 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\18-43156.txt
2025-07-08 19:46:34,268 - INFO - 处理 callId=34233, callTime=6.624531ms, rank=19
2025-07-08 19:46:34,268 - INFO - 执行详细分析: callId=34233
2025-07-08 19:46:34,332 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\19-34233.txt
2025-07-08 19:46:34,332 - INFO - 处理 callId=43468, callTime=6.301406ms, rank=20
2025-07-08 19:46:34,332 - INFO - 执行详细分析: callId=43468
2025-07-08 19:46:34,374 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\20-43468.txt
2025-07-08 19:46:34,374 - INFO - 处理 callId=43936, callTime=6.2325ms, rank=21
2025-07-08 19:46:34,374 - INFO - 执行详细分析: callId=43936
2025-07-08 19:46:34,416 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\21-43936.txt
2025-07-08 19:46:34,416 - INFO - 处理 callId=37732, callTime=6.199531ms, rank=22
2025-07-08 19:46:34,416 - INFO - 执行详细分析: callId=37732
2025-07-08 19:46:34,455 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\22-37732.txt
2025-07-08 19:46:34,455 - INFO - 处理 callId=41444, callTime=6.191093ms, rank=23
2025-07-08 19:46:34,455 - INFO - 执行详细分析: callId=41444
2025-07-08 19:46:34,496 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\23-41444.txt
2025-07-08 19:46:34,496 - INFO - 处理 callId=41132, callTime=6.039063ms, rank=24
2025-07-08 19:46:34,496 - INFO - 执行详细分析: callId=41132
2025-07-08 19:46:34,537 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\24-41132.txt
2025-07-08 19:46:34,538 - INFO - 处理 callId=43624, callTime=6.002187ms, rank=25
2025-07-08 19:46:34,538 - INFO - 执行详细分析: callId=43624
2025-07-08 19:46:34,579 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\25-43624.txt
2025-07-08 19:46:34,579 - INFO - 处理 callId=39728, callTime=5.921719ms, rank=26
2025-07-08 19:46:34,580 - INFO - 执行详细分析: callId=39728
2025-07-08 19:46:34,619 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\26-39728.txt
2025-07-08 19:46:34,619 - INFO - 处理 callId=40664, callTime=5.901406ms, rank=27
2025-07-08 19:46:34,619 - INFO - 执行详细分析: callId=40664
2025-07-08 19:46:34,660 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\27-40664.txt
2025-07-08 19:46:34,660 - INFO - 处理 callId=39416, callTime=5.875469ms, rank=28
2025-07-08 19:46:34,660 - INFO - 执行详细分析: callId=39416
2025-07-08 19:46:34,701 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\28-39416.txt
2025-07-08 19:46:34,701 - INFO - 处理 callId=44716, callTime=5.869531ms, rank=29
2025-07-08 19:46:34,701 - INFO - 执行详细分析: callId=44716
2025-07-08 19:46:34,744 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\29-44716.txt
2025-07-08 19:46:34,744 - INFO - 处理 callId=44248, callTime=5.776875ms, rank=30
2025-07-08 19:46:34,744 - INFO - 执行详细分析: callId=44248
2025-07-08 19:46:34,787 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\30-44248.txt
2025-07-08 19:46:34,787 - INFO - 处理 callId=44404, callTime=5.752813ms, rank=31
2025-07-08 19:46:34,787 - INFO - 执行详细分析: callId=44404
2025-07-08 19:46:34,829 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\31-44404.txt
2025-07-08 19:46:34,829 - INFO - 处理 callId=31470, callTime=5.739688ms, rank=32
2025-07-08 19:46:34,829 - INFO - 执行详细分析: callId=31470
2025-07-08 19:46:34,863 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\32-31470.txt
2025-07-08 19:46:34,863 - INFO - 处理 callId=42768, callTime=5.636562ms, rank=33
2025-07-08 19:46:34,863 - INFO - 执行详细分析: callId=42768
2025-07-08 19:46:34,905 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\33-42768.txt
2025-07-08 19:46:34,905 - INFO - 处理 callId=39260, callTime=5.625312ms, rank=34
2025-07-08 19:46:34,905 - INFO - 执行详细分析: callId=39260
2025-07-08 19:46:34,945 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\34-39260.txt
2025-07-08 19:46:34,945 - INFO - 处理 callId=39572, callTime=5.582344ms, rank=35
2025-07-08 19:46:34,945 - INFO - 执行详细分析: callId=39572
2025-07-08 19:46:34,985 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\35-39572.txt
2025-07-08 19:46:34,985 - INFO - 处理 callId=31977, callTime=5.574219ms, rank=36
2025-07-08 19:46:34,985 - INFO - 执行详细分析: callId=31977
2025-07-08 19:46:35,019 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\36-31977.txt
2025-07-08 19:46:35,019 - INFO - 处理 callId=41600, callTime=5.415938ms, rank=37
2025-07-08 19:46:35,019 - INFO - 执行详细分析: callId=41600
2025-07-08 19:46:35,060 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\37-41600.txt
2025-07-08 19:46:35,061 - INFO - 处理 callId=42612, callTime=5.365ms, rank=38
2025-07-08 19:46:35,061 - INFO - 执行详细分析: callId=42612
2025-07-08 19:46:35,102 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\38-42612.txt
2025-07-08 19:46:35,103 - INFO - 处理 callId=38848, callTime=5.362968ms, rank=39
2025-07-08 19:46:35,103 - INFO - 执行详细分析: callId=38848
2025-07-08 19:46:35,142 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\39-38848.txt
2025-07-08 19:46:35,142 - INFO - 处理 callId=43312, callTime=5.355937ms, rank=40
2025-07-08 19:46:35,142 - INFO - 执行详细分析: callId=43312
2025-07-08 19:46:35,184 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_095219-wpp文档绘制\40-43312.txt
2025-07-08 19:46:35,184 - INFO - 完成处理文件: perfmon-angle-20250707_095219-wpp文档绘制.dat
2025-07-08 19:46:35,184 - INFO - 处理进度: 2/30
2025-07-08 19:46:35,184 - INFO - 开始处理文件: perfmon-angle-20250707_101003-wpp旋转.dat
2025-07-08 19:46:35,184 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-angle-20250707_101003-wpp旋转.dat
2025-07-08 19:46:35,462 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-angle-20250707_101003-wpp旋转.dat.txt
2025-07-08 19:46:35,463 - INFO - 找到event函数: E:\wpsskia\qt\source\qtbase\src\widgets\kernel\qwidget.cpp:9682 event
2025-07-08 19:46:35,463 - INFO - 提取callId: 53447, callTime: 104.65375ms
2025-07-08 19:46:35,463 - INFO - 提取callId: 48735, callTime: 54.710625ms
2025-07-08 19:46:35,463 - INFO - 提取callId: 250674, callTime: 38.155313ms
2025-07-08 19:46:35,463 - INFO - 提取callId: 14210, callTime: 31.375781ms
2025-07-08 19:46:35,463 - INFO - 提取callId: 23192, callTime: 28.365938ms
2025-07-08 19:46:35,463 - INFO - 提取callId: 1642, callTime: 27.635469ms
2025-07-08 19:46:35,463 - INFO - 提取callId: 253563, callTime: 22.61625ms
2025-07-08 19:46:35,463 - INFO - 提取callId: 9748, callTime: 21.416875ms
2025-07-08 19:46:35,463 - INFO - 提取callId: 256191, callTime: 21.329218ms
2025-07-08 19:46:35,463 - INFO - 提取callId: 26903, callTime: 15.118125ms
2025-07-08 19:46:35,463 - INFO - 提取callId: 73387, callTime: 14.854063ms
2025-07-08 19:46:35,463 - INFO - 提取callId: 6306, callTime: 14.277969ms
2025-07-08 19:46:35,464 - INFO - 提取callId: 252174, callTime: 13.555937ms
2025-07-08 19:46:35,464 - INFO - 提取callId: 51204, callTime: 12.106562ms
2025-07-08 19:46:35,464 - INFO - 提取callId: 4075, callTime: 11.790156ms
2025-07-08 19:46:35,464 - INFO - 提取callId: 46530, callTime: 9.04ms
2025-07-08 19:46:35,464 - INFO - 提取callId: 38484, callTime: 8.811562ms
2025-07-08 19:46:35,464 - INFO - 提取callId: 28098, callTime: 8.804219ms
2025-07-08 19:46:35,464 - INFO - 提取callId: 12472, callTime: 8.552656ms
2025-07-08 19:46:35,464 - INFO - 提取callId: 38792, callTime: 8.528125ms
2025-07-08 19:46:35,464 - INFO - 提取callId: 44680, callTime: 8.417031ms
2025-07-08 19:46:35,464 - INFO - 提取callId: 52913, callTime: 8.337344ms
2025-07-08 19:46:35,464 - INFO - 提取callId: 40284, callTime: 8.187968ms
2025-07-08 19:46:35,464 - INFO - 提取callId: 34643, callTime: 7.950937ms
2025-07-08 19:46:35,464 - INFO - 提取callId: 39560, callTime: 7.931562ms
2025-07-08 19:46:35,464 - INFO - 提取callId: 39368, callTime: 7.892343ms
2025-07-08 19:46:35,464 - INFO - 提取callId: 45643, callTime: 7.61875ms
2025-07-08 19:46:35,465 - INFO - 提取callId: 38984, callTime: 7.5675ms
2025-07-08 19:46:35,465 - INFO - 提取callId: 45236, callTime: 7.503906ms
2025-07-08 19:46:35,465 - INFO - 提取callId: 29635, callTime: 7.502812ms
2025-07-08 19:46:35,465 - INFO - 提取callId: 252783, callTime: 7.377187ms
2025-07-08 19:46:35,465 - INFO - 提取callId: 32791, callTime: 7.180156ms
2025-07-08 19:46:35,465 - INFO - 提取callId: 46330, callTime: 7.125313ms
2025-07-08 19:46:35,465 - INFO - 提取callId: 8195, callTime: 7.125ms
2025-07-08 19:46:35,465 - INFO - 提取callId: 40440, callTime: 7.046094ms
2025-07-08 19:46:35,465 - INFO - 提取callId: 42000, callTime: 7.013125ms
2025-07-08 19:46:35,465 - INFO - 提取callId: 45443, callTime: 6.949843ms
2025-07-08 19:46:35,465 - INFO - 提取callId: 46730, callTime: 6.948438ms
2025-07-08 19:46:35,465 - INFO - 提取callId: 42312, callTime: 6.900782ms
2025-07-08 19:46:35,465 - INFO - 提取callId: 46174, callTime: 6.865625ms
2025-07-08 19:46:35,465 - INFO - 提取到 40 个event相关的callId
2025-07-08 19:46:35,466 - INFO - 处理 callId=53447, callTime=104.65375ms, rank=1
2025-07-08 19:46:35,466 - INFO - 执行详细分析: callId=53447
2025-07-08 19:46:35,563 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\1-53447.txt
2025-07-08 19:46:35,563 - INFO - 处理 callId=48735, callTime=54.710625ms, rank=2
2025-07-08 19:46:35,563 - INFO - 执行详细分析: callId=48735
2025-07-08 19:46:35,621 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\2-48735.txt
2025-07-08 19:46:35,621 - INFO - 处理 callId=250674, callTime=38.155313ms, rank=3
2025-07-08 19:46:35,621 - INFO - 执行详细分析: callId=250674
2025-07-08 19:46:35,850 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\3-250674.txt
2025-07-08 19:46:35,850 - INFO - 处理 callId=14210, callTime=31.375781ms, rank=4
2025-07-08 19:46:35,851 - INFO - 执行详细分析: callId=14210
2025-07-08 19:46:36,121 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\4-14210.txt
2025-07-08 19:46:36,122 - INFO - 处理 callId=23192, callTime=28.365938ms, rank=5
2025-07-08 19:46:36,122 - INFO - 执行详细分析: callId=23192
2025-07-08 19:46:36,260 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\5-23192.txt
2025-07-08 19:46:36,260 - INFO - 处理 callId=1642, callTime=27.635469ms, rank=6
2025-07-08 19:46:36,260 - INFO - 执行详细分析: callId=1642
2025-07-08 19:46:36,333 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\6-1642.txt
2025-07-08 19:46:36,333 - INFO - 处理 callId=253563, callTime=22.61625ms, rank=7
2025-07-08 19:46:36,333 - INFO - 执行详细分析: callId=253563
2025-07-08 19:46:36,589 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\7-253563.txt
2025-07-08 19:46:36,589 - INFO - 处理 callId=9748, callTime=21.416875ms, rank=8
2025-07-08 19:46:36,589 - INFO - 执行详细分析: callId=9748
2025-07-08 19:46:36,625 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\8-9748.txt
2025-07-08 19:46:36,626 - INFO - 处理 callId=256191, callTime=21.329218ms, rank=9
2025-07-08 19:46:36,626 - INFO - 执行详细分析: callId=256191
2025-07-08 19:46:37,049 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\9-256191.txt
2025-07-08 19:46:37,050 - INFO - 处理 callId=26903, callTime=15.118125ms, rank=10
2025-07-08 19:46:37,050 - INFO - 执行详细分析: callId=26903
2025-07-08 19:46:37,108 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\10-26903.txt
2025-07-08 19:46:37,108 - INFO - 处理 callId=73387, callTime=14.854063ms, rank=11
2025-07-08 19:46:37,108 - INFO - 执行详细分析: callId=73387
2025-07-08 19:46:37,176 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\11-73387.txt
2025-07-08 19:46:37,177 - INFO - 处理 callId=6306, callTime=14.277969ms, rank=12
2025-07-08 19:46:37,177 - INFO - 执行详细分析: callId=6306
2025-07-08 19:46:37,241 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\12-6306.txt
2025-07-08 19:46:37,242 - INFO - 处理 callId=252174, callTime=13.555937ms, rank=13
2025-07-08 19:46:37,242 - INFO - 执行详细分析: callId=252174
2025-07-08 19:46:37,435 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\13-252174.txt
2025-07-08 19:46:37,435 - INFO - 处理 callId=51204, callTime=12.106562ms, rank=14
2025-07-08 19:46:37,435 - INFO - 执行详细分析: callId=51204
2025-07-08 19:46:37,510 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\14-51204.txt
2025-07-08 19:46:37,510 - INFO - 处理 callId=4075, callTime=11.790156ms, rank=15
2025-07-08 19:46:37,510 - INFO - 执行详细分析: callId=4075
2025-07-08 19:46:37,576 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\15-4075.txt
2025-07-08 19:46:37,576 - INFO - 处理 callId=46530, callTime=9.04ms, rank=16
2025-07-08 19:46:37,576 - INFO - 执行详细分析: callId=46530
2025-07-08 19:46:37,625 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\16-46530.txt
2025-07-08 19:46:37,625 - INFO - 处理 callId=38484, callTime=8.811562ms, rank=17
2025-07-08 19:46:37,625 - INFO - 执行详细分析: callId=38484
2025-07-08 19:46:37,667 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\17-38484.txt
2025-07-08 19:46:37,667 - INFO - 处理 callId=28098, callTime=8.804219ms, rank=18
2025-07-08 19:46:37,667 - INFO - 执行详细分析: callId=28098
2025-07-08 19:46:37,743 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\18-28098.txt
2025-07-08 19:46:37,743 - INFO - 处理 callId=12472, callTime=8.552656ms, rank=19
2025-07-08 19:46:37,743 - INFO - 执行详细分析: callId=12472
2025-07-08 19:46:37,767 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\19-12472.txt
2025-07-08 19:46:37,767 - INFO - 处理 callId=38792, callTime=8.528125ms, rank=20
2025-07-08 19:46:37,767 - INFO - 执行详细分析: callId=38792
2025-07-08 19:46:37,810 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\20-38792.txt
2025-07-08 19:46:37,810 - INFO - 处理 callId=44680, callTime=8.417031ms, rank=21
2025-07-08 19:46:37,810 - INFO - 执行详细分析: callId=44680
2025-07-08 19:46:37,857 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\21-44680.txt
2025-07-08 19:46:37,857 - INFO - 处理 callId=52913, callTime=8.337344ms, rank=22
2025-07-08 19:46:37,857 - INFO - 执行详细分析: callId=52913
2025-07-08 19:46:37,913 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\22-52913.txt
2025-07-08 19:46:37,914 - INFO - 处理 callId=40284, callTime=8.187968ms, rank=23
2025-07-08 19:46:37,914 - INFO - 执行详细分析: callId=40284
2025-07-08 19:46:37,958 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\23-40284.txt
2025-07-08 19:46:37,958 - INFO - 处理 callId=34643, callTime=7.950937ms, rank=24
2025-07-08 19:46:37,958 - INFO - 执行详细分析: callId=34643
2025-07-08 19:46:38,019 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\24-34643.txt
2025-07-08 19:46:38,019 - INFO - 处理 callId=39560, callTime=7.931562ms, rank=25
2025-07-08 19:46:38,020 - INFO - 执行详细分析: callId=39560
2025-07-08 19:46:38,063 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\25-39560.txt
2025-07-08 19:46:38,063 - INFO - 处理 callId=39368, callTime=7.892343ms, rank=26
2025-07-08 19:46:38,063 - INFO - 执行详细分析: callId=39368
2025-07-08 19:46:38,108 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\26-39368.txt
2025-07-08 19:46:38,108 - INFO - 处理 callId=45643, callTime=7.61875ms, rank=27
2025-07-08 19:46:38,108 - INFO - 执行详细分析: callId=45643
2025-07-08 19:46:38,158 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\27-45643.txt
2025-07-08 19:46:38,158 - INFO - 处理 callId=38984, callTime=7.5675ms, rank=28
2025-07-08 19:46:38,158 - INFO - 执行详细分析: callId=38984
2025-07-08 19:46:38,202 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\28-38984.txt
2025-07-08 19:46:38,202 - INFO - 处理 callId=45236, callTime=7.503906ms, rank=29
2025-07-08 19:46:38,202 - INFO - 执行详细分析: callId=45236
2025-07-08 19:46:38,249 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\29-45236.txt
2025-07-08 19:46:38,249 - INFO - 处理 callId=29635, callTime=7.502812ms, rank=30
2025-07-08 19:46:38,249 - INFO - 执行详细分析: callId=29635
2025-07-08 19:46:38,298 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\30-29635.txt
2025-07-08 19:46:38,298 - INFO - 处理 callId=252783, callTime=7.377187ms, rank=31
2025-07-08 19:46:38,298 - INFO - 执行详细分析: callId=252783
2025-07-08 19:46:38,491 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\31-252783.txt
2025-07-08 19:46:38,491 - INFO - 处理 callId=32791, callTime=7.180156ms, rank=32
2025-07-08 19:46:38,491 - INFO - 执行详细分析: callId=32791
2025-07-08 19:46:38,529 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\32-32791.txt
2025-07-08 19:46:38,529 - INFO - 处理 callId=46330, callTime=7.125313ms, rank=33
2025-07-08 19:46:38,529 - INFO - 执行详细分析: callId=46330
2025-07-08 19:46:38,578 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\33-46330.txt
2025-07-08 19:46:38,578 - INFO - 处理 callId=8195, callTime=7.125ms, rank=34
2025-07-08 19:46:38,578 - INFO - 执行详细分析: callId=8195
2025-07-08 19:46:38,630 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\34-8195.txt
2025-07-08 19:46:38,630 - INFO - 处理 callId=40440, callTime=7.046094ms, rank=35
2025-07-08 19:46:38,630 - INFO - 执行详细分析: callId=40440
2025-07-08 19:46:38,683 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\35-40440.txt
2025-07-08 19:46:38,683 - INFO - 处理 callId=42000, callTime=7.013125ms, rank=36
2025-07-08 19:46:38,683 - INFO - 执行详细分析: callId=42000
2025-07-08 19:46:38,728 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\36-42000.txt
2025-07-08 19:46:38,728 - INFO - 处理 callId=45443, callTime=6.949843ms, rank=37
2025-07-08 19:46:38,728 - INFO - 执行详细分析: callId=45443
2025-07-08 19:46:38,776 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\37-45443.txt
2025-07-08 19:46:38,776 - INFO - 处理 callId=46730, callTime=6.948438ms, rank=38
2025-07-08 19:46:38,776 - INFO - 执行详细分析: callId=46730
2025-07-08 19:46:38,826 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\38-46730.txt
2025-07-08 19:46:38,826 - INFO - 处理 callId=42312, callTime=6.900782ms, rank=39
2025-07-08 19:46:38,826 - INFO - 执行详细分析: callId=42312
2025-07-08 19:46:38,871 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\39-42312.txt
2025-07-08 19:46:38,871 - INFO - 处理 callId=46174, callTime=6.865625ms, rank=40
2025-07-08 19:46:38,871 - INFO - 执行详细分析: callId=46174
2025-07-08 19:46:38,919 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101003-wpp旋转\40-46174.txt
2025-07-08 19:46:38,919 - INFO - 完成处理文件: perfmon-angle-20250707_101003-wpp旋转.dat
2025-07-08 19:46:38,919 - INFO - 处理进度: 3/30
2025-07-08 19:46:38,919 - INFO - 开始处理文件: perfmon-angle-20250707_101321_wpp缩放.dat
2025-07-08 19:46:38,919 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-angle-20250707_101321_wpp缩放.dat
2025-07-08 19:46:39,002 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-angle-20250707_101321_wpp缩放.dat.txt
2025-07-08 19:46:39,002 - INFO - 找到event函数: E:\wpsskia\qt\source\qtbase\src\widgets\kernel\qwidget.cpp:9682 event
2025-07-08 19:46:39,002 - INFO - 提取callId: 86500, callTime: 138.608281ms
2025-07-08 19:46:39,002 - INFO - 提取callId: 12459, callTime: 35.725ms
2025-07-08 19:46:39,002 - INFO - 提取callId: 21441, callTime: 30.538906ms
2025-07-08 19:46:39,002 - INFO - 提取callId: 4075, callTime: 28.869375ms
2025-07-08 19:46:39,002 - INFO - 提取callId: 9748, callTime: 28.708125ms
2025-07-08 19:46:39,002 - INFO - 提取callId: 1642, callTime: 28.00625ms
2025-07-08 19:46:39,002 - INFO - 提取callId: 37700, callTime: 21.172187ms
2025-07-08 19:46:39,002 - INFO - 提取callId: 25152, callTime: 15.522032ms
2025-07-08 19:46:39,003 - INFO - 提取callId: 53173, callTime: 13.244062ms
2025-07-08 19:46:39,003 - INFO - 提取callId: 86180, callTime: 12.923125ms
2025-07-08 19:46:39,003 - INFO - 提取callId: 79816, callTime: 12.267969ms
2025-07-08 19:46:39,003 - INFO - 提取callId: 46258, callTime: 11.938593ms
2025-07-08 19:46:39,003 - INFO - 提取callId: 77179, callTime: 11.48125ms
2025-07-08 19:46:39,003 - INFO - 提取callId: 53103, callTime: 11.237969ms
2025-07-08 19:46:39,003 - INFO - 提取callId: 49382, callTime: 10.471406ms
2025-07-08 19:46:39,003 - INFO - 提取callId: 53994, callTime: 10.000781ms
2025-07-08 19:46:39,003 - INFO - 提取callId: 84296, callTime: 9.927031ms
2025-07-08 19:46:39,003 - INFO - 提取callId: 83515, callTime: 9.805938ms
2025-07-08 19:46:39,003 - INFO - 提取callId: 8195, callTime: 9.802656ms
2025-07-08 19:46:39,003 - INFO - 提取callId: 58274, callTime: 9.47625ms
2025-07-08 19:46:39,003 - INFO - 提取callId: 46969, callTime: 9.303594ms
2025-07-08 19:46:39,003 - INFO - 提取callId: 54815, callTime: 8.807187ms
2025-07-08 19:46:39,003 - INFO - 提取callId: 6306, callTime: 8.717187ms
2025-07-08 19:46:39,003 - INFO - 提取callId: 38044, callTime: 8.485156ms
2025-07-08 19:46:39,003 - INFO - 提取callId: 48601, callTime: 8.463438ms
2025-07-08 19:46:39,004 - INFO - 提取callId: 52355, callTime: 8.461719ms
2025-07-08 19:46:39,004 - INFO - 提取callId: 58937, callTime: 8.458438ms
2025-07-08 19:46:39,004 - INFO - 提取callId: 50200, callTime: 8.219531ms
2025-07-08 19:46:39,004 - INFO - 提取callId: 71918, callTime: 8.207656ms
2025-07-08 19:46:39,004 - INFO - 提取callId: 51762, callTime: 8.195937ms
2025-07-08 19:46:39,004 - INFO - 提取callId: 41252, callTime: 8.182031ms
2025-07-08 19:46:39,004 - INFO - 提取callId: 74055, callTime: 8.135625ms
2025-07-08 19:46:39,004 - INFO - 提取callId: 83585, callTime: 8.038281ms
2025-07-08 19:46:39,004 - INFO - 提取callId: 72751, callTime: 8.012656ms
2025-07-08 19:46:39,004 - INFO - 提取callId: 40940, callTime: 7.934687ms
2025-07-08 19:46:39,004 - INFO - 提取callId: 81360, callTime: 7.900937ms
2025-07-08 19:46:39,004 - INFO - 提取callId: 27884, callTime: 7.704688ms
2025-07-08 19:46:39,004 - INFO - 提取callId: 38200, callTime: 7.693437ms
2025-07-08 19:46:39,004 - INFO - 提取callId: 31715, callTime: 7.66125ms
2025-07-08 19:46:39,004 - INFO - 提取callId: 26347, callTime: 7.649844ms
2025-07-08 19:46:39,005 - INFO - 提取到 40 个event相关的callId
2025-07-08 19:46:39,005 - INFO - 处理 callId=86500, callTime=138.608281ms, rank=1
2025-07-08 19:46:39,005 - INFO - 执行详细分析: callId=86500
2025-07-08 19:46:39,087 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\1-86500.txt
2025-07-08 19:46:39,087 - INFO - 处理 callId=12459, callTime=35.725ms, rank=2
2025-07-08 19:46:39,087 - INFO - 执行详细分析: callId=12459
2025-07-08 19:46:39,353 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\2-12459.txt
2025-07-08 19:46:39,354 - INFO - 处理 callId=21441, callTime=30.538906ms, rank=3
2025-07-08 19:46:39,354 - INFO - 执行详细分析: callId=21441
2025-07-08 19:46:39,487 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\3-21441.txt
2025-07-08 19:46:39,488 - INFO - 处理 callId=4075, callTime=28.869375ms, rank=4
2025-07-08 19:46:39,488 - INFO - 执行详细分析: callId=4075
2025-07-08 19:46:39,551 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\4-4075.txt
2025-07-08 19:46:39,551 - INFO - 处理 callId=9748, callTime=28.708125ms, rank=5
2025-07-08 19:46:39,551 - INFO - 执行详细分析: callId=9748
2025-07-08 19:46:39,592 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\5-9748.txt
2025-07-08 19:46:39,592 - INFO - 处理 callId=1642, callTime=28.00625ms, rank=6
2025-07-08 19:46:39,593 - INFO - 执行详细分析: callId=1642
2025-07-08 19:46:39,663 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\6-1642.txt
2025-07-08 19:46:39,663 - INFO - 处理 callId=37700, callTime=21.172187ms, rank=7
2025-07-08 19:46:39,663 - INFO - 执行详细分析: callId=37700
2025-07-08 19:46:39,701 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\7-37700.txt
2025-07-08 19:46:39,701 - INFO - 处理 callId=25152, callTime=15.522032ms, rank=8
2025-07-08 19:46:39,701 - INFO - 执行详细分析: callId=25152
2025-07-08 19:46:39,755 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\8-25152.txt
2025-07-08 19:46:39,755 - INFO - 处理 callId=53173, callTime=13.244062ms, rank=9
2025-07-08 19:46:39,755 - INFO - 执行详细分析: callId=53173
2025-07-08 19:46:39,814 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\9-53173.txt
2025-07-08 19:46:39,814 - INFO - 处理 callId=86180, callTime=12.923125ms, rank=10
2025-07-08 19:46:39,814 - INFO - 执行详细分析: callId=86180
2025-07-08 19:46:39,889 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\10-86180.txt
2025-07-08 19:46:39,889 - INFO - 处理 callId=79816, callTime=12.267969ms, rank=11
2025-07-08 19:46:39,889 - INFO - 执行详细分析: callId=79816
2025-07-08 19:46:39,966 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\11-79816.txt
2025-07-08 19:46:39,967 - INFO - 处理 callId=46258, callTime=11.938593ms, rank=12
2025-07-08 19:46:39,967 - INFO - 执行详细分析: callId=46258
2025-07-08 19:46:40,020 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\12-46258.txt
2025-07-08 19:46:40,020 - INFO - 处理 callId=77179, callTime=11.48125ms, rank=13
2025-07-08 19:46:40,020 - INFO - 执行详细分析: callId=77179
2025-07-08 19:46:40,085 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\13-77179.txt
2025-07-08 19:46:40,086 - INFO - 处理 callId=53103, callTime=11.237969ms, rank=14
2025-07-08 19:46:40,086 - INFO - 执行详细分析: callId=53103
2025-07-08 19:46:40,134 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\14-53103.txt
2025-07-08 19:46:40,135 - INFO - 处理 callId=49382, callTime=10.471406ms, rank=15
2025-07-08 19:46:40,135 - INFO - 执行详细分析: callId=49382
2025-07-08 19:46:40,190 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\15-49382.txt
2025-07-08 19:46:40,190 - INFO - 处理 callId=53994, callTime=10.000781ms, rank=16
2025-07-08 19:46:40,191 - INFO - 执行详细分析: callId=53994
2025-07-08 19:46:40,250 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\16-53994.txt
2025-07-08 19:46:40,250 - INFO - 处理 callId=84296, callTime=9.927031ms, rank=17
2025-07-08 19:46:40,250 - INFO - 执行详细分析: callId=84296
2025-07-08 19:46:40,321 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\17-84296.txt
2025-07-08 19:46:40,321 - INFO - 处理 callId=83515, callTime=9.805938ms, rank=18
2025-07-08 19:46:40,321 - INFO - 执行详细分析: callId=83515
2025-07-08 19:46:40,391 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\18-83515.txt
2025-07-08 19:46:40,391 - INFO - 处理 callId=8195, callTime=9.802656ms, rank=19
2025-07-08 19:46:40,391 - INFO - 执行详细分析: callId=8195
2025-07-08 19:46:40,440 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\19-8195.txt
2025-07-08 19:46:40,440 - INFO - 处理 callId=58274, callTime=9.47625ms, rank=20
2025-07-08 19:46:40,441 - INFO - 执行详细分析: callId=58274
2025-07-08 19:46:40,493 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\20-58274.txt
2025-07-08 19:46:40,493 - INFO - 处理 callId=46969, callTime=9.303594ms, rank=21
2025-07-08 19:46:40,493 - INFO - 执行详细分析: callId=46969
2025-07-08 19:46:40,538 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\21-46969.txt
2025-07-08 19:46:40,538 - INFO - 处理 callId=54815, callTime=8.807187ms, rank=22
2025-07-08 19:46:40,538 - INFO - 执行详细分析: callId=54815
2025-07-08 19:46:40,598 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\22-54815.txt
2025-07-08 19:46:40,598 - INFO - 处理 callId=6306, callTime=8.717187ms, rank=23
2025-07-08 19:46:40,598 - INFO - 执行详细分析: callId=6306
2025-07-08 19:46:40,660 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\23-6306.txt
2025-07-08 19:46:40,660 - INFO - 处理 callId=38044, callTime=8.485156ms, rank=24
2025-07-08 19:46:40,660 - INFO - 执行详细分析: callId=38044
2025-07-08 19:46:40,700 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\24-38044.txt
2025-07-08 19:46:40,701 - INFO - 处理 callId=48601, callTime=8.463438ms, rank=25
2025-07-08 19:46:40,701 - INFO - 执行详细分析: callId=48601
2025-07-08 19:46:40,757 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\25-48601.txt
2025-07-08 19:46:40,757 - INFO - 处理 callId=52355, callTime=8.461719ms, rank=26
2025-07-08 19:46:40,757 - INFO - 执行详细分析: callId=52355
2025-07-08 19:46:40,816 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\26-52355.txt
2025-07-08 19:46:40,816 - INFO - 处理 callId=58937, callTime=8.458438ms, rank=27
2025-07-08 19:46:40,817 - INFO - 执行详细分析: callId=58937
2025-07-08 19:46:40,880 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\27-58937.txt
2025-07-08 19:46:40,880 - INFO - 处理 callId=50200, callTime=8.219531ms, rank=28
2025-07-08 19:46:40,880 - INFO - 执行详细分析: callId=50200
2025-07-08 19:46:40,937 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\28-50200.txt
2025-07-08 19:46:40,937 - INFO - 处理 callId=71918, callTime=8.207656ms, rank=29
2025-07-08 19:46:40,937 - INFO - 执行详细分析: callId=71918
2025-07-08 19:46:41,011 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\29-71918.txt
2025-07-08 19:46:41,011 - INFO - 处理 callId=51762, callTime=8.195937ms, rank=30
2025-07-08 19:46:41,011 - INFO - 执行详细分析: callId=51762
2025-07-08 19:46:41,070 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\30-51762.txt
2025-07-08 19:46:41,070 - INFO - 处理 callId=41252, callTime=8.182031ms, rank=31
2025-07-08 19:46:41,070 - INFO - 执行详细分析: callId=41252
2025-07-08 19:46:41,113 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\31-41252.txt
2025-07-08 19:46:41,113 - INFO - 处理 callId=74055, callTime=8.135625ms, rank=32
2025-07-08 19:46:41,113 - INFO - 执行详细分析: callId=74055
2025-07-08 19:46:41,176 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\32-74055.txt
2025-07-08 19:46:41,177 - INFO - 处理 callId=83585, callTime=8.038281ms, rank=33
2025-07-08 19:46:41,177 - INFO - 执行详细分析: callId=83585
2025-07-08 19:46:41,257 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\33-83585.txt
2025-07-08 19:46:41,258 - INFO - 处理 callId=72751, callTime=8.012656ms, rank=34
2025-07-08 19:46:41,258 - INFO - 执行详细分析: callId=72751
2025-07-08 19:46:41,331 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\34-72751.txt
2025-07-08 19:46:41,331 - INFO - 处理 callId=40940, callTime=7.934687ms, rank=35
2025-07-08 19:46:41,331 - INFO - 执行详细分析: callId=40940
2025-07-08 19:46:41,373 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\35-40940.txt
2025-07-08 19:46:41,373 - INFO - 处理 callId=81360, callTime=7.900937ms, rank=36
2025-07-08 19:46:41,373 - INFO - 执行详细分析: callId=81360
2025-07-08 19:46:41,443 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\36-81360.txt
2025-07-08 19:46:41,443 - INFO - 处理 callId=27884, callTime=7.704688ms, rank=37
2025-07-08 19:46:41,443 - INFO - 执行详细分析: callId=27884
2025-07-08 19:46:41,487 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\37-27884.txt
2025-07-08 19:46:41,487 - INFO - 处理 callId=38200, callTime=7.693437ms, rank=38
2025-07-08 19:46:41,487 - INFO - 执行详细分析: callId=38200
2025-07-08 19:46:41,536 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\38-38200.txt
2025-07-08 19:46:41,536 - INFO - 处理 callId=31715, callTime=7.66125ms, rank=39
2025-07-08 19:46:41,537 - INFO - 执行详细分析: callId=31715
2025-07-08 19:46:41,572 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\39-31715.txt
2025-07-08 19:46:41,572 - INFO - 处理 callId=26347, callTime=7.649844ms, rank=40
2025-07-08 19:46:41,572 - INFO - 执行详细分析: callId=26347
2025-07-08 19:46:41,644 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101321_wpp缩放\40-26347.txt
2025-07-08 19:46:41,645 - INFO - 完成处理文件: perfmon-angle-20250707_101321_wpp缩放.dat
2025-07-08 19:46:41,645 - INFO - 处理进度: 4/30
2025-07-08 19:46:41,645 - INFO - 开始处理文件: perfmon-angle-20250707_101853_wpp切换页面.dat
2025-07-08 19:46:41,645 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-angle-20250707_101853_wpp切换页面.dat
2025-07-08 19:46:41,747 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-angle-20250707_101853_wpp切换页面.dat.txt
2025-07-08 19:46:41,747 - INFO - 找到event函数: E:\wpsskia\qt\source\qtbase\src\widgets\kernel\qwidget.cpp:9682 event
2025-07-08 19:46:41,747 - INFO - 提取callId: 63972, callTime: 131.302344ms
2025-07-08 19:46:41,747 - INFO - 提取callId: 60517, callTime: 111.09ms
2025-07-08 19:46:41,747 - INFO - 提取callId: 71069, callTime: 75.249531ms
2025-07-08 19:46:41,747 - INFO - 提取callId: 102938, callTime: 47.013438ms
2025-07-08 19:46:41,747 - INFO - 提取callId: 105463, callTime: 44.635782ms
2025-07-08 19:46:41,747 - INFO - 提取callId: 65726, callTime: 42.246563ms
2025-07-08 19:46:41,747 - INFO - 提取callId: 57730, callTime: 35.937032ms
2025-07-08 19:46:41,747 - INFO - 提取callId: 94939, callTime: 34.837188ms
2025-07-08 19:46:41,747 - INFO - 提取callId: 14519, callTime: 31.659688ms
2025-07-08 19:46:41,748 - INFO - 提取callId: 23501, callTime: 29.431719ms
2025-07-08 19:46:41,748 - INFO - 提取callId: 67595, callTime: 29.206563ms
2025-07-08 19:46:41,748 - INFO - 提取callId: 1642, callTime: 28.079844ms
2025-07-08 19:46:41,748 - INFO - 提取callId: 101382, callTime: 24.106875ms
2025-07-08 19:46:41,748 - INFO - 提取callId: 74028, callTime: 23.006094ms
2025-07-08 19:46:41,748 - INFO - 提取callId: 72624, callTime: 21.865312ms
2025-07-08 19:46:41,748 - INFO - 提取callId: 90363, callTime: 21.192813ms
2025-07-08 19:46:41,748 - INFO - 提取callId: 104284, callTime: 20.962656ms
2025-07-08 19:46:41,748 - INFO - 提取callId: 72540, callTime: 20.676719ms
2025-07-08 19:46:41,748 - INFO - 提取callId: 82324, callTime: 19.376406ms
2025-07-08 19:46:41,748 - INFO - 提取callId: 98870, callTime: 19.027031ms
2025-07-08 19:46:41,748 - INFO - 提取callId: 9748, callTime: 18.370625ms
2025-07-08 19:46:41,748 - INFO - 提取callId: 68814, callTime: 17.393437ms
2025-07-08 19:46:41,748 - INFO - 提取callId: 100260, callTime: 16.920312ms
2025-07-08 19:46:41,748 - INFO - 提取callId: 47709, callTime: 16.849687ms
2025-07-08 19:46:41,748 - INFO - 提取callId: 56575, callTime: 16.11375ms
2025-07-08 19:46:41,749 - INFO - 提取callId: 27212, callTime: 16.064844ms
2025-07-08 19:46:41,749 - INFO - 提取callId: 56704, callTime: 15.972344ms
2025-07-08 19:46:41,749 - INFO - 提取callId: 80447, callTime: 15.550469ms
2025-07-08 19:46:41,749 - INFO - 提取callId: 80576, callTime: 15.315157ms
2025-07-08 19:46:41,749 - INFO - 提取callId: 54933, callTime: 14.897656ms
2025-07-08 19:46:41,749 - INFO - 提取callId: 104200, callTime: 14.402188ms
2025-07-08 19:46:41,749 - INFO - 提取callId: 100344, callTime: 13.777812ms
2025-07-08 19:46:41,749 - INFO - 提取callId: 51474, callTime: 13.473594ms
2025-07-08 19:46:41,749 - INFO - 提取callId: 101298, callTime: 12.974531ms
2025-07-08 19:46:41,749 - INFO - 提取callId: 54804, callTime: 12.782188ms
2025-07-08 19:46:41,749 - INFO - 提取callId: 85681, callTime: 12.687657ms
2025-07-08 19:46:41,749 - INFO - 提取callId: 6306, callTime: 12.655ms
2025-07-08 19:46:41,749 - INFO - 提取callId: 73944, callTime: 12.647657ms
2025-07-08 19:46:41,749 - INFO - 提取callId: 97542, callTime: 12.5075ms
2025-07-08 19:46:41,749 - INFO - 提取callId: 79541, callTime: 11.878282ms
2025-07-08 19:46:41,749 - INFO - 提取到 40 个event相关的callId
2025-07-08 19:46:41,749 - INFO - 处理 callId=63972, callTime=131.302344ms, rank=1
2025-07-08 19:46:41,749 - INFO - 执行详细分析: callId=63972
2025-07-08 19:46:41,860 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\1-63972.txt
2025-07-08 19:46:41,860 - INFO - 处理 callId=60517, callTime=111.09ms, rank=2
2025-07-08 19:46:41,860 - INFO - 执行详细分析: callId=60517
2025-07-08 19:46:42,000 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\2-60517.txt
2025-07-08 19:46:42,000 - INFO - 处理 callId=71069, callTime=75.249531ms, rank=3
2025-07-08 19:46:42,000 - INFO - 执行详细分析: callId=71069
2025-07-08 19:46:42,104 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\3-71069.txt
2025-07-08 19:46:42,104 - INFO - 处理 callId=102938, callTime=47.013438ms, rank=4
2025-07-08 19:46:42,104 - INFO - 执行详细分析: callId=102938
2025-07-08 19:46:42,217 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\4-102938.txt
2025-07-08 19:46:42,217 - INFO - 处理 callId=105463, callTime=44.635782ms, rank=5
2025-07-08 19:46:42,217 - INFO - 执行详细分析: callId=105463
2025-07-08 19:46:42,333 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\5-105463.txt
2025-07-08 19:46:42,333 - INFO - 处理 callId=65726, callTime=42.246563ms, rank=6
2025-07-08 19:46:42,333 - INFO - 执行详细分析: callId=65726
2025-07-08 19:46:42,448 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\6-65726.txt
2025-07-08 19:46:42,449 - INFO - 处理 callId=57730, callTime=35.937032ms, rank=7
2025-07-08 19:46:42,449 - INFO - 执行详细分析: callId=57730
2025-07-08 19:46:42,614 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\7-57730.txt
2025-07-08 19:46:42,614 - INFO - 处理 callId=94939, callTime=34.837188ms, rank=8
2025-07-08 19:46:42,614 - INFO - 执行详细分析: callId=94939
2025-07-08 19:46:42,733 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\8-94939.txt
2025-07-08 19:46:42,733 - INFO - 处理 callId=14519, callTime=31.659688ms, rank=9
2025-07-08 19:46:42,734 - INFO - 执行详细分析: callId=14519
2025-07-08 19:46:43,008 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\9-14519.txt
2025-07-08 19:46:43,008 - INFO - 处理 callId=23501, callTime=29.431719ms, rank=10
2025-07-08 19:46:43,008 - INFO - 执行详细分析: callId=23501
2025-07-08 19:46:43,146 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\10-23501.txt
2025-07-08 19:46:43,146 - INFO - 处理 callId=67595, callTime=29.206563ms, rank=11
2025-07-08 19:46:43,146 - INFO - 执行详细分析: callId=67595
2025-07-08 19:46:43,232 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\11-67595.txt
2025-07-08 19:46:43,232 - INFO - 处理 callId=1642, callTime=28.079844ms, rank=12
2025-07-08 19:46:43,233 - INFO - 执行详细分析: callId=1642
2025-07-08 19:46:43,304 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\12-1642.txt
2025-07-08 19:46:43,304 - INFO - 处理 callId=101382, callTime=24.106875ms, rank=13
2025-07-08 19:46:43,304 - INFO - 执行详细分析: callId=101382
2025-07-08 19:46:43,430 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\13-101382.txt
2025-07-08 19:46:43,430 - INFO - 处理 callId=74028, callTime=23.006094ms, rank=14
2025-07-08 19:46:43,430 - INFO - 执行详细分析: callId=74028
2025-07-08 19:46:43,517 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\14-74028.txt
2025-07-08 19:46:43,517 - INFO - 处理 callId=72624, callTime=21.865312ms, rank=15
2025-07-08 19:46:43,517 - INFO - 执行详细分析: callId=72624
2025-07-08 19:46:43,609 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\15-72624.txt
2025-07-08 19:46:43,609 - INFO - 处理 callId=90363, callTime=21.192813ms, rank=16
2025-07-08 19:46:43,609 - INFO - 执行详细分析: callId=90363
2025-07-08 19:46:43,735 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\16-90363.txt
2025-07-08 19:46:43,735 - INFO - 处理 callId=104284, callTime=20.962656ms, rank=17
2025-07-08 19:46:43,735 - INFO - 执行详细分析: callId=104284
2025-07-08 19:46:43,845 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\17-104284.txt
2025-07-08 19:46:43,846 - INFO - 处理 callId=72540, callTime=20.676719ms, rank=18
2025-07-08 19:46:43,846 - INFO - 执行详细分析: callId=72540
2025-07-08 19:46:43,909 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\18-72540.txt
2025-07-08 19:46:43,910 - INFO - 处理 callId=82324, callTime=19.376406ms, rank=19
2025-07-08 19:46:43,910 - INFO - 执行详细分析: callId=82324
2025-07-08 19:46:44,002 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\19-82324.txt
2025-07-08 19:46:44,002 - INFO - 处理 callId=98870, callTime=19.027031ms, rank=20
2025-07-08 19:46:44,003 - INFO - 执行详细分析: callId=98870
2025-07-08 19:46:44,115 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\20-98870.txt
2025-07-08 19:46:44,116 - INFO - 处理 callId=9748, callTime=18.370625ms, rank=21
2025-07-08 19:46:44,116 - INFO - 执行详细分析: callId=9748
2025-07-08 19:46:44,149 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\21-9748.txt
2025-07-08 19:46:44,149 - INFO - 处理 callId=68814, callTime=17.393437ms, rank=22
2025-07-08 19:46:44,149 - INFO - 执行详细分析: callId=68814
2025-07-08 19:46:44,295 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\22-68814.txt
2025-07-08 19:46:44,295 - INFO - 处理 callId=100260, callTime=16.920312ms, rank=23
2025-07-08 19:46:44,295 - INFO - 执行详细分析: callId=100260
2025-07-08 19:46:44,379 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\23-100260.txt
2025-07-08 19:46:44,380 - INFO - 处理 callId=47709, callTime=16.849687ms, rank=24
2025-07-08 19:46:44,380 - INFO - 执行详细分析: callId=47709
2025-07-08 19:46:44,430 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\24-47709.txt
2025-07-08 19:46:44,430 - INFO - 处理 callId=56575, callTime=16.11375ms, rank=25
2025-07-08 19:46:44,430 - INFO - 执行详细分析: callId=56575
2025-07-08 19:46:44,483 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\25-56575.txt
2025-07-08 19:46:44,483 - INFO - 处理 callId=27212, callTime=16.064844ms, rank=26
2025-07-08 19:46:44,484 - INFO - 执行详细分析: callId=27212
2025-07-08 19:46:44,541 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\26-27212.txt
2025-07-08 19:46:44,541 - INFO - 处理 callId=56704, callTime=15.972344ms, rank=27
2025-07-08 19:46:44,542 - INFO - 执行详细分析: callId=56704
2025-07-08 19:46:44,610 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\27-56704.txt
2025-07-08 19:46:44,610 - INFO - 处理 callId=80447, callTime=15.550469ms, rank=28
2025-07-08 19:46:44,610 - INFO - 执行详细分析: callId=80447
2025-07-08 19:46:44,680 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\28-80447.txt
2025-07-08 19:46:44,680 - INFO - 处理 callId=80576, callTime=15.315157ms, rank=29
2025-07-08 19:46:44,680 - INFO - 执行详细分析: callId=80576
2025-07-08 19:46:44,801 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\29-80576.txt
2025-07-08 19:46:44,801 - INFO - 处理 callId=54933, callTime=14.897656ms, rank=30
2025-07-08 19:46:44,801 - INFO - 执行详细分析: callId=54933
2025-07-08 19:46:44,902 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\30-54933.txt
2025-07-08 19:46:44,902 - INFO - 处理 callId=104200, callTime=14.402188ms, rank=31
2025-07-08 19:46:44,902 - INFO - 执行详细分析: callId=104200
2025-07-08 19:46:44,990 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\31-104200.txt
2025-07-08 19:46:44,990 - INFO - 处理 callId=100344, callTime=13.777812ms, rank=32
2025-07-08 19:46:44,990 - INFO - 执行详细分析: callId=100344
2025-07-08 19:46:45,091 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\32-100344.txt
2025-07-08 19:46:45,091 - INFO - 处理 callId=51474, callTime=13.473594ms, rank=33
2025-07-08 19:46:45,091 - INFO - 执行详细分析: callId=51474
2025-07-08 19:46:45,165 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\33-51474.txt
2025-07-08 19:46:45,165 - INFO - 处理 callId=101298, callTime=12.974531ms, rank=34
2025-07-08 19:46:45,165 - INFO - 执行详细分析: callId=101298
2025-07-08 19:46:45,250 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\34-101298.txt
2025-07-08 19:46:45,250 - INFO - 处理 callId=54804, callTime=12.782188ms, rank=35
2025-07-08 19:46:45,250 - INFO - 执行详细分析: callId=54804
2025-07-08 19:46:45,303 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\35-54804.txt
2025-07-08 19:46:45,303 - INFO - 处理 callId=85681, callTime=12.687657ms, rank=36
2025-07-08 19:46:45,303 - INFO - 执行详细分析: callId=85681
2025-07-08 19:46:45,377 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\36-85681.txt
2025-07-08 19:46:45,377 - INFO - 处理 callId=6306, callTime=12.655ms, rank=37
2025-07-08 19:46:45,377 - INFO - 执行详细分析: callId=6306
2025-07-08 19:46:45,440 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\37-6306.txt
2025-07-08 19:46:45,440 - INFO - 处理 callId=73944, callTime=12.647657ms, rank=38
2025-07-08 19:46:45,441 - INFO - 执行详细分析: callId=73944
2025-07-08 19:46:45,505 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\38-73944.txt
2025-07-08 19:46:45,505 - INFO - 处理 callId=97542, callTime=12.5075ms, rank=39
2025-07-08 19:46:45,505 - INFO - 执行详细分析: callId=97542
2025-07-08 19:46:45,609 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\39-97542.txt
2025-07-08 19:46:45,610 - INFO - 处理 callId=79541, callTime=11.878282ms, rank=40
2025-07-08 19:46:45,610 - INFO - 执行详细分析: callId=79541
2025-07-08 19:46:45,693 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_101853_wpp切换页面\40-79541.txt
2025-07-08 19:46:45,693 - INFO - 完成处理文件: perfmon-angle-20250707_101853_wpp切换页面.dat
2025-07-08 19:46:45,693 - INFO - 处理进度: 5/30
2025-07-08 19:46:45,694 - INFO - 开始处理文件: perfmon-angle-20250707_102205_wpp缩略图生成.dat
2025-07-08 19:46:45,694 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-angle-20250707_102205_wpp缩略图生成.dat
2025-07-08 19:46:45,763 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-angle-20250707_102205_wpp缩略图生成.dat.txt
2025-07-08 19:46:45,764 - INFO - 找到event函数: E:\wpsskia\qt\source\qtbase\src\widgets\kernel\qwidget.cpp:9682 event
2025-07-08 19:46:45,764 - INFO - 提取callId: 56223, callTime: 111.297344ms
2025-07-08 19:46:45,764 - INFO - 提取callId: 71221, callTime: 103.367187ms
2025-07-08 19:46:45,764 - INFO - 提取callId: 54100, callTime: 56.309844ms
2025-07-08 19:46:45,764 - INFO - 提取callId: 63878, callTime: 55.833594ms
2025-07-08 19:46:45,764 - INFO - 提取callId: 13446, callTime: 30.958125ms
2025-07-08 19:46:45,764 - INFO - 提取callId: 22462, callTime: 28.534843ms
2025-07-08 19:46:45,764 - INFO - 提取callId: 37602, callTime: 26.593437ms
2025-07-08 19:46:45,764 - INFO - 提取callId: 1642, callTime: 24.745ms
2025-07-08 19:46:45,764 - INFO - 提取callId: 60682, callTime: 21.349844ms
2025-07-08 19:46:45,764 - INFO - 提取callId: 11004, callTime: 20.885312ms
2025-07-08 19:46:45,764 - INFO - 提取callId: 47442, callTime: 18.266719ms
2025-07-08 19:46:45,764 - INFO - 提取callId: 8657, callTime: 16.839063ms
2025-07-08 19:46:45,764 - INFO - 提取callId: 26173, callTime: 15.807968ms
2025-07-08 19:46:45,765 - INFO - 提取callId: 3190, callTime: 11.986094ms
2025-07-08 19:46:45,765 - INFO - 提取callId: 41238, callTime: 11.755938ms
2025-07-08 19:46:45,765 - INFO - 提取callId: 46400, callTime: 10.906563ms
2025-07-08 19:46:45,765 - INFO - 提取callId: 5464, callTime: 9.694375ms
2025-07-08 19:46:45,765 - INFO - 提取callId: 50336, callTime: 9.48375ms
2025-07-08 19:46:45,765 - INFO - 提取callId: 7365, callTime: 9.347812ms
2025-07-08 19:46:45,765 - INFO - 提取callId: 49371, callTime: 9.161563ms
2025-07-08 19:46:45,765 - INFO - 提取callId: 40690, callTime: 8.920468ms
2025-07-08 19:46:45,765 - INFO - 提取callId: 38094, callTime: 8.685469ms
2025-07-08 19:46:45,765 - INFO - 提取callId: 40532, callTime: 8.511719ms
2025-07-08 19:46:45,765 - INFO - 提取callId: 37942, callTime: 8.459375ms
2025-07-08 19:46:45,765 - INFO - 提取callId: 38158, callTime: 8.438906ms
2025-07-08 19:46:45,765 - INFO - 提取callId: 31103, callTime: 8.351406ms
2025-07-08 19:46:45,765 - INFO - 提取callId: 45914, callTime: 8.284843ms
2025-07-08 19:46:45,765 - INFO - 提取callId: 34825, callTime: 8.235157ms
2025-07-08 19:46:45,766 - INFO - 提取callId: 27368, callTime: 8.1125ms
2025-07-08 19:46:45,766 - INFO - 提取callId: 62839, callTime: 8.002188ms
2025-07-08 19:46:45,766 - INFO - 提取callId: 12067, callTime: 7.957031ms
2025-07-08 19:46:45,766 - INFO - 提取callId: 38658, callTime: 7.681875ms
2025-07-08 19:46:45,766 - INFO - 提取callId: 38850, callTime: 7.55875ms
2025-07-08 19:46:45,766 - INFO - 提取callId: 61913, callTime: 7.493281ms
2025-07-08 19:46:45,766 - INFO - 提取callId: 52575, callTime: 7.444062ms
2025-07-08 19:46:45,766 - INFO - 提取callId: 38920, callTime: 7.403125ms
2025-07-08 19:46:45,766 - INFO - 提取callId: 45290, callTime: 7.116718ms
2025-07-08 19:46:45,766 - INFO - 提取callId: 42174, callTime: 7.061875ms
2025-07-08 19:46:45,766 - INFO - 提取callId: 68919, callTime: 7.024688ms
2025-07-08 19:46:45,766 - INFO - 提取callId: 44122, callTime: 6.912188ms
2025-07-08 19:46:45,766 - INFO - 提取到 40 个event相关的callId
2025-07-08 19:46:45,766 - INFO - 处理 callId=56223, callTime=111.297344ms, rank=1
2025-07-08 19:46:45,766 - INFO - 执行详细分析: callId=56223
2025-07-08 19:46:45,844 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\1-56223.txt
2025-07-08 19:46:45,845 - INFO - 处理 callId=71221, callTime=103.367187ms, rank=2
2025-07-08 19:46:45,845 - INFO - 执行详细分析: callId=71221
2025-07-08 19:46:45,914 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\2-71221.txt
2025-07-08 19:46:45,914 - INFO - 处理 callId=54100, callTime=56.309844ms, rank=3
2025-07-08 19:46:45,914 - INFO - 执行详细分析: callId=54100
2025-07-08 19:46:46,001 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\3-54100.txt
2025-07-08 19:46:46,002 - INFO - 处理 callId=63878, callTime=55.833594ms, rank=4
2025-07-08 19:46:46,002 - INFO - 执行详细分析: callId=63878
2025-07-08 19:46:46,520 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\4-63878.txt
2025-07-08 19:46:46,520 - INFO - 处理 callId=13446, callTime=30.958125ms, rank=5
2025-07-08 19:46:46,520 - INFO - 执行详细分析: callId=13446
2025-07-08 19:46:46,793 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\5-13446.txt
2025-07-08 19:46:46,793 - INFO - 处理 callId=22462, callTime=28.534843ms, rank=6
2025-07-08 19:46:46,793 - INFO - 执行详细分析: callId=22462
2025-07-08 19:46:46,930 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\6-22462.txt
2025-07-08 19:46:46,930 - INFO - 处理 callId=37602, callTime=26.593437ms, rank=7
2025-07-08 19:46:46,930 - INFO - 执行详细分析: callId=37602
2025-07-08 19:46:46,970 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\7-37602.txt
2025-07-08 19:46:46,970 - INFO - 处理 callId=1642, callTime=24.745ms, rank=8
2025-07-08 19:46:46,970 - INFO - 执行详细分析: callId=1642
2025-07-08 19:46:47,013 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\8-1642.txt
2025-07-08 19:46:47,013 - INFO - 处理 callId=60682, callTime=21.349844ms, rank=9
2025-07-08 19:46:47,013 - INFO - 执行详细分析: callId=60682
2025-07-08 19:46:47,096 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\9-60682.txt
2025-07-08 19:46:47,096 - INFO - 处理 callId=11004, callTime=20.885312ms, rank=10
2025-07-08 19:46:47,096 - INFO - 执行详细分析: callId=11004
2025-07-08 19:46:47,132 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\10-11004.txt
2025-07-08 19:46:47,133 - INFO - 处理 callId=47442, callTime=18.266719ms, rank=11
2025-07-08 19:46:47,133 - INFO - 执行详细分析: callId=47442
2025-07-08 19:46:47,204 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\11-47442.txt
2025-07-08 19:46:47,204 - INFO - 处理 callId=8657, callTime=16.839063ms, rank=12
2025-07-08 19:46:47,204 - INFO - 执行详细分析: callId=8657
2025-07-08 19:46:47,236 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\12-8657.txt
2025-07-08 19:46:47,236 - INFO - 处理 callId=26173, callTime=15.807968ms, rank=13
2025-07-08 19:46:47,236 - INFO - 执行详细分析: callId=26173
2025-07-08 19:46:47,291 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\13-26173.txt
2025-07-08 19:46:47,291 - INFO - 处理 callId=3190, callTime=11.986094ms, rank=14
2025-07-08 19:46:47,291 - INFO - 执行详细分析: callId=3190
2025-07-08 19:46:47,355 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\14-3190.txt
2025-07-08 19:46:47,355 - INFO - 处理 callId=41238, callTime=11.755938ms, rank=15
2025-07-08 19:46:47,355 - INFO - 执行详细分析: callId=41238
2025-07-08 19:46:47,397 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\15-41238.txt
2025-07-08 19:46:47,397 - INFO - 处理 callId=46400, callTime=10.906563ms, rank=16
2025-07-08 19:46:47,397 - INFO - 执行详细分析: callId=46400
2025-07-08 19:46:47,463 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\16-46400.txt
2025-07-08 19:46:47,463 - INFO - 处理 callId=5464, callTime=9.694375ms, rank=17
2025-07-08 19:46:47,464 - INFO - 执行详细分析: callId=5464
2025-07-08 19:46:47,525 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\17-5464.txt
2025-07-08 19:46:47,525 - INFO - 处理 callId=50336, callTime=9.48375ms, rank=18
2025-07-08 19:46:47,525 - INFO - 执行详细分析: callId=50336
2025-07-08 19:46:47,618 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\18-50336.txt
2025-07-08 19:46:47,619 - INFO - 处理 callId=7365, callTime=9.347812ms, rank=19
2025-07-08 19:46:47,619 - INFO - 执行详细分析: callId=7365
2025-07-08 19:46:47,658 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\19-7365.txt
2025-07-08 19:46:47,658 - INFO - 处理 callId=49371, callTime=9.161563ms, rank=20
2025-07-08 19:46:47,658 - INFO - 执行详细分析: callId=49371
2025-07-08 19:46:47,726 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\20-49371.txt
2025-07-08 19:46:47,726 - INFO - 处理 callId=40690, callTime=8.920468ms, rank=21
2025-07-08 19:46:47,726 - INFO - 执行详细分析: callId=40690
2025-07-08 19:46:47,771 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\21-40690.txt
2025-07-08 19:46:47,771 - INFO - 处理 callId=38094, callTime=8.685469ms, rank=22
2025-07-08 19:46:47,771 - INFO - 执行详细分析: callId=38094
2025-07-08 19:46:47,811 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\22-38094.txt
2025-07-08 19:46:47,811 - INFO - 处理 callId=40532, callTime=8.511719ms, rank=23
2025-07-08 19:46:47,811 - INFO - 执行详细分析: callId=40532
2025-07-08 19:46:47,853 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\23-40532.txt
2025-07-08 19:46:47,853 - INFO - 处理 callId=37942, callTime=8.459375ms, rank=24
2025-07-08 19:46:47,853 - INFO - 执行详细分析: callId=37942
2025-07-08 19:46:47,893 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\24-37942.txt
2025-07-08 19:46:47,893 - INFO - 处理 callId=38158, callTime=8.438906ms, rank=25
2025-07-08 19:46:47,893 - INFO - 执行详细分析: callId=38158
2025-07-08 19:46:47,932 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\25-38158.txt
2025-07-08 19:46:47,932 - INFO - 处理 callId=31103, callTime=8.351406ms, rank=26
2025-07-08 19:46:47,932 - INFO - 执行详细分析: callId=31103
2025-07-08 19:46:47,966 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\26-31103.txt
2025-07-08 19:46:47,966 - INFO - 处理 callId=45914, callTime=8.284843ms, rank=27
2025-07-08 19:46:47,966 - INFO - 执行详细分析: callId=45914
2025-07-08 19:46:48,015 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\27-45914.txt
2025-07-08 19:46:48,015 - INFO - 处理 callId=34825, callTime=8.235157ms, rank=28
2025-07-08 19:46:48,015 - INFO - 执行详细分析: callId=34825
2025-07-08 19:46:48,076 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\28-34825.txt
2025-07-08 19:46:48,077 - INFO - 处理 callId=27368, callTime=8.1125ms, rank=29
2025-07-08 19:46:48,077 - INFO - 执行详细分析: callId=27368
2025-07-08 19:46:48,149 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\29-27368.txt
2025-07-08 19:46:48,149 - INFO - 处理 callId=62839, callTime=8.002188ms, rank=30
2025-07-08 19:46:48,150 - INFO - 执行详细分析: callId=62839
2025-07-08 19:46:48,228 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\30-62839.txt
2025-07-08 19:46:48,228 - INFO - 处理 callId=12067, callTime=7.957031ms, rank=31
2025-07-08 19:46:48,228 - INFO - 执行详细分析: callId=12067
2025-07-08 19:46:48,265 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\31-12067.txt
2025-07-08 19:46:48,265 - INFO - 处理 callId=38658, callTime=7.681875ms, rank=32
2025-07-08 19:46:48,265 - INFO - 执行详细分析: callId=38658
2025-07-08 19:46:48,305 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\32-38658.txt
2025-07-08 19:46:48,305 - INFO - 处理 callId=38850, callTime=7.55875ms, rank=33
2025-07-08 19:46:48,305 - INFO - 执行详细分析: callId=38850
2025-07-08 19:46:48,344 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\33-38850.txt
2025-07-08 19:46:48,344 - INFO - 处理 callId=61913, callTime=7.493281ms, rank=34
2025-07-08 19:46:48,344 - INFO - 执行详细分析: callId=61913
2025-07-08 19:46:48,419 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\34-61913.txt
2025-07-08 19:46:48,419 - INFO - 处理 callId=52575, callTime=7.444062ms, rank=35
2025-07-08 19:46:48,419 - INFO - 执行详细分析: callId=52575
2025-07-08 19:46:48,516 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\35-52575.txt
2025-07-08 19:46:48,516 - INFO - 处理 callId=38920, callTime=7.403125ms, rank=36
2025-07-08 19:46:48,516 - INFO - 执行详细分析: callId=38920
2025-07-08 19:46:48,556 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\36-38920.txt
2025-07-08 19:46:48,556 - INFO - 处理 callId=45290, callTime=7.116718ms, rank=37
2025-07-08 19:46:48,556 - INFO - 执行详细分析: callId=45290
2025-07-08 19:46:48,602 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\37-45290.txt
2025-07-08 19:46:48,602 - INFO - 处理 callId=42174, callTime=7.061875ms, rank=38
2025-07-08 19:46:48,602 - INFO - 执行详细分析: callId=42174
2025-07-08 19:46:48,646 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\38-42174.txt
2025-07-08 19:46:48,646 - INFO - 处理 callId=68919, callTime=7.024688ms, rank=39
2025-07-08 19:46:48,646 - INFO - 执行详细分析: callId=68919
2025-07-08 19:46:48,736 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\39-68919.txt
2025-07-08 19:46:48,736 - INFO - 处理 callId=44122, callTime=6.912188ms, rank=40
2025-07-08 19:46:48,736 - INFO - 执行详细分析: callId=44122
2025-07-08 19:46:48,780 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102205_wpp缩略图生成\40-44122.txt
2025-07-08 19:46:48,780 - INFO - 完成处理文件: perfmon-angle-20250707_102205_wpp缩略图生成.dat
2025-07-08 19:46:48,780 - INFO - 处理进度: 6/30
2025-07-08 19:46:48,780 - INFO - 开始处理文件: perfmon-angle-20250707_102653_wpp动画播放对象动画.dat
2025-07-08 19:46:48,780 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-angle-20250707_102653_wpp动画播放对象动画.dat
2025-07-08 19:46:52,990 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-angle-20250707_102653_wpp动画播放对象动画.dat.txt
2025-07-08 19:46:52,990 - INFO - 找到event函数: E:\wpsskia\qt\source\qtbase\src\widgets\kernel\qwidget.cpp:9682 event
2025-07-08 19:46:52,990 - INFO - 提取callId: 13260, callTime: 31.675312ms
2025-07-08 19:46:52,990 - INFO - 提取callId: 1642, callTime: 29.647343ms
2025-07-08 19:46:52,991 - INFO - 提取callId: 21995, callTime: 27.705313ms
2025-07-08 19:46:52,991 - INFO - 提取callId: 4075, callTime: 25.960312ms
2025-07-08 19:46:52,991 - INFO - 提取callId: 9748, callTime: 25.40625ms
2025-07-08 19:46:52,991 - INFO - 提取callId: 83284, callTime: 16.584688ms
2025-07-08 19:46:52,991 - INFO - 提取callId: 48541, callTime: 15.522343ms
2025-07-08 19:46:52,991 - INFO - 提取callId: 51282, callTime: 15.477344ms
2025-07-08 19:46:52,991 - INFO - 提取callId: 8195, callTime: 10.34875ms
2025-07-08 19:46:52,991 - INFO - 提取callId: 47203, callTime: 9.964375ms
2025-07-08 19:46:52,991 - INFO - 提取callId: 65886, callTime: 9.93875ms
2025-07-08 19:46:52,991 - INFO - 提取callId: 59412, callTime: 9.745938ms
2025-07-08 19:46:52,991 - INFO - 提取callId: 79302, callTime: 9.325625ms
2025-07-08 19:46:52,991 - INFO - 提取callId: 76338, callTime: 9.254062ms
2025-07-08 19:46:52,992 - INFO - 提取callId: 24487, callTime: 9.2525ms
2025-07-08 19:46:52,992 - INFO - 提取callId: 78834, callTime: 8.869063ms
2025-07-08 19:46:52,992 - INFO - 提取callId: 25659, callTime: 8.689219ms
2025-07-08 19:46:52,992 - INFO - 提取callId: 75948, callTime: 8.579844ms
2025-07-08 19:46:52,992 - INFO - 提取callId: 6306, callTime: 8.509843ms
2025-07-08 19:46:52,992 - INFO - 提取callId: 67602, callTime: 8.396094ms
2025-07-08 19:46:52,992 - INFO - 提取callId: 67290, callTime: 8.35ms
2025-07-08 19:46:52,992 - INFO - 提取callId: 75324, callTime: 8.339844ms
2025-07-08 19:46:52,992 - INFO - 提取callId: 70254, callTime: 8.309218ms
2025-07-08 19:46:52,992 - INFO - 提取callId: 62298, callTime: 8.243125ms
2025-07-08 19:46:52,992 - INFO - 提取callId: 80550, callTime: 8.223125ms
2025-07-08 19:46:52,992 - INFO - 提取callId: 73998, callTime: 8.204688ms
2025-07-08 19:46:52,992 - INFO - 提取callId: 64716, callTime: 8.1875ms
2025-07-08 19:46:52,992 - INFO - 提取callId: 74544, callTime: 8.155938ms
2025-07-08 19:46:52,993 - INFO - 提取callId: 67056, callTime: 8.130312ms
2025-07-08 19:46:52,993 - INFO - 提取callId: 77508, callTime: 8.100625ms
2025-07-08 19:46:52,993 - INFO - 提取callId: 80394, callTime: 8.059844ms
2025-07-08 19:46:52,993 - INFO - 提取callId: 71112, callTime: 8.049844ms
2025-07-08 19:46:52,993 - INFO - 提取callId: 69084, callTime: 8.02875ms
2025-07-08 19:46:52,993 - INFO - 提取callId: 75090, callTime: 7.99125ms
2025-07-08 19:46:52,993 - INFO - 提取callId: 61596, callTime: 7.964532ms
2025-07-08 19:46:52,993 - INFO - 提取callId: 74076, callTime: 7.96375ms
2025-07-08 19:46:52,993 - INFO - 提取callId: 75636, callTime: 7.958907ms
2025-07-08 19:46:52,993 - INFO - 提取callId: 81468, callTime: 7.915468ms
2025-07-08 19:46:52,993 - INFO - 提取callId: 45291, callTime: 7.887968ms
2025-07-08 19:46:52,993 - INFO - 提取callId: 68148, callTime: 7.885312ms
2025-07-08 19:46:52,993 - INFO - 提取到 40 个event相关的callId
2025-07-08 19:46:52,994 - INFO - 处理 callId=13260, callTime=31.675312ms, rank=1
2025-07-08 19:46:52,994 - INFO - 执行详细分析: callId=13260
2025-07-08 19:46:53,265 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\1-13260.txt
2025-07-08 19:46:53,265 - INFO - 处理 callId=1642, callTime=29.647343ms, rank=2
2025-07-08 19:46:53,265 - INFO - 执行详细分析: callId=1642
2025-07-08 19:46:53,335 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\2-1642.txt
2025-07-08 19:46:53,336 - INFO - 处理 callId=21995, callTime=27.705313ms, rank=3
2025-07-08 19:46:53,336 - INFO - 执行详细分析: callId=21995
2025-07-08 19:46:53,460 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\3-21995.txt
2025-07-08 19:46:53,460 - INFO - 处理 callId=4075, callTime=25.960312ms, rank=4
2025-07-08 19:46:53,460 - INFO - 执行详细分析: callId=4075
2025-07-08 19:46:53,522 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\4-4075.txt
2025-07-08 19:46:53,523 - INFO - 处理 callId=9748, callTime=25.40625ms, rank=5
2025-07-08 19:46:53,523 - INFO - 执行详细分析: callId=9748
2025-07-08 19:46:53,555 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\5-9748.txt
2025-07-08 19:46:53,555 - INFO - 处理 callId=83284, callTime=16.584688ms, rank=6
2025-07-08 19:46:53,555 - INFO - 执行详细分析: callId=83284
2025-07-08 19:46:53,626 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\6-83284.txt
2025-07-08 19:46:53,627 - INFO - 处理 callId=48541, callTime=15.522343ms, rank=7
2025-07-08 19:46:53,627 - INFO - 执行详细分析: callId=48541
2025-07-08 19:46:53,680 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\7-48541.txt
2025-07-08 19:46:53,680 - INFO - 处理 callId=51282, callTime=15.477344ms, rank=8
2025-07-08 19:46:53,680 - INFO - 执行详细分析: callId=51282
2025-07-08 19:46:53,760 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\8-51282.txt
2025-07-08 19:46:53,760 - INFO - 处理 callId=8195, callTime=10.34875ms, rank=9
2025-07-08 19:46:53,760 - INFO - 执行详细分析: callId=8195
2025-07-08 19:46:53,809 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\9-8195.txt
2025-07-08 19:46:53,810 - INFO - 处理 callId=47203, callTime=9.964375ms, rank=10
2025-07-08 19:46:53,810 - INFO - 执行详细分析: callId=47203
2025-07-08 19:46:53,856 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\10-47203.txt
2025-07-08 19:46:53,856 - INFO - 处理 callId=65886, callTime=9.93875ms, rank=11
2025-07-08 19:46:53,856 - INFO - 执行详细分析: callId=65886
2025-07-08 19:46:53,913 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\11-65886.txt
2025-07-08 19:46:53,913 - INFO - 处理 callId=59412, callTime=9.745938ms, rank=12
2025-07-08 19:46:53,913 - INFO - 执行详细分析: callId=59412
2025-07-08 19:46:53,966 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\12-59412.txt
2025-07-08 19:46:53,966 - INFO - 处理 callId=79302, callTime=9.325625ms, rank=13
2025-07-08 19:46:53,966 - INFO - 执行详细分析: callId=79302
2025-07-08 19:46:54,034 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\13-79302.txt
2025-07-08 19:46:54,034 - INFO - 处理 callId=76338, callTime=9.254062ms, rank=14
2025-07-08 19:46:54,034 - INFO - 执行详细分析: callId=76338
2025-07-08 19:46:54,100 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\14-76338.txt
2025-07-08 19:46:54,100 - INFO - 处理 callId=24487, callTime=9.2525ms, rank=15
2025-07-08 19:46:54,100 - INFO - 执行详细分析: callId=24487
2025-07-08 19:46:54,156 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\15-24487.txt
2025-07-08 19:46:54,156 - INFO - 处理 callId=78834, callTime=8.869063ms, rank=16
2025-07-08 19:46:54,156 - INFO - 执行详细分析: callId=78834
2025-07-08 19:46:54,223 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\16-78834.txt
2025-07-08 19:46:54,223 - INFO - 处理 callId=25659, callTime=8.689219ms, rank=17
2025-07-08 19:46:54,223 - INFO - 执行详细分析: callId=25659
2025-07-08 19:46:54,277 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\17-25659.txt
2025-07-08 19:46:54,277 - INFO - 处理 callId=75948, callTime=8.579844ms, rank=18
2025-07-08 19:46:54,277 - INFO - 执行详细分析: callId=75948
2025-07-08 19:46:54,341 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\18-75948.txt
2025-07-08 19:46:54,341 - INFO - 处理 callId=6306, callTime=8.509843ms, rank=19
2025-07-08 19:46:54,341 - INFO - 执行详细分析: callId=6306
2025-07-08 19:46:54,407 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\19-6306.txt
2025-07-08 19:46:54,407 - INFO - 处理 callId=67602, callTime=8.396094ms, rank=20
2025-07-08 19:46:54,407 - INFO - 执行详细分析: callId=67602
2025-07-08 19:46:54,465 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\20-67602.txt
2025-07-08 19:46:54,465 - INFO - 处理 callId=67290, callTime=8.35ms, rank=21
2025-07-08 19:46:54,466 - INFO - 执行详细分析: callId=67290
2025-07-08 19:46:54,524 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\21-67290.txt
2025-07-08 19:46:54,524 - INFO - 处理 callId=75324, callTime=8.339844ms, rank=22
2025-07-08 19:46:54,524 - INFO - 执行详细分析: callId=75324
2025-07-08 19:46:54,588 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\22-75324.txt
2025-07-08 19:46:54,588 - INFO - 处理 callId=70254, callTime=8.309218ms, rank=23
2025-07-08 19:46:54,588 - INFO - 执行详细分析: callId=70254
2025-07-08 19:46:54,648 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\23-70254.txt
2025-07-08 19:46:54,648 - INFO - 处理 callId=62298, callTime=8.243125ms, rank=24
2025-07-08 19:46:54,649 - INFO - 执行详细分析: callId=62298
2025-07-08 19:46:54,704 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\24-62298.txt
2025-07-08 19:46:54,704 - INFO - 处理 callId=80550, callTime=8.223125ms, rank=25
2025-07-08 19:46:54,704 - INFO - 执行详细分析: callId=80550
2025-07-08 19:46:54,773 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\25-80550.txt
2025-07-08 19:46:54,773 - INFO - 处理 callId=73998, callTime=8.204688ms, rank=26
2025-07-08 19:46:54,773 - INFO - 执行详细分析: callId=73998
2025-07-08 19:46:54,836 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\26-73998.txt
2025-07-08 19:46:54,836 - INFO - 处理 callId=64716, callTime=8.1875ms, rank=27
2025-07-08 19:46:54,837 - INFO - 执行详细分析: callId=64716
2025-07-08 19:46:54,900 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\27-64716.txt
2025-07-08 19:46:54,900 - INFO - 处理 callId=74544, callTime=8.155938ms, rank=28
2025-07-08 19:46:54,900 - INFO - 执行详细分析: callId=74544
2025-07-08 19:46:54,963 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\28-74544.txt
2025-07-08 19:46:54,964 - INFO - 处理 callId=67056, callTime=8.130312ms, rank=29
2025-07-08 19:46:54,964 - INFO - 执行详细分析: callId=67056
2025-07-08 19:46:55,022 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\29-67056.txt
2025-07-08 19:46:55,022 - INFO - 处理 callId=77508, callTime=8.100625ms, rank=30
2025-07-08 19:46:55,022 - INFO - 执行详细分析: callId=77508
2025-07-08 19:46:55,088 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\30-77508.txt
2025-07-08 19:46:55,088 - INFO - 处理 callId=80394, callTime=8.059844ms, rank=31
2025-07-08 19:46:55,088 - INFO - 执行详细分析: callId=80394
2025-07-08 19:46:55,157 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\31-80394.txt
2025-07-08 19:46:55,157 - INFO - 处理 callId=71112, callTime=8.049844ms, rank=32
2025-07-08 19:46:55,157 - INFO - 执行详细分析: callId=71112
2025-07-08 19:46:55,219 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\32-71112.txt
2025-07-08 19:46:55,219 - INFO - 处理 callId=69084, callTime=8.02875ms, rank=33
2025-07-08 19:46:55,219 - INFO - 执行详细分析: callId=69084
2025-07-08 19:46:55,279 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\33-69084.txt
2025-07-08 19:46:55,279 - INFO - 处理 callId=75090, callTime=7.99125ms, rank=34
2025-07-08 19:46:55,279 - INFO - 执行详细分析: callId=75090
2025-07-08 19:46:55,343 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\34-75090.txt
2025-07-08 19:46:55,343 - INFO - 处理 callId=61596, callTime=7.964532ms, rank=35
2025-07-08 19:46:55,343 - INFO - 执行详细分析: callId=61596
2025-07-08 19:46:55,398 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\35-61596.txt
2025-07-08 19:46:55,398 - INFO - 处理 callId=74076, callTime=7.96375ms, rank=36
2025-07-08 19:46:55,398 - INFO - 执行详细分析: callId=74076
2025-07-08 19:46:55,462 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\36-74076.txt
2025-07-08 19:46:55,462 - INFO - 处理 callId=75636, callTime=7.958907ms, rank=37
2025-07-08 19:46:55,462 - INFO - 执行详细分析: callId=75636
2025-07-08 19:46:55,527 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\37-75636.txt
2025-07-08 19:46:55,527 - INFO - 处理 callId=81468, callTime=7.915468ms, rank=38
2025-07-08 19:46:55,527 - INFO - 执行详细分析: callId=81468
2025-07-08 19:46:55,596 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\38-81468.txt
2025-07-08 19:46:55,596 - INFO - 处理 callId=45291, callTime=7.887968ms, rank=39
2025-07-08 19:46:55,596 - INFO - 执行详细分析: callId=45291
2025-07-08 19:46:55,640 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\39-45291.txt
2025-07-08 19:46:55,640 - INFO - 处理 callId=68148, callTime=7.885312ms, rank=40
2025-07-08 19:46:55,640 - INFO - 执行详细分析: callId=68148
2025-07-08 19:46:55,700 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_102653_wpp动画播放对象动画\40-68148.txt
2025-07-08 19:46:55,700 - INFO - 完成处理文件: perfmon-angle-20250707_102653_wpp动画播放对象动画.dat
2025-07-08 19:46:55,700 - INFO - 处理进度: 7/30
2025-07-08 19:46:55,700 - INFO - 开始处理文件: perfmon-angle-20250707_103437_wpp动画页切换.dat
2025-07-08 19:46:55,700 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-angle-20250707_103437_wpp动画页切换.dat
2025-07-08 19:46:55,877 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-angle-20250707_103437_wpp动画页切换.dat.txt
2025-07-08 19:46:55,877 - INFO - 找到event函数: E:\wpsskia\qt\source\qtbase\src\widgets\kernel\qwidget.cpp:9682 event
2025-07-08 19:46:55,877 - INFO - 提取callId: 37385, callTime: 199.263125ms
2025-07-08 19:46:55,877 - INFO - 提取callId: 40975, callTime: 195.043906ms
2025-07-08 19:46:55,877 - INFO - 提取callId: 33780, callTime: 165.033594ms
2025-07-08 19:46:55,878 - INFO - 提取callId: 43150, callTime: 58.15375ms
2025-07-08 19:46:55,878 - INFO - 提取callId: 58504, callTime: 49.572812ms
2025-07-08 19:46:55,878 - INFO - 提取callId: 24086, callTime: 45.75875ms
2025-07-08 19:46:55,878 - INFO - 提取callId: 19131, callTime: 29.954532ms
2025-07-08 19:46:55,878 - INFO - 提取callId: 1642, callTime: 27.786407ms
2025-07-08 19:46:55,878 - INFO - 提取callId: 12095, callTime: 22.509688ms
2025-07-08 19:46:55,878 - INFO - 提取callId: 9748, callTime: 19.577656ms
2025-07-08 19:46:55,878 - INFO - 提取callId: 80147, callTime: 18.032656ms
2025-07-08 19:46:55,878 - INFO - 提取callId: 15343, callTime: 15.425ms
2025-07-08 19:46:55,878 - INFO - 提取callId: 59851, callTime: 15.374843ms
2025-07-08 19:46:55,878 - INFO - 提取callId: 80117, callTime: 14.89625ms
2025-07-08 19:46:55,878 - INFO - 提取callId: 80207, callTime: 14.171718ms
2025-07-08 19:46:55,878 - INFO - 提取callId: 6306, callTime: 13.919375ms
2025-07-08 19:46:55,878 - INFO - 提取callId: 80327, callTime: 13.713125ms
2025-07-08 19:46:55,878 - INFO - 提取callId: 80297, callTime: 13.514688ms
2025-07-08 19:46:55,878 - INFO - 提取callId: 80267, callTime: 13.393125ms
2025-07-08 19:46:55,878 - INFO - 提取callId: 80177, callTime: 13.192344ms
2025-07-08 19:46:55,879 - INFO - 提取callId: 80387, callTime: 12.860781ms
2025-07-08 19:46:55,879 - INFO - 提取callId: 80357, callTime: 12.814219ms
2025-07-08 19:46:55,879 - INFO - 提取callId: 80237, callTime: 12.6025ms
2025-07-08 19:46:55,879 - INFO - 提取callId: 16430, callTime: 12.58125ms
2025-07-08 19:46:55,879 - INFO - 提取callId: 80447, callTime: 12.578437ms
2025-07-08 19:46:55,879 - INFO - 提取callId: 64965, callTime: 12.448438ms
2025-07-08 19:46:55,879 - INFO - 提取callId: 80477, callTime: 12.404844ms
2025-07-08 19:46:55,879 - INFO - 提取callId: 81467, callTime: 12.395625ms
2025-07-08 19:46:55,879 - INFO - 提取callId: 63154, callTime: 12.324844ms
2025-07-08 19:46:55,879 - INFO - 提取callId: 80417, callTime: 11.887969ms
2025-07-08 19:46:55,879 - INFO - 提取callId: 4075, callTime: 11.768438ms
2025-07-08 19:46:55,879 - INFO - 提取callId: 80537, callTime: 11.75125ms
2025-07-08 19:46:55,879 - INFO - 提取callId: 80507, callTime: 11.629687ms
2025-07-08 19:46:55,879 - INFO - 提取callId: 18741, callTime: 11.52625ms
2025-07-08 19:46:55,879 - INFO - 提取callId: 64878, callTime: 11.440469ms
2025-07-08 19:46:55,879 - INFO - 提取callId: 80627, callTime: 11.411563ms
2025-07-08 19:46:55,880 - INFO - 提取callId: 61291, callTime: 11.000156ms
2025-07-08 19:46:55,880 - INFO - 提取callId: 80717, callTime: 10.954375ms
2025-07-08 19:46:55,880 - INFO - 提取callId: 80597, callTime: 10.876094ms
2025-07-08 19:46:55,880 - INFO - 提取callId: 82817, callTime: 10.648906ms
2025-07-08 19:46:55,880 - INFO - 提取到 40 个event相关的callId
2025-07-08 19:46:55,880 - INFO - 处理 callId=37385, callTime=199.263125ms, rank=1
2025-07-08 19:46:55,880 - INFO - 执行详细分析: callId=37385
2025-07-08 19:46:56,070 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\1-37385.txt
2025-07-08 19:46:56,070 - INFO - 处理 callId=40975, callTime=195.043906ms, rank=2
2025-07-08 19:46:56,070 - INFO - 执行详细分析: callId=40975
2025-07-08 19:46:56,175 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\2-40975.txt
2025-07-08 19:46:56,175 - INFO - 处理 callId=33780, callTime=165.033594ms, rank=3
2025-07-08 19:46:56,176 - INFO - 执行详细分析: callId=33780
2025-07-08 19:46:56,360 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\3-33780.txt
2025-07-08 19:46:56,360 - INFO - 处理 callId=43150, callTime=58.15375ms, rank=4
2025-07-08 19:46:56,360 - INFO - 执行详细分析: callId=43150
2025-07-08 19:46:56,433 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\4-43150.txt
2025-07-08 19:46:56,433 - INFO - 处理 callId=58504, callTime=49.572812ms, rank=5
2025-07-08 19:46:56,433 - INFO - 执行详细分析: callId=58504
2025-07-08 19:46:56,503 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\5-58504.txt
2025-07-08 19:46:56,503 - INFO - 处理 callId=24086, callTime=45.75875ms, rank=6
2025-07-08 19:46:56,503 - INFO - 执行详细分析: callId=24086
2025-07-08 19:46:56,788 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\6-24086.txt
2025-07-08 19:46:56,788 - INFO - 处理 callId=19131, callTime=29.954532ms, rank=7
2025-07-08 19:46:56,788 - INFO - 执行详细分析: callId=19131
2025-07-08 19:46:57,060 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\7-19131.txt
2025-07-08 19:46:57,061 - INFO - 处理 callId=1642, callTime=27.786407ms, rank=8
2025-07-08 19:46:57,061 - INFO - 执行详细分析: callId=1642
2025-07-08 19:46:57,132 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\8-1642.txt
2025-07-08 19:46:57,133 - INFO - 处理 callId=12095, callTime=22.509688ms, rank=9
2025-07-08 19:46:57,133 - INFO - 执行详细分析: callId=12095
2025-07-08 19:46:57,170 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\9-12095.txt
2025-07-08 19:46:57,170 - INFO - 处理 callId=9748, callTime=19.577656ms, rank=10
2025-07-08 19:46:57,170 - INFO - 执行详细分析: callId=9748
2025-07-08 19:46:57,203 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\10-9748.txt
2025-07-08 19:46:57,203 - INFO - 处理 callId=80147, callTime=18.032656ms, rank=11
2025-07-08 19:46:57,203 - INFO - 执行详细分析: callId=80147
2025-07-08 19:46:57,271 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\11-80147.txt
2025-07-08 19:46:57,271 - INFO - 处理 callId=15343, callTime=15.425ms, rank=12
2025-07-08 19:46:57,271 - INFO - 执行详细分析: callId=15343
2025-07-08 19:46:57,311 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\12-15343.txt
2025-07-08 19:46:57,312 - INFO - 处理 callId=59851, callTime=15.374843ms, rank=13
2025-07-08 19:46:57,312 - INFO - 执行详细分析: callId=59851
2025-07-08 19:46:57,374 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\13-59851.txt
2025-07-08 19:46:57,375 - INFO - 处理 callId=80117, callTime=14.89625ms, rank=14
2025-07-08 19:46:57,375 - INFO - 执行详细分析: callId=80117
2025-07-08 19:46:57,443 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\14-80117.txt
2025-07-08 19:46:57,443 - INFO - 处理 callId=80207, callTime=14.171718ms, rank=15
2025-07-08 19:46:57,443 - INFO - 执行详细分析: callId=80207
2025-07-08 19:46:57,511 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\15-80207.txt
2025-07-08 19:46:57,512 - INFO - 处理 callId=6306, callTime=13.919375ms, rank=16
2025-07-08 19:46:57,512 - INFO - 执行详细分析: callId=6306
2025-07-08 19:46:57,574 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\16-6306.txt
2025-07-08 19:46:57,574 - INFO - 处理 callId=80327, callTime=13.713125ms, rank=17
2025-07-08 19:46:57,574 - INFO - 执行详细分析: callId=80327
2025-07-08 19:46:57,642 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\17-80327.txt
2025-07-08 19:46:57,642 - INFO - 处理 callId=80297, callTime=13.514688ms, rank=18
2025-07-08 19:46:57,642 - INFO - 执行详细分析: callId=80297
2025-07-08 19:46:57,711 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\18-80297.txt
2025-07-08 19:46:57,711 - INFO - 处理 callId=80267, callTime=13.393125ms, rank=19
2025-07-08 19:46:57,711 - INFO - 执行详细分析: callId=80267
2025-07-08 19:46:57,779 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\19-80267.txt
2025-07-08 19:46:57,779 - INFO - 处理 callId=80177, callTime=13.192344ms, rank=20
2025-07-08 19:46:57,779 - INFO - 执行详细分析: callId=80177
2025-07-08 19:46:57,847 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\20-80177.txt
2025-07-08 19:46:57,847 - INFO - 处理 callId=80387, callTime=12.860781ms, rank=21
2025-07-08 19:46:57,847 - INFO - 执行详细分析: callId=80387
2025-07-08 19:46:57,915 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\21-80387.txt
2025-07-08 19:46:57,915 - INFO - 处理 callId=80357, callTime=12.814219ms, rank=22
2025-07-08 19:46:57,915 - INFO - 执行详细分析: callId=80357
2025-07-08 19:46:57,983 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\22-80357.txt
2025-07-08 19:46:57,983 - INFO - 处理 callId=80237, callTime=12.6025ms, rank=23
2025-07-08 19:46:57,983 - INFO - 执行详细分析: callId=80237
2025-07-08 19:46:58,052 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\23-80237.txt
2025-07-08 19:46:58,052 - INFO - 处理 callId=16430, callTime=12.58125ms, rank=24
2025-07-08 19:46:58,052 - INFO - 执行详细分析: callId=16430
2025-07-08 19:46:58,093 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\24-16430.txt
2025-07-08 19:46:58,093 - INFO - 处理 callId=80447, callTime=12.578437ms, rank=25
2025-07-08 19:46:58,093 - INFO - 执行详细分析: callId=80447
2025-07-08 19:46:58,162 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\25-80447.txt
2025-07-08 19:46:58,162 - INFO - 处理 callId=64965, callTime=12.448438ms, rank=26
2025-07-08 19:46:58,162 - INFO - 执行详细分析: callId=64965
2025-07-08 19:46:58,222 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\26-64965.txt
2025-07-08 19:46:58,222 - INFO - 处理 callId=80477, callTime=12.404844ms, rank=27
2025-07-08 19:46:58,222 - INFO - 执行详细分析: callId=80477
2025-07-08 19:46:58,292 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\27-80477.txt
2025-07-08 19:46:58,292 - INFO - 处理 callId=81467, callTime=12.395625ms, rank=28
2025-07-08 19:46:58,292 - INFO - 执行详细分析: callId=81467
2025-07-08 19:46:58,361 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\28-81467.txt
2025-07-08 19:46:58,361 - INFO - 处理 callId=63154, callTime=12.324844ms, rank=29
2025-07-08 19:46:58,361 - INFO - 执行详细分析: callId=63154
2025-07-08 19:46:58,464 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\29-63154.txt
2025-07-08 19:46:58,464 - INFO - 处理 callId=80417, callTime=11.887969ms, rank=30
2025-07-08 19:46:58,464 - INFO - 执行详细分析: callId=80417
2025-07-08 19:46:58,532 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\30-80417.txt
2025-07-08 19:46:58,532 - INFO - 处理 callId=4075, callTime=11.768438ms, rank=31
2025-07-08 19:46:58,532 - INFO - 执行详细分析: callId=4075
2025-07-08 19:46:58,596 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\31-4075.txt
2025-07-08 19:46:58,596 - INFO - 处理 callId=80537, callTime=11.75125ms, rank=32
2025-07-08 19:46:58,596 - INFO - 执行详细分析: callId=80537
2025-07-08 19:46:58,665 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\32-80537.txt
2025-07-08 19:46:58,665 - INFO - 处理 callId=80507, callTime=11.629687ms, rank=33
2025-07-08 19:46:58,665 - INFO - 执行详细分析: callId=80507
2025-07-08 19:46:58,733 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\33-80507.txt
2025-07-08 19:46:58,733 - INFO - 处理 callId=18741, callTime=11.52625ms, rank=34
2025-07-08 19:46:58,733 - INFO - 执行详细分析: callId=18741
2025-07-08 19:46:58,759 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\34-18741.txt
2025-07-08 19:46:58,759 - INFO - 处理 callId=64878, callTime=11.440469ms, rank=35
2025-07-08 19:46:58,759 - INFO - 执行详细分析: callId=64878
2025-07-08 19:46:58,817 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\35-64878.txt
2025-07-08 19:46:58,817 - INFO - 处理 callId=80627, callTime=11.411563ms, rank=36
2025-07-08 19:46:58,817 - INFO - 执行详细分析: callId=80627
2025-07-08 19:46:58,885 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\36-80627.txt
2025-07-08 19:46:58,886 - INFO - 处理 callId=61291, callTime=11.000156ms, rank=37
2025-07-08 19:46:58,886 - INFO - 执行详细分析: callId=61291
2025-07-08 19:46:58,942 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\37-61291.txt
2025-07-08 19:46:58,942 - INFO - 处理 callId=80717, callTime=10.954375ms, rank=38
2025-07-08 19:46:58,942 - INFO - 执行详细分析: callId=80717
2025-07-08 19:46:59,011 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\38-80717.txt
2025-07-08 19:46:59,011 - INFO - 处理 callId=80597, callTime=10.876094ms, rank=39
2025-07-08 19:46:59,011 - INFO - 执行详细分析: callId=80597
2025-07-08 19:46:59,080 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\39-80597.txt
2025-07-08 19:46:59,080 - INFO - 处理 callId=82817, callTime=10.648906ms, rank=40
2025-07-08 19:46:59,080 - INFO - 执行详细分析: callId=82817
2025-07-08 19:46:59,150 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103437_wpp动画页切换\40-82817.txt
2025-07-08 19:46:59,151 - INFO - 完成处理文件: perfmon-angle-20250707_103437_wpp动画页切换.dat
2025-07-08 19:46:59,151 - INFO - 处理进度: 8/30
2025-07-08 19:46:59,151 - INFO - 开始处理文件: perfmon-angle-20250707_103855_wpp动画测试荧光笔.dat
2025-07-08 19:46:59,151 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-angle-20250707_103855_wpp动画测试荧光笔.dat
2025-07-08 19:46:59,612 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-angle-20250707_103855_wpp动画测试荧光笔.dat.txt
2025-07-08 19:46:59,612 - INFO - 找到event函数: E:\wpsskia\qt\source\qtbase\src\widgets\kernel\qwidget.cpp:9682 event
2025-07-08 19:46:59,612 - INFO - 提取callId: 55757, callTime: 130.067344ms
2025-07-08 19:46:59,613 - INFO - 提取callId: 32432, callTime: 85.021407ms
2025-07-08 19:46:59,613 - INFO - 提取callId: 28819, callTime: 79.192656ms
2025-07-08 19:46:59,613 - INFO - 提取callId: 36235, callTime: 51.770312ms
2025-07-08 19:46:59,613 - INFO - 提取callId: 49700, callTime: 44.245312ms
2025-07-08 19:46:59,613 - INFO - 提取callId: 19128, callTime: 32.645156ms
2025-07-08 19:46:59,613 - INFO - 提取callId: 1642, callTime: 27.847188ms
2025-07-08 19:46:59,613 - INFO - 提取callId: 66859, callTime: 23.580313ms
2025-07-08 19:46:59,613 - INFO - 提取callId: 4075, callTime: 20.357812ms
2025-07-08 19:46:59,613 - INFO - 提取callId: 9748, callTime: 16.01875ms
2025-07-08 19:46:59,614 - INFO - 提取callId: 53154, callTime: 14.998906ms
2025-07-08 19:46:59,614 - INFO - 提取callId: 66575, callTime: 14.249531ms
2025-07-08 19:46:59,614 - INFO - 提取callId: 76534, callTime: 13.175156ms
2025-07-08 19:46:59,614 - INFO - 提取callId: 38414, callTime: 12.644062ms
2025-07-08 19:46:59,614 - INFO - 提取callId: 58025, callTime: 12.150469ms
2025-07-08 19:46:59,614 - INFO - 提取callId: 76686, callTime: 11.657344ms
2025-07-08 19:46:59,614 - INFO - 提取callId: 6306, callTime: 11.610781ms
2025-07-08 19:46:59,614 - INFO - 提取callId: 62433, callTime: 11.589844ms
2025-07-08 19:46:59,614 - INFO - 提取callId: 77066, callTime: 11.567813ms
2025-07-08 19:46:59,614 - INFO - 提取callId: 76800, callTime: 11.50125ms
2025-07-08 19:46:59,615 - INFO - 提取callId: 47498, callTime: 11.494688ms
2025-07-08 19:46:59,615 - INFO - 提取callId: 66299, callTime: 11.370625ms
2025-07-08 19:46:59,615 - INFO - 提取callId: 76990, callTime: 10.904688ms
2025-07-08 19:46:59,615 - INFO - 提取callId: 76914, callTime: 10.814532ms
2025-07-08 19:46:59,615 - INFO - 提取callId: 44758, callTime: 10.522657ms
2025-07-08 19:46:59,615 - INFO - 提取callId: 76952, callTime: 10.450313ms
2025-07-08 19:46:59,615 - INFO - 提取callId: 60189, callTime: 10.441407ms
2025-07-08 19:46:59,615 - INFO - 提取callId: 59545, callTime: 10.396406ms
2025-07-08 19:46:59,615 - INFO - 提取callId: 77104, callTime: 10.232969ms
2025-07-08 19:46:59,615 - INFO - 提取callId: 77940, callTime: 10.052343ms
2025-07-08 19:46:59,615 - INFO - 提取callId: 67576, callTime: 10.0225ms
2025-07-08 19:46:59,615 - INFO - 提取callId: 48122, callTime: 9.992812ms
2025-07-08 19:46:59,615 - INFO - 提取callId: 79764, callTime: 9.991875ms
2025-07-08 19:46:59,616 - INFO - 提取callId: 78472, callTime: 9.87375ms
2025-07-08 19:46:59,616 - INFO - 提取callId: 77028, callTime: 9.85ms
2025-07-08 19:46:59,616 - INFO - 提取callId: 76610, callTime: 9.819375ms
2025-07-08 19:46:59,616 - INFO - 提取callId: 71898, callTime: 9.782031ms
2025-07-08 19:46:59,616 - INFO - 提取callId: 76724, callTime: 9.731875ms
2025-07-08 19:46:59,616 - INFO - 提取callId: 79612, callTime: 9.721718ms
2025-07-08 19:46:59,616 - INFO - 提取callId: 70302, callTime: 9.656719ms
2025-07-08 19:46:59,616 - INFO - 提取到 40 个event相关的callId
2025-07-08 19:46:59,616 - INFO - 处理 callId=55757, callTime=130.067344ms, rank=1
2025-07-08 19:46:59,617 - INFO - 执行详细分析: callId=55757
2025-07-08 19:46:59,681 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\1-55757.txt
2025-07-08 19:46:59,681 - INFO - 处理 callId=32432, callTime=85.021407ms, rank=2
2025-07-08 19:46:59,681 - INFO - 执行详细分析: callId=32432
2025-07-08 19:46:59,871 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\2-32432.txt
2025-07-08 19:46:59,871 - INFO - 处理 callId=28819, callTime=79.192656ms, rank=3
2025-07-08 19:46:59,871 - INFO - 执行详细分析: callId=28819
2025-07-08 19:47:00,053 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\3-28819.txt
2025-07-08 19:47:00,054 - INFO - 处理 callId=36235, callTime=51.770312ms, rank=4
2025-07-08 19:47:00,054 - INFO - 执行详细分析: callId=36235
2025-07-08 19:47:00,155 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\4-36235.txt
2025-07-08 19:47:00,156 - INFO - 处理 callId=49700, callTime=44.245312ms, rank=5
2025-07-08 19:47:00,156 - INFO - 执行详细分析: callId=49700
2025-07-08 19:47:00,220 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\5-49700.txt
2025-07-08 19:47:00,220 - INFO - 处理 callId=19128, callTime=32.645156ms, rank=6
2025-07-08 19:47:00,220 - INFO - 执行详细分析: callId=19128
2025-07-08 19:47:00,496 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\6-19128.txt
2025-07-08 19:47:00,496 - INFO - 处理 callId=1642, callTime=27.847188ms, rank=7
2025-07-08 19:47:00,496 - INFO - 执行详细分析: callId=1642
2025-07-08 19:47:00,568 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\7-1642.txt
2025-07-08 19:47:00,568 - INFO - 处理 callId=66859, callTime=23.580313ms, rank=8
2025-07-08 19:47:00,568 - INFO - 执行详细分析: callId=66859
2025-07-08 19:47:00,631 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\8-66859.txt
2025-07-08 19:47:00,631 - INFO - 处理 callId=4075, callTime=20.357812ms, rank=9
2025-07-08 19:47:00,631 - INFO - 执行详细分析: callId=4075
2025-07-08 19:47:00,694 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\9-4075.txt
2025-07-08 19:47:00,694 - INFO - 处理 callId=9748, callTime=16.01875ms, rank=10
2025-07-08 19:47:00,694 - INFO - 执行详细分析: callId=9748
2025-07-08 19:47:00,728 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\10-9748.txt
2025-07-08 19:47:00,728 - INFO - 处理 callId=53154, callTime=14.998906ms, rank=11
2025-07-08 19:47:00,728 - INFO - 执行详细分析: callId=53154
2025-07-08 19:47:00,786 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\11-53154.txt
2025-07-08 19:47:00,786 - INFO - 处理 callId=66575, callTime=14.249531ms, rank=12
2025-07-08 19:47:00,786 - INFO - 执行详细分析: callId=66575
2025-07-08 19:47:00,847 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\12-66575.txt
2025-07-08 19:47:00,847 - INFO - 处理 callId=76534, callTime=13.175156ms, rank=13
2025-07-08 19:47:00,847 - INFO - 执行详细分析: callId=76534
2025-07-08 19:47:00,914 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\13-76534.txt
2025-07-08 19:47:00,914 - INFO - 处理 callId=38414, callTime=12.644062ms, rank=14
2025-07-08 19:47:00,914 - INFO - 执行详细分析: callId=38414
2025-07-08 19:47:00,985 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\14-38414.txt
2025-07-08 19:47:00,985 - INFO - 处理 callId=58025, callTime=12.150469ms, rank=15
2025-07-08 19:47:00,985 - INFO - 执行详细分析: callId=58025
2025-07-08 19:47:01,084 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\15-58025.txt
2025-07-08 19:47:01,085 - INFO - 处理 callId=76686, callTime=11.657344ms, rank=16
2025-07-08 19:47:01,085 - INFO - 执行详细分析: callId=76686
2025-07-08 19:47:01,151 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\16-76686.txt
2025-07-08 19:47:01,151 - INFO - 处理 callId=6306, callTime=11.610781ms, rank=17
2025-07-08 19:47:01,151 - INFO - 执行详细分析: callId=6306
2025-07-08 19:47:01,215 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\17-6306.txt
2025-07-08 19:47:01,215 - INFO - 处理 callId=62433, callTime=11.589844ms, rank=18
2025-07-08 19:47:01,215 - INFO - 执行详细分析: callId=62433
2025-07-08 19:47:01,271 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\18-62433.txt
2025-07-08 19:47:01,272 - INFO - 处理 callId=77066, callTime=11.567813ms, rank=19
2025-07-08 19:47:01,272 - INFO - 执行详细分析: callId=77066
2025-07-08 19:47:01,339 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\19-77066.txt
2025-07-08 19:47:01,339 - INFO - 处理 callId=76800, callTime=11.50125ms, rank=20
2025-07-08 19:47:01,339 - INFO - 执行详细分析: callId=76800
2025-07-08 19:47:01,405 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\20-76800.txt
2025-07-08 19:47:01,405 - INFO - 处理 callId=47498, callTime=11.494688ms, rank=21
2025-07-08 19:47:01,405 - INFO - 执行详细分析: callId=47498
2025-07-08 19:47:01,451 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\21-47498.txt
2025-07-08 19:47:01,452 - INFO - 处理 callId=66299, callTime=11.370625ms, rank=22
2025-07-08 19:47:01,452 - INFO - 执行详细分析: callId=66299
2025-07-08 19:47:01,513 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\22-66299.txt
2025-07-08 19:47:01,514 - INFO - 处理 callId=76990, callTime=10.904688ms, rank=23
2025-07-08 19:47:01,514 - INFO - 执行详细分析: callId=76990
2025-07-08 19:47:01,579 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\23-76990.txt
2025-07-08 19:47:01,579 - INFO - 处理 callId=76914, callTime=10.814532ms, rank=24
2025-07-08 19:47:01,579 - INFO - 执行详细分析: callId=76914
2025-07-08 19:47:01,645 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\24-76914.txt
2025-07-08 19:47:01,646 - INFO - 处理 callId=44758, callTime=10.522657ms, rank=25
2025-07-08 19:47:01,646 - INFO - 执行详细分析: callId=44758
2025-07-08 19:47:01,706 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\25-44758.txt
2025-07-08 19:47:01,706 - INFO - 处理 callId=76952, callTime=10.450313ms, rank=26
2025-07-08 19:47:01,706 - INFO - 执行详细分析: callId=76952
2025-07-08 19:47:01,772 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\26-76952.txt
2025-07-08 19:47:01,772 - INFO - 处理 callId=60189, callTime=10.441407ms, rank=27
2025-07-08 19:47:01,773 - INFO - 执行详细分析: callId=60189
2025-07-08 19:47:01,827 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\27-60189.txt
2025-07-08 19:47:01,828 - INFO - 处理 callId=59545, callTime=10.396406ms, rank=28
2025-07-08 19:47:01,828 - INFO - 执行详细分析: callId=59545
2025-07-08 19:47:01,889 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\28-59545.txt
2025-07-08 19:47:01,890 - INFO - 处理 callId=77104, callTime=10.232969ms, rank=29
2025-07-08 19:47:01,890 - INFO - 执行详细分析: callId=77104
2025-07-08 19:47:01,956 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\29-77104.txt
2025-07-08 19:47:01,956 - INFO - 处理 callId=77940, callTime=10.052343ms, rank=30
2025-07-08 19:47:01,956 - INFO - 执行详细分析: callId=77940
2025-07-08 19:47:02,023 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\30-77940.txt
2025-07-08 19:47:02,023 - INFO - 处理 callId=67576, callTime=10.0225ms, rank=31
2025-07-08 19:47:02,023 - INFO - 执行详细分析: callId=67576
2025-07-08 19:47:02,083 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\31-67576.txt
2025-07-08 19:47:02,083 - INFO - 处理 callId=48122, callTime=9.992812ms, rank=32
2025-07-08 19:47:02,083 - INFO - 执行详细分析: callId=48122
2025-07-08 19:47:02,131 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\32-48122.txt
2025-07-08 19:47:02,131 - INFO - 处理 callId=79764, callTime=9.991875ms, rank=33
2025-07-08 19:47:02,131 - INFO - 执行详细分析: callId=79764
2025-07-08 19:47:02,199 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\33-79764.txt
2025-07-08 19:47:02,199 - INFO - 处理 callId=78472, callTime=9.87375ms, rank=34
2025-07-08 19:47:02,199 - INFO - 执行详细分析: callId=78472
2025-07-08 19:47:02,266 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\34-78472.txt
2025-07-08 19:47:02,266 - INFO - 处理 callId=77028, callTime=9.85ms, rank=35
2025-07-08 19:47:02,266 - INFO - 执行详细分析: callId=77028
2025-07-08 19:47:02,332 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\35-77028.txt
2025-07-08 19:47:02,332 - INFO - 处理 callId=76610, callTime=9.819375ms, rank=36
2025-07-08 19:47:02,332 - INFO - 执行详细分析: callId=76610
2025-07-08 19:47:02,400 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\36-76610.txt
2025-07-08 19:47:02,400 - INFO - 处理 callId=71898, callTime=9.782031ms, rank=37
2025-07-08 19:47:02,400 - INFO - 执行详细分析: callId=71898
2025-07-08 19:47:02,464 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\37-71898.txt
2025-07-08 19:47:02,464 - INFO - 处理 callId=76724, callTime=9.731875ms, rank=38
2025-07-08 19:47:02,464 - INFO - 执行详细分析: callId=76724
2025-07-08 19:47:02,531 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\38-76724.txt
2025-07-08 19:47:02,532 - INFO - 处理 callId=79612, callTime=9.721718ms, rank=39
2025-07-08 19:47:02,532 - INFO - 执行详细分析: callId=79612
2025-07-08 19:47:02,600 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\39-79612.txt
2025-07-08 19:47:02,600 - INFO - 处理 callId=70302, callTime=9.656719ms, rank=40
2025-07-08 19:47:02,600 - INFO - 执行详细分析: callId=70302
2025-07-08 19:47:02,662 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_103855_wpp动画测试荧光笔\40-70302.txt
2025-07-08 19:47:02,662 - INFO - 完成处理文件: perfmon-angle-20250707_103855_wpp动画测试荧光笔.dat
2025-07-08 19:47:02,662 - INFO - 处理进度: 9/30
2025-07-08 19:47:02,663 - INFO - 开始处理文件: perfmon-angle-20250707_155842_wps文档绘制.dat
2025-07-08 19:47:02,663 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-angle-20250707_155842_wps文档绘制.dat
2025-07-08 19:47:02,787 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-angle-20250707_155842_wps文档绘制.dat.txt
2025-07-08 19:47:02,787 - INFO - 找到event函数: E:\wpsskia\qt\source\qtbase\src\widgets\kernel\qwidget.cpp:9682 event
2025-07-08 19:47:02,787 - INFO - 提取callId: 130516, callTime: 86.385ms
2025-07-08 19:47:02,787 - INFO - 提取callId: 16502, callTime: 81.782344ms
2025-07-08 19:47:02,787 - INFO - 提取callId: 70323, callTime: 38.233906ms
2025-07-08 19:47:02,787 - INFO - 提取callId: 78671, callTime: 33.555ms
2025-07-08 19:47:02,787 - INFO - 提取callId: 119522, callTime: 31.75375ms
2025-07-08 19:47:02,787 - INFO - 提取callId: 75839, callTime: 30.013907ms
2025-07-08 19:47:02,788 - INFO - 提取callId: 73142, callTime: 28.735156ms
2025-07-08 19:47:02,788 - INFO - 提取callId: 89006, callTime: 28.502187ms
2025-07-08 19:47:02,788 - INFO - 提取callId: 80022, callTime: 26.711406ms
2025-07-08 19:47:02,788 - INFO - 提取callId: 94456, callTime: 25.03125ms
2025-07-08 19:47:02,788 - INFO - 提取callId: 132333, callTime: 24.799375ms
2025-07-08 19:47:02,788 - INFO - 提取callId: 92157, callTime: 24.264844ms
2025-07-08 19:47:02,788 - INFO - 提取callId: 112153, callTime: 23.488281ms
2025-07-08 19:47:02,788 - INFO - 提取callId: 100268, callTime: 23.115469ms
2025-07-08 19:47:02,788 - INFO - 提取callId: 21039, callTime: 22.95625ms
2025-07-08 19:47:02,788 - INFO - 提取callId: 12720, callTime: 22.102968ms
2025-07-08 19:47:02,788 - INFO - 提取callId: 24787, callTime: 21.604219ms
2025-07-08 19:47:02,788 - INFO - 提取callId: 114462, callTime: 21.081406ms
2025-07-08 19:47:02,788 - INFO - 提取callId: 761, callTime: 19.844375ms
2025-07-08 19:47:02,788 - INFO - 提取callId: 103152, callTime: 19.158281ms
2025-07-08 19:47:02,789 - INFO - 提取callId: 126848, callTime: 18.720312ms
2025-07-08 19:47:02,789 - INFO - 提取callId: 83973, callTime: 18.389219ms
2025-07-08 19:47:02,789 - INFO - 提取callId: 85024, callTime: 18.154218ms
2025-07-08 19:47:02,789 - INFO - 提取callId: 116990, callTime: 17.532969ms
2025-07-08 19:47:02,789 - INFO - 提取callId: 103873, callTime: 17.522968ms
2025-07-08 19:47:02,789 - INFO - 提取callId: 122054, callTime: 17.423125ms
2025-07-08 19:47:02,789 - INFO - 提取callId: 10069, callTime: 17.300938ms
2025-07-08 19:47:02,789 - INFO - 提取callId: 130029, callTime: 16.746875ms
2025-07-08 19:47:02,789 - INFO - 提取callId: 96416, callTime: 16.18875ms
2025-07-08 19:47:02,789 - INFO - 提取callId: 47535, callTime: 15.65375ms
2025-07-08 19:47:02,789 - INFO - 提取callId: 49943, callTime: 15.415313ms
2025-07-08 19:47:02,789 - INFO - 提取callId: 6336, callTime: 15.052344ms
2025-07-08 19:47:02,789 - INFO - 提取callId: 59653, callTime: 14.974531ms
2025-07-08 19:47:02,789 - INFO - 提取callId: 105328, callTime: 14.583906ms
2025-07-08 19:47:02,789 - INFO - 提取callId: 132792, callTime: 14.494062ms
2025-07-08 19:47:02,790 - INFO - 提取callId: 106387, callTime: 14.245938ms
2025-07-08 19:47:02,790 - INFO - 提取callId: 124426, callTime: 13.990625ms
2025-07-08 19:47:02,790 - INFO - 提取callId: 87897, callTime: 13.282031ms
2025-07-08 19:47:02,790 - INFO - 提取callId: 95349, callTime: 12.982812ms
2025-07-08 19:47:02,790 - INFO - 提取callId: 97378, callTime: 12.951719ms
2025-07-08 19:47:02,790 - INFO - 提取到 40 个event相关的callId
2025-07-08 19:47:02,790 - INFO - 处理 callId=130516, callTime=86.385ms, rank=1
2025-07-08 19:47:02,790 - INFO - 执行详细分析: callId=130516
2025-07-08 19:47:02,923 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\1-130516.txt
2025-07-08 19:47:02,923 - INFO - 处理 callId=16502, callTime=81.782344ms, rank=2
2025-07-08 19:47:02,923 - INFO - 执行详细分析: callId=16502
2025-07-08 19:47:03,145 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\2-16502.txt
2025-07-08 19:47:03,145 - INFO - 处理 callId=70323, callTime=38.233906ms, rank=3
2025-07-08 19:47:03,145 - INFO - 执行详细分析: callId=70323
2025-07-08 19:47:03,289 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\3-70323.txt
2025-07-08 19:47:03,290 - INFO - 处理 callId=78671, callTime=33.555ms, rank=4
2025-07-08 19:47:03,290 - INFO - 执行详细分析: callId=78671
2025-07-08 19:47:03,389 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\4-78671.txt
2025-07-08 19:47:03,389 - INFO - 处理 callId=119522, callTime=31.75375ms, rank=5
2025-07-08 19:47:03,389 - INFO - 执行详细分析: callId=119522
2025-07-08 19:47:03,580 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\5-119522.txt
2025-07-08 19:47:03,580 - INFO - 处理 callId=75839, callTime=30.013907ms, rank=6
2025-07-08 19:47:03,580 - INFO - 执行详细分析: callId=75839
2025-07-08 19:47:03,735 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\6-75839.txt
2025-07-08 19:47:03,735 - INFO - 处理 callId=73142, callTime=28.735156ms, rank=7
2025-07-08 19:47:03,735 - INFO - 执行详细分析: callId=73142
2025-07-08 19:47:03,882 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\7-73142.txt
2025-07-08 19:47:03,882 - INFO - 处理 callId=89006, callTime=28.502187ms, rank=8
2025-07-08 19:47:03,882 - INFO - 执行详细分析: callId=89006
2025-07-08 19:47:03,984 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\8-89006.txt
2025-07-08 19:47:03,984 - INFO - 处理 callId=80022, callTime=26.711406ms, rank=9
2025-07-08 19:47:03,984 - INFO - 执行详细分析: callId=80022
2025-07-08 19:47:04,083 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\9-80022.txt
2025-07-08 19:47:04,084 - INFO - 处理 callId=94456, callTime=25.03125ms, rank=10
2025-07-08 19:47:04,084 - INFO - 执行详细分析: callId=94456
2025-07-08 19:47:04,180 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\10-94456.txt
2025-07-08 19:47:04,180 - INFO - 处理 callId=132333, callTime=24.799375ms, rank=11
2025-07-08 19:47:04,180 - INFO - 执行详细分析: callId=132333
2025-07-08 19:47:04,292 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\11-132333.txt
2025-07-08 19:47:04,292 - INFO - 处理 callId=92157, callTime=24.264844ms, rank=12
2025-07-08 19:47:04,292 - INFO - 执行详细分析: callId=92157
2025-07-08 19:47:04,384 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\12-92157.txt
2025-07-08 19:47:04,385 - INFO - 处理 callId=112153, callTime=23.488281ms, rank=13
2025-07-08 19:47:04,385 - INFO - 执行详细分析: callId=112153
2025-07-08 19:47:04,492 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\13-112153.txt
2025-07-08 19:47:04,492 - INFO - 处理 callId=100268, callTime=23.115469ms, rank=14
2025-07-08 19:47:04,492 - INFO - 执行详细分析: callId=100268
2025-07-08 19:47:04,589 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\14-100268.txt
2025-07-08 19:47:04,589 - INFO - 处理 callId=21039, callTime=22.95625ms, rank=15
2025-07-08 19:47:04,590 - INFO - 执行详细分析: callId=21039
2025-07-08 19:47:04,775 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\15-21039.txt
2025-07-08 19:47:04,775 - INFO - 处理 callId=12720, callTime=22.102968ms, rank=16
2025-07-08 19:47:04,775 - INFO - 执行详细分析: callId=12720
2025-07-08 19:47:04,883 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\16-12720.txt
2025-07-08 19:47:04,883 - INFO - 处理 callId=24787, callTime=21.604219ms, rank=17
2025-07-08 19:47:04,883 - INFO - 执行详细分析: callId=24787
2025-07-08 19:47:04,963 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\17-24787.txt
2025-07-08 19:47:04,963 - INFO - 处理 callId=114462, callTime=21.081406ms, rank=18
2025-07-08 19:47:04,963 - INFO - 执行详细分析: callId=114462
2025-07-08 19:47:05,151 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\18-114462.txt
2025-07-08 19:47:05,151 - INFO - 处理 callId=761, callTime=19.844375ms, rank=19
2025-07-08 19:47:05,151 - INFO - 执行详细分析: callId=761
2025-07-08 19:47:05,183 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\19-761.txt
2025-07-08 19:47:05,183 - INFO - 处理 callId=103152, callTime=19.158281ms, rank=20
2025-07-08 19:47:05,183 - INFO - 执行详细分析: callId=103152
2025-07-08 19:47:05,282 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\20-103152.txt
2025-07-08 19:47:05,282 - INFO - 处理 callId=126848, callTime=18.720312ms, rank=21
2025-07-08 19:47:05,282 - INFO - 执行详细分析: callId=126848
2025-07-08 19:47:05,476 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\21-126848.txt
2025-07-08 19:47:05,476 - INFO - 处理 callId=83973, callTime=18.389219ms, rank=22
2025-07-08 19:47:05,476 - INFO - 执行详细分析: callId=83973
2025-07-08 19:47:05,572 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\22-83973.txt
2025-07-08 19:47:05,572 - INFO - 处理 callId=85024, callTime=18.154218ms, rank=23
2025-07-08 19:47:05,572 - INFO - 执行详细分析: callId=85024
2025-07-08 19:47:05,662 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\23-85024.txt
2025-07-08 19:47:05,662 - INFO - 处理 callId=116990, callTime=17.532969ms, rank=24
2025-07-08 19:47:05,662 - INFO - 执行详细分析: callId=116990
2025-07-08 19:47:05,851 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\24-116990.txt
2025-07-08 19:47:05,851 - INFO - 处理 callId=103873, callTime=17.522968ms, rank=25
2025-07-08 19:47:05,851 - INFO - 执行详细分析: callId=103873
2025-07-08 19:47:05,947 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\25-103873.txt
2025-07-08 19:47:05,947 - INFO - 处理 callId=122054, callTime=17.423125ms, rank=26
2025-07-08 19:47:05,947 - INFO - 执行详细分析: callId=122054
2025-07-08 19:47:06,114 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\26-122054.txt
2025-07-08 19:47:06,114 - INFO - 处理 callId=10069, callTime=17.300938ms, rank=27
2025-07-08 19:47:06,114 - INFO - 执行详细分析: callId=10069
2025-07-08 19:47:06,218 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\27-10069.txt
2025-07-08 19:47:06,218 - INFO - 处理 callId=130029, callTime=16.746875ms, rank=28
2025-07-08 19:47:06,218 - INFO - 执行详细分析: callId=130029
2025-07-08 19:47:06,332 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\28-130029.txt
2025-07-08 19:47:06,332 - INFO - 处理 callId=96416, callTime=16.18875ms, rank=29
2025-07-08 19:47:06,333 - INFO - 执行详细分析: callId=96416
2025-07-08 19:47:06,432 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\29-96416.txt
2025-07-08 19:47:06,432 - INFO - 处理 callId=47535, callTime=15.65375ms, rank=30
2025-07-08 19:47:06,432 - INFO - 执行详细分析: callId=47535
2025-07-08 19:47:06,545 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\30-47535.txt
2025-07-08 19:47:06,545 - INFO - 处理 callId=49943, callTime=15.415313ms, rank=31
2025-07-08 19:47:06,545 - INFO - 执行详细分析: callId=49943
2025-07-08 19:47:06,673 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\31-49943.txt
2025-07-08 19:47:06,673 - INFO - 处理 callId=6336, callTime=15.052344ms, rank=32
2025-07-08 19:47:06,673 - INFO - 执行详细分析: callId=6336
2025-07-08 19:47:06,691 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\32-6336.txt
2025-07-08 19:47:06,691 - INFO - 处理 callId=59653, callTime=14.974531ms, rank=33
2025-07-08 19:47:06,691 - INFO - 执行详细分析: callId=59653
2025-07-08 19:47:06,827 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\33-59653.txt
2025-07-08 19:47:06,827 - INFO - 处理 callId=105328, callTime=14.583906ms, rank=34
2025-07-08 19:47:06,828 - INFO - 执行详细分析: callId=105328
2025-07-08 19:47:06,934 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\34-105328.txt
2025-07-08 19:47:06,934 - INFO - 处理 callId=132792, callTime=14.494062ms, rank=35
2025-07-08 19:47:06,935 - INFO - 执行详细分析: callId=132792
2025-07-08 19:47:07,050 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\35-132792.txt
2025-07-08 19:47:07,050 - INFO - 处理 callId=106387, callTime=14.245938ms, rank=36
2025-07-08 19:47:07,050 - INFO - 执行详细分析: callId=106387
2025-07-08 19:47:07,156 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\36-106387.txt
2025-07-08 19:47:07,156 - INFO - 处理 callId=124426, callTime=13.990625ms, rank=37
2025-07-08 19:47:07,156 - INFO - 执行详细分析: callId=124426
2025-07-08 19:47:07,347 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\37-124426.txt
2025-07-08 19:47:07,347 - INFO - 处理 callId=87897, callTime=13.282031ms, rank=38
2025-07-08 19:47:07,347 - INFO - 执行详细分析: callId=87897
2025-07-08 19:47:07,447 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\38-87897.txt
2025-07-08 19:47:07,448 - INFO - 处理 callId=95349, callTime=12.982812ms, rank=39
2025-07-08 19:47:07,448 - INFO - 执行详细分析: callId=95349
2025-07-08 19:47:07,545 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\39-95349.txt
2025-07-08 19:47:07,545 - INFO - 处理 callId=97378, callTime=12.951719ms, rank=40
2025-07-08 19:47:07,545 - INFO - 执行详细分析: callId=97378
2025-07-08 19:47:07,643 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_155842_wps文档绘制\40-97378.txt
2025-07-08 19:47:07,644 - INFO - 完成处理文件: perfmon-angle-20250707_155842_wps文档绘制.dat
2025-07-08 19:47:07,644 - INFO - 处理进度: 10/30
2025-07-08 19:47:07,644 - INFO - 开始处理文件: perfmon-angle-20250707_160551_wps块选.dat
2025-07-08 19:47:07,644 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-angle-20250707_160551_wps块选.dat
2025-07-08 19:47:07,903 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-angle-20250707_160551_wps块选.dat.txt
2025-07-08 19:47:07,903 - INFO - 找到event函数: E:\wpsskia\qt\source\qtbase\src\widgets\kernel\qwidget.cpp:9682 event
2025-07-08 19:47:07,903 - INFO - 提取callId: 42903, callTime: 52.746875ms
2025-07-08 19:47:07,903 - INFO - 提取callId: 171679, callTime: 38.117031ms
2025-07-08 19:47:07,903 - INFO - 提取callId: 283249, callTime: 30.987031ms
2025-07-08 19:47:07,903 - INFO - 提取callId: 229703, callTime: 29.58875ms
2025-07-08 19:47:07,903 - INFO - 提取callId: 171032, callTime: 29.529531ms
2025-07-08 19:47:07,903 - INFO - 提取callId: 198398, callTime: 28.708906ms
2025-07-08 19:47:07,903 - INFO - 提取callId: 248429, callTime: 28.365625ms
2025-07-08 19:47:07,903 - INFO - 提取callId: 250319, callTime: 28.329219ms
2025-07-08 19:47:07,904 - INFO - 提取callId: 295357, callTime: 28.084375ms
2025-07-08 19:47:07,904 - INFO - 提取callId: 230380, callTime: 28.030156ms
2025-07-08 19:47:07,904 - INFO - 提取callId: 296434, callTime: 27.812031ms
2025-07-08 19:47:07,904 - INFO - 提取callId: 268655, callTime: 27.524375ms
2025-07-08 19:47:07,904 - INFO - 提取callId: 170372, callTime: 27.445469ms
2025-07-08 19:47:07,904 - INFO - 提取callId: 174301, callTime: 27.249375ms
2025-07-08 19:47:07,904 - INFO - 提取callId: 203622, callTime: 26.972812ms
2025-07-08 19:47:07,904 - INFO - 提取callId: 1642, callTime: 26.529687ms
2025-07-08 19:47:07,904 - INFO - 提取callId: 234981, callTime: 26.310468ms
2025-07-08 19:47:07,904 - INFO - 提取callId: 20805, callTime: 26.179844ms
2025-07-08 19:47:07,904 - INFO - 提取callId: 241197, callTime: 25.778437ms
2025-07-08 19:47:07,904 - INFO - 提取callId: 151826, callTime: 25.372969ms
2025-07-08 19:47:07,904 - INFO - 提取callId: 174955, callTime: 25.238906ms
2025-07-08 19:47:07,904 - INFO - 提取callId: 231045, callTime: 25.22875ms
2025-07-08 19:47:07,904 - INFO - 提取callId: 247818, callTime: 24.896875ms
2025-07-08 19:47:07,905 - INFO - 提取callId: 178207, callTime: 24.700938ms
2025-07-08 19:47:07,905 - INFO - 提取callId: 168400, callTime: 24.527969ms
2025-07-08 19:47:07,905 - INFO - 提取callId: 119154, callTime: 24.516875ms
2025-07-08 19:47:07,905 - INFO - 提取callId: 209445, callTime: 24.422968ms
2025-07-08 19:47:07,905 - INFO - 提取callId: 210087, callTime: 24.258906ms
2025-07-08 19:47:07,905 - INFO - 提取callId: 207515, callTime: 24.24375ms
2025-07-08 19:47:07,905 - INFO - 提取callId: 284578, callTime: 23.925782ms
2025-07-08 19:47:07,905 - INFO - 提取callId: 175609, callTime: 23.849219ms
2025-07-08 19:47:07,905 - INFO - 提取callId: 250959, callTime: 23.835625ms
2025-07-08 19:47:07,905 - INFO - 提取callId: 197740, callTime: 23.793437ms
2025-07-08 19:47:07,905 - INFO - 提取callId: 103764, callTime: 23.616094ms
2025-07-08 19:47:07,905 - INFO - 提取callId: 145077, callTime: 23.498437ms
2025-07-08 19:47:07,905 - INFO - 提取callId: 238126, callTime: 23.436875ms
2025-07-08 19:47:07,905 - INFO - 提取callId: 255283, callTime: 23.359219ms
2025-07-08 19:47:07,905 - INFO - 提取callId: 245383, callTime: 23.209062ms
2025-07-08 19:47:07,905 - INFO - 提取callId: 152493, callTime: 23.020312ms
2025-07-08 19:47:07,906 - INFO - 提取callId: 283925, callTime: 22.912031ms
2025-07-08 19:47:07,906 - INFO - 提取到 40 个event相关的callId
2025-07-08 19:47:07,906 - INFO - 处理 callId=42903, callTime=52.746875ms, rank=1
2025-07-08 19:47:07,906 - INFO - 执行详细分析: callId=42903
2025-07-08 19:47:08,026 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\1-42903.txt
2025-07-08 19:47:08,026 - INFO - 处理 callId=171679, callTime=38.117031ms, rank=2
2025-07-08 19:47:08,026 - INFO - 执行详细分析: callId=171679
2025-07-08 19:47:08,176 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\2-171679.txt
2025-07-08 19:47:08,176 - INFO - 处理 callId=283249, callTime=30.987031ms, rank=3
2025-07-08 19:47:08,176 - INFO - 执行详细分析: callId=283249
2025-07-08 19:47:08,409 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\3-283249.txt
2025-07-08 19:47:08,409 - INFO - 处理 callId=229703, callTime=29.58875ms, rank=4
2025-07-08 19:47:08,409 - INFO - 执行详细分析: callId=229703
2025-07-08 19:47:08,602 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\4-229703.txt
2025-07-08 19:47:08,602 - INFO - 处理 callId=171032, callTime=29.529531ms, rank=5
2025-07-08 19:47:08,602 - INFO - 执行详细分析: callId=171032
2025-07-08 19:47:08,751 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\5-171032.txt
2025-07-08 19:47:08,751 - INFO - 处理 callId=198398, callTime=28.708906ms, rank=6
2025-07-08 19:47:08,751 - INFO - 执行详细分析: callId=198398
2025-07-08 19:47:08,920 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\6-198398.txt
2025-07-08 19:47:08,921 - INFO - 处理 callId=248429, callTime=28.365625ms, rank=7
2025-07-08 19:47:08,921 - INFO - 执行详细分析: callId=248429
2025-07-08 19:47:09,124 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\7-248429.txt
2025-07-08 19:47:09,124 - INFO - 处理 callId=250319, callTime=28.329219ms, rank=8
2025-07-08 19:47:09,125 - INFO - 执行详细分析: callId=250319
2025-07-08 19:47:09,331 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\8-250319.txt
2025-07-08 19:47:09,331 - INFO - 处理 callId=295357, callTime=28.084375ms, rank=9
2025-07-08 19:47:09,331 - INFO - 执行详细分析: callId=295357
2025-07-08 19:47:09,565 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\9-295357.txt
2025-07-08 19:47:09,565 - INFO - 处理 callId=230380, callTime=28.030156ms, rank=10
2025-07-08 19:47:09,565 - INFO - 执行详细分析: callId=230380
2025-07-08 19:47:09,759 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\10-230380.txt
2025-07-08 19:47:09,759 - INFO - 处理 callId=296434, callTime=27.812031ms, rank=11
2025-07-08 19:47:09,759 - INFO - 执行详细分析: callId=296434
2025-07-08 19:47:09,995 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\11-296434.txt
2025-07-08 19:47:09,995 - INFO - 处理 callId=268655, callTime=27.524375ms, rank=12
2025-07-08 19:47:09,995 - INFO - 执行详细分析: callId=268655
2025-07-08 19:47:10,216 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\12-268655.txt
2025-07-08 19:47:10,216 - INFO - 处理 callId=170372, callTime=27.445469ms, rank=13
2025-07-08 19:47:10,216 - INFO - 执行详细分析: callId=170372
2025-07-08 19:47:10,364 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\13-170372.txt
2025-07-08 19:47:10,365 - INFO - 处理 callId=174301, callTime=27.249375ms, rank=14
2025-07-08 19:47:10,365 - INFO - 执行详细分析: callId=174301
2025-07-08 19:47:10,516 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\14-174301.txt
2025-07-08 19:47:10,517 - INFO - 处理 callId=203622, callTime=26.972812ms, rank=15
2025-07-08 19:47:10,517 - INFO - 执行详细分析: callId=203622
2025-07-08 19:47:10,691 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\15-203622.txt
2025-07-08 19:47:10,691 - INFO - 处理 callId=1642, callTime=26.529687ms, rank=16
2025-07-08 19:47:10,691 - INFO - 执行详细分析: callId=1642
2025-07-08 19:47:10,738 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\16-1642.txt
2025-07-08 19:47:10,739 - INFO - 处理 callId=234981, callTime=26.310468ms, rank=17
2025-07-08 19:47:10,739 - INFO - 执行详细分析: callId=234981
2025-07-08 19:47:10,929 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\17-234981.txt
2025-07-08 19:47:10,929 - INFO - 处理 callId=20805, callTime=26.179844ms, rank=18
2025-07-08 19:47:10,929 - INFO - 执行详细分析: callId=20805
2025-07-08 19:47:11,022 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\18-20805.txt
2025-07-08 19:47:11,023 - INFO - 处理 callId=241197, callTime=25.778437ms, rank=19
2025-07-08 19:47:11,023 - INFO - 执行详细分析: callId=241197
2025-07-08 19:47:11,218 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\19-241197.txt
2025-07-08 19:47:11,218 - INFO - 处理 callId=151826, callTime=25.372969ms, rank=20
2025-07-08 19:47:11,219 - INFO - 执行详细分析: callId=151826
2025-07-08 19:47:11,352 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\20-151826.txt
2025-07-08 19:47:11,352 - INFO - 处理 callId=174955, callTime=25.238906ms, rank=21
2025-07-08 19:47:11,352 - INFO - 执行详细分析: callId=174955
2025-07-08 19:47:11,504 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\21-174955.txt
2025-07-08 19:47:11,504 - INFO - 处理 callId=231045, callTime=25.22875ms, rank=22
2025-07-08 19:47:11,504 - INFO - 执行详细分析: callId=231045
2025-07-08 19:47:11,693 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\22-231045.txt
2025-07-08 19:47:11,693 - INFO - 处理 callId=247818, callTime=24.896875ms, rank=23
2025-07-08 19:47:11,693 - INFO - 执行详细分析: callId=247818
2025-07-08 19:47:11,892 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\23-247818.txt
2025-07-08 19:47:11,892 - INFO - 处理 callId=178207, callTime=24.700938ms, rank=24
2025-07-08 19:47:11,893 - INFO - 执行详细分析: callId=178207
2025-07-08 19:47:12,044 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\24-178207.txt
2025-07-08 19:47:12,044 - INFO - 处理 callId=168400, callTime=24.527969ms, rank=25
2025-07-08 19:47:12,044 - INFO - 执行详细分析: callId=168400
2025-07-08 19:47:12,189 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\25-168400.txt
2025-07-08 19:47:12,190 - INFO - 处理 callId=119154, callTime=24.516875ms, rank=26
2025-07-08 19:47:12,190 - INFO - 执行详细分析: callId=119154
2025-07-08 19:47:12,297 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\26-119154.txt
2025-07-08 19:47:12,297 - INFO - 处理 callId=209445, callTime=24.422968ms, rank=27
2025-07-08 19:47:12,297 - INFO - 执行详细分析: callId=209445
2025-07-08 19:47:12,471 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\27-209445.txt
2025-07-08 19:47:12,471 - INFO - 处理 callId=210087, callTime=24.258906ms, rank=28
2025-07-08 19:47:12,471 - INFO - 执行详细分析: callId=210087
2025-07-08 19:47:12,648 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\28-210087.txt
2025-07-08 19:47:12,648 - INFO - 处理 callId=207515, callTime=24.24375ms, rank=29
2025-07-08 19:47:12,648 - INFO - 执行详细分析: callId=207515
2025-07-08 19:47:12,822 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\29-207515.txt
2025-07-08 19:47:12,822 - INFO - 处理 callId=284578, callTime=23.925782ms, rank=30
2025-07-08 19:47:12,822 - INFO - 执行详细分析: callId=284578
2025-07-08 19:47:13,049 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\30-284578.txt
2025-07-08 19:47:13,049 - INFO - 处理 callId=175609, callTime=23.849219ms, rank=31
2025-07-08 19:47:13,049 - INFO - 执行详细分析: callId=175609
2025-07-08 19:47:13,199 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\31-175609.txt
2025-07-08 19:47:13,199 - INFO - 处理 callId=250959, callTime=23.835625ms, rank=32
2025-07-08 19:47:13,199 - INFO - 执行详细分析: callId=250959
2025-07-08 19:47:13,402 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\32-250959.txt
2025-07-08 19:47:13,402 - INFO - 处理 callId=197740, callTime=23.793437ms, rank=33
2025-07-08 19:47:13,402 - INFO - 执行详细分析: callId=197740
2025-07-08 19:47:13,571 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\33-197740.txt
2025-07-08 19:47:13,571 - INFO - 处理 callId=103764, callTime=23.616094ms, rank=34
2025-07-08 19:47:13,571 - INFO - 执行详细分析: callId=103764
2025-07-08 19:47:13,672 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\34-103764.txt
2025-07-08 19:47:13,672 - INFO - 处理 callId=145077, callTime=23.498437ms, rank=35
2025-07-08 19:47:13,672 - INFO - 执行详细分析: callId=145077
2025-07-08 19:47:13,803 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\35-145077.txt
2025-07-08 19:47:13,803 - INFO - 处理 callId=238126, callTime=23.436875ms, rank=36
2025-07-08 19:47:13,803 - INFO - 执行详细分析: callId=238126
2025-07-08 19:47:13,995 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\36-238126.txt
2025-07-08 19:47:13,995 - INFO - 处理 callId=255283, callTime=23.359219ms, rank=37
2025-07-08 19:47:13,995 - INFO - 执行详细分析: callId=255283
2025-07-08 19:47:14,201 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\37-255283.txt
2025-07-08 19:47:14,201 - INFO - 处理 callId=245383, callTime=23.209062ms, rank=38
2025-07-08 19:47:14,201 - INFO - 执行详细分析: callId=245383
2025-07-08 19:47:14,400 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\38-245383.txt
2025-07-08 19:47:14,400 - INFO - 处理 callId=152493, callTime=23.020312ms, rank=39
2025-07-08 19:47:14,400 - INFO - 执行详细分析: callId=152493
2025-07-08 19:47:14,533 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\39-152493.txt
2025-07-08 19:47:14,533 - INFO - 处理 callId=283925, callTime=22.912031ms, rank=40
2025-07-08 19:47:14,533 - INFO - 执行详细分析: callId=283925
2025-07-08 19:47:14,760 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160551_wps块选\40-283925.txt
2025-07-08 19:47:14,761 - INFO - 完成处理文件: perfmon-angle-20250707_160551_wps块选.dat
2025-07-08 19:47:14,761 - INFO - 处理进度: 11/30
2025-07-08 19:47:14,761 - INFO - 开始处理文件: perfmon-angle-20250707_160824_wps插入.dat
2025-07-08 19:47:14,761 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-angle-20250707_160824_wps插入.dat
2025-07-08 19:47:14,951 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-angle-20250707_160824_wps插入.dat.txt
2025-07-08 19:47:14,951 - INFO - 找到event函数: E:\wpsskia\qt\source\qtbase\src\widgets\kernel\qwidget.cpp:9682 event
2025-07-08 19:47:14,951 - INFO - 提取callId: 75060, callTime: 62.568594ms
2025-07-08 19:47:14,951 - INFO - 提取callId: 34272, callTime: 52.147032ms
2025-07-08 19:47:14,951 - INFO - 提取callId: 13683, callTime: 38.956093ms
2025-07-08 19:47:14,951 - INFO - 提取callId: 139124, callTime: 36.943594ms
2025-07-08 19:47:14,952 - INFO - 提取callId: 79663, callTime: 28.275782ms
2025-07-08 19:47:14,952 - INFO - 提取callId: 19548, callTime: 28.172188ms
2025-07-08 19:47:14,952 - INFO - 提取callId: 63679, callTime: 27.08375ms
2025-07-08 19:47:14,952 - INFO - 提取callId: 185496, callTime: 26.757968ms
2025-07-08 19:47:14,952 - INFO - 提取callId: 1642, callTime: 24.662343ms
2025-07-08 19:47:14,952 - INFO - 提取callId: 30344, callTime: 24.267656ms
2025-07-08 19:47:14,952 - INFO - 提取callId: 3190, callTime: 20.138125ms
2025-07-08 19:47:14,952 - INFO - 提取callId: 27693, callTime: 19.339375ms
2025-07-08 19:47:14,952 - INFO - 提取callId: 81484, callTime: 17.463593ms
2025-07-08 19:47:14,952 - INFO - 提取callId: 66901, callTime: 17.452969ms
2025-07-08 19:47:14,952 - INFO - 提取callId: 80578, callTime: 17.180157ms
2025-07-08 19:47:14,952 - INFO - 提取callId: 36691, callTime: 16.749531ms
2025-07-08 19:47:14,953 - INFO - 提取callId: 39989, callTime: 15.589375ms
2025-07-08 19:47:14,953 - INFO - 提取callId: 83834, callTime: 14.475157ms
2025-07-08 19:47:14,953 - INFO - 提取callId: 77787, callTime: 14.422657ms
2025-07-08 19:47:14,953 - INFO - 提取callId: 12660, callTime: 14.34ms
2025-07-08 19:47:14,953 - INFO - 提取callId: 76881, callTime: 14.33875ms
2025-07-08 19:47:14,953 - INFO - 提取callId: 78757, callTime: 13.1ms
2025-07-08 19:47:14,953 - INFO - 提取callId: 183057, callTime: 12.849063ms
2025-07-08 19:47:14,953 - INFO - 提取callId: 189236, callTime: 12.796875ms
2025-07-08 19:47:14,953 - INFO - 提取callId: 10093, callTime: 12.718281ms
2025-07-08 19:47:14,953 - INFO - 提取callId: 75966, callTime: 12.438906ms
2025-07-08 19:47:14,953 - INFO - 提取callId: 182411, callTime: 12.282188ms
2025-07-08 19:47:14,953 - INFO - 提取callId: 23347, callTime: 11.93375ms
2025-07-08 19:47:14,953 - INFO - 提取callId: 137499, callTime: 11.876562ms
2025-07-08 19:47:14,953 - INFO - 提取callId: 82520, callTime: 11.738907ms
2025-07-08 19:47:14,954 - INFO - 提取callId: 33121, callTime: 11.492969ms
2025-07-08 19:47:14,954 - INFO - 提取callId: 16832, callTime: 11.250312ms
2025-07-08 19:47:14,954 - INFO - 提取callId: 5421, callTime: 10.813125ms
2025-07-08 19:47:14,954 - INFO - 提取callId: 63046, callTime: 10.650625ms
2025-07-08 19:47:14,954 - INFO - 提取callId: 24689, callTime: 10.291563ms
2025-07-08 19:47:14,954 - INFO - 提取callId: 6517, callTime: 10.250625ms
2025-07-08 19:47:14,954 - INFO - 提取callId: 143502, callTime: 10.115313ms
2025-07-08 19:47:14,954 - INFO - 提取callId: 181646, callTime: 9.916407ms
2025-07-08 19:47:14,954 - INFO - 提取callId: 141060, callTime: 9.765469ms
2025-07-08 19:47:14,954 - INFO - 提取callId: 55711, callTime: 9.617656ms
2025-07-08 19:47:14,954 - INFO - 提取到 40 个event相关的callId
2025-07-08 19:47:14,954 - INFO - 处理 callId=75060, callTime=62.568594ms, rank=1
2025-07-08 19:47:14,954 - INFO - 执行详细分析: callId=75060
2025-07-08 19:47:15,043 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\1-75060.txt
2025-07-08 19:47:15,043 - INFO - 处理 callId=34272, callTime=52.147032ms, rank=2
2025-07-08 19:47:15,043 - INFO - 执行详细分析: callId=34272
2025-07-08 19:47:15,153 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\2-34272.txt
2025-07-08 19:47:15,153 - INFO - 处理 callId=13683, callTime=38.956093ms, rank=3
2025-07-08 19:47:15,153 - INFO - 执行详细分析: callId=13683
2025-07-08 19:47:15,192 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\3-13683.txt
2025-07-08 19:47:15,193 - INFO - 处理 callId=139124, callTime=36.943594ms, rank=4
2025-07-08 19:47:15,193 - INFO - 执行详细分析: callId=139124
2025-07-08 19:47:15,328 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\4-139124.txt
2025-07-08 19:47:15,328 - INFO - 处理 callId=79663, callTime=28.275782ms, rank=5
2025-07-08 19:47:15,328 - INFO - 执行详细分析: callId=79663
2025-07-08 19:47:15,419 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\5-79663.txt
2025-07-08 19:47:15,419 - INFO - 处理 callId=19548, callTime=28.172188ms, rank=6
2025-07-08 19:47:15,419 - INFO - 执行详细分析: callId=19548
2025-07-08 19:47:15,510 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\6-19548.txt
2025-07-08 19:47:15,510 - INFO - 处理 callId=63679, callTime=27.08375ms, rank=7
2025-07-08 19:47:15,510 - INFO - 执行详细分析: callId=63679
2025-07-08 19:47:15,593 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\7-63679.txt
2025-07-08 19:47:15,593 - INFO - 处理 callId=185496, callTime=26.757968ms, rank=8
2025-07-08 19:47:15,593 - INFO - 执行详细分析: callId=185496
2025-07-08 19:47:15,943 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\8-185496.txt
2025-07-08 19:47:15,943 - INFO - 处理 callId=1642, callTime=24.662343ms, rank=9
2025-07-08 19:47:15,943 - INFO - 执行详细分析: callId=1642
2025-07-08 19:47:15,988 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\9-1642.txt
2025-07-08 19:47:15,988 - INFO - 处理 callId=30344, callTime=24.267656ms, rank=10
2025-07-08 19:47:15,988 - INFO - 执行详细分析: callId=30344
2025-07-08 19:47:16,105 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\10-30344.txt
2025-07-08 19:47:16,105 - INFO - 处理 callId=3190, callTime=20.138125ms, rank=11
2025-07-08 19:47:16,105 - INFO - 执行详细分析: callId=3190
2025-07-08 19:47:16,169 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\11-3190.txt
2025-07-08 19:47:16,170 - INFO - 处理 callId=27693, callTime=19.339375ms, rank=12
2025-07-08 19:47:16,170 - INFO - 执行详细分析: callId=27693
2025-07-08 19:47:16,287 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\12-27693.txt
2025-07-08 19:47:16,287 - INFO - 处理 callId=81484, callTime=17.463593ms, rank=13
2025-07-08 19:47:16,287 - INFO - 执行详细分析: callId=81484
2025-07-08 19:47:16,379 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\13-81484.txt
2025-07-08 19:47:16,379 - INFO - 处理 callId=66901, callTime=17.452969ms, rank=14
2025-07-08 19:47:16,380 - INFO - 执行详细分析: callId=66901
2025-07-08 19:47:16,454 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\14-66901.txt
2025-07-08 19:47:16,454 - INFO - 处理 callId=80578, callTime=17.180157ms, rank=15
2025-07-08 19:47:16,454 - INFO - 执行详细分析: callId=80578
2025-07-08 19:47:16,546 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\15-80578.txt
2025-07-08 19:47:16,546 - INFO - 处理 callId=36691, callTime=16.749531ms, rank=16
2025-07-08 19:47:16,546 - INFO - 执行详细分析: callId=36691
2025-07-08 19:47:16,642 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\16-36691.txt
2025-07-08 19:47:16,642 - INFO - 处理 callId=39989, callTime=15.589375ms, rank=17
2025-07-08 19:47:16,643 - INFO - 执行详细分析: callId=39989
2025-07-08 19:47:16,741 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\17-39989.txt
2025-07-08 19:47:16,741 - INFO - 处理 callId=83834, callTime=14.475157ms, rank=18
2025-07-08 19:47:16,741 - INFO - 执行详细分析: callId=83834
2025-07-08 19:47:16,815 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\18-83834.txt
2025-07-08 19:47:16,816 - INFO - 处理 callId=77787, callTime=14.422657ms, rank=19
2025-07-08 19:47:16,816 - INFO - 执行详细分析: callId=77787
2025-07-08 19:47:16,905 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\19-77787.txt
2025-07-08 19:47:16,906 - INFO - 处理 callId=12660, callTime=14.34ms, rank=20
2025-07-08 19:47:16,906 - INFO - 执行详细分析: callId=12660
2025-07-08 19:47:16,945 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\20-12660.txt
2025-07-08 19:47:16,945 - INFO - 处理 callId=76881, callTime=14.33875ms, rank=21
2025-07-08 19:47:16,945 - INFO - 执行详细分析: callId=76881
2025-07-08 19:47:17,036 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\21-76881.txt
2025-07-08 19:47:17,036 - INFO - 处理 callId=78757, callTime=13.1ms, rank=22
2025-07-08 19:47:17,036 - INFO - 执行详细分析: callId=78757
2025-07-08 19:47:17,129 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\22-78757.txt
2025-07-08 19:47:17,130 - INFO - 处理 callId=183057, callTime=12.849063ms, rank=23
2025-07-08 19:47:17,130 - INFO - 执行详细分析: callId=183057
2025-07-08 19:47:17,334 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\23-183057.txt
2025-07-08 19:47:17,335 - INFO - 处理 callId=189236, callTime=12.796875ms, rank=24
2025-07-08 19:47:17,335 - INFO - 执行详细分析: callId=189236
2025-07-08 19:47:17,481 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\24-189236.txt
2025-07-08 19:47:17,481 - INFO - 处理 callId=10093, callTime=12.718281ms, rank=25
2025-07-08 19:47:17,481 - INFO - 执行详细分析: callId=10093
2025-07-08 19:47:17,585 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\25-10093.txt
2025-07-08 19:47:17,585 - INFO - 处理 callId=75966, callTime=12.438906ms, rank=26
2025-07-08 19:47:17,585 - INFO - 执行详细分析: callId=75966
2025-07-08 19:47:17,674 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\26-75966.txt
2025-07-08 19:47:17,674 - INFO - 处理 callId=182411, callTime=12.282188ms, rank=27
2025-07-08 19:47:17,674 - INFO - 执行详细分析: callId=182411
2025-07-08 19:47:17,818 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\27-182411.txt
2025-07-08 19:47:17,818 - INFO - 处理 callId=23347, callTime=11.93375ms, rank=28
2025-07-08 19:47:17,818 - INFO - 执行详细分析: callId=23347
2025-07-08 19:47:17,874 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\28-23347.txt
2025-07-08 19:47:17,874 - INFO - 处理 callId=137499, callTime=11.876562ms, rank=29
2025-07-08 19:47:17,874 - INFO - 执行详细分析: callId=137499
2025-07-08 19:47:17,999 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\29-137499.txt
2025-07-08 19:47:17,999 - INFO - 处理 callId=82520, callTime=11.738907ms, rank=30
2025-07-08 19:47:17,999 - INFO - 执行详细分析: callId=82520
2025-07-08 19:47:18,084 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\30-82520.txt
2025-07-08 19:47:18,084 - INFO - 处理 callId=33121, callTime=11.492969ms, rank=31
2025-07-08 19:47:18,084 - INFO - 执行详细分析: callId=33121
2025-07-08 19:47:18,128 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\31-33121.txt
2025-07-08 19:47:18,128 - INFO - 处理 callId=16832, callTime=11.250312ms, rank=32
2025-07-08 19:47:18,128 - INFO - 执行详细分析: callId=16832
2025-07-08 19:47:18,171 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\32-16832.txt
2025-07-08 19:47:18,171 - INFO - 处理 callId=5421, callTime=10.813125ms, rank=33
2025-07-08 19:47:18,171 - INFO - 执行详细分析: callId=5421
2025-07-08 19:47:18,206 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\33-5421.txt
2025-07-08 19:47:18,206 - INFO - 处理 callId=63046, callTime=10.650625ms, rank=34
2025-07-08 19:47:18,206 - INFO - 执行详细分析: callId=63046
2025-07-08 19:47:18,275 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\34-63046.txt
2025-07-08 19:47:18,275 - INFO - 处理 callId=24689, callTime=10.291563ms, rank=35
2025-07-08 19:47:18,275 - INFO - 执行详细分析: callId=24689
2025-07-08 19:47:18,329 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\35-24689.txt
2025-07-08 19:47:18,330 - INFO - 处理 callId=6517, callTime=10.250625ms, rank=36
2025-07-08 19:47:18,330 - INFO - 执行详细分析: callId=6517
2025-07-08 19:47:18,401 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\36-6517.txt
2025-07-08 19:47:18,401 - INFO - 处理 callId=143502, callTime=10.115313ms, rank=37
2025-07-08 19:47:18,402 - INFO - 执行详细分析: callId=143502
2025-07-08 19:47:18,528 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\37-143502.txt
2025-07-08 19:47:18,528 - INFO - 处理 callId=181646, callTime=9.916407ms, rank=38
2025-07-08 19:47:18,528 - INFO - 执行详细分析: callId=181646
2025-07-08 19:47:18,687 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\38-181646.txt
2025-07-08 19:47:18,687 - INFO - 处理 callId=141060, callTime=9.765469ms, rank=39
2025-07-08 19:47:18,687 - INFO - 执行详细分析: callId=141060
2025-07-08 19:47:18,808 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\39-141060.txt
2025-07-08 19:47:18,808 - INFO - 处理 callId=55711, callTime=9.617656ms, rank=40
2025-07-08 19:47:18,808 - INFO - 执行详细分析: callId=55711
2025-07-08 19:47:18,912 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_160824_wps插入\40-55711.txt
2025-07-08 19:47:18,912 - INFO - 完成处理文件: perfmon-angle-20250707_160824_wps插入.dat
2025-07-08 19:47:18,912 - INFO - 处理进度: 12/30
2025-07-08 19:47:18,913 - INFO - 开始处理文件: perfmon-angle-20250707_161351_wps滚动文档.dat
2025-07-08 19:47:18,913 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-angle-20250707_161351_wps滚动文档.dat
2025-07-08 19:47:19,408 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-angle-20250707_161351_wps滚动文档.dat.txt
2025-07-08 19:47:19,408 - INFO - 找到event函数: E:\wpsskia\qt\source\qtbase\src\widgets\kernel\qwidget.cpp:9682 event
2025-07-08 19:47:19,408 - INFO - 提取callId: 33157, callTime: 66.029375ms
2025-07-08 19:47:19,408 - INFO - 提取callId: 70243, callTime: 32.899219ms
2025-07-08 19:47:19,408 - INFO - 提取callId: 64043, callTime: 28.17875ms
2025-07-08 19:47:19,408 - INFO - 提取callId: 29229, callTime: 25.495312ms
2025-07-08 19:47:19,408 - INFO - 提取callId: 1642, callTime: 25.325156ms
2025-07-08 19:47:19,408 - INFO - 提取callId: 36292, callTime: 25.248125ms
2025-07-08 19:47:19,408 - INFO - 提取callId: 26578, callTime: 25.006094ms
2025-07-08 19:47:19,408 - INFO - 提取callId: 73609, callTime: 24.228125ms
2025-07-08 19:47:19,408 - INFO - 提取callId: 19758, callTime: 23.822657ms
2025-07-08 19:47:19,409 - INFO - 提取callId: 3190, callTime: 20.980157ms
2025-07-08 19:47:19,409 - INFO - 提取callId: 45232, callTime: 17.291562ms
2025-07-08 19:47:19,409 - INFO - 提取callId: 40306, callTime: 16.462656ms
2025-07-08 19:47:19,409 - INFO - 提取callId: 83478, callTime: 15.441094ms
2025-07-08 19:47:19,409 - INFO - 提取callId: 66512, callTime: 14.78375ms
2025-07-08 19:47:19,409 - INFO - 提取callId: 114633, callTime: 14.69875ms
2025-07-08 19:47:19,409 - INFO - 提取callId: 81556, callTime: 14.670781ms
2025-07-08 19:47:19,409 - INFO - 提取callId: 58552, callTime: 14.637656ms
2025-07-08 19:47:19,409 - INFO - 提取callId: 75320, callTime: 14.372813ms
2025-07-08 19:47:19,409 - INFO - 提取callId: 23557, callTime: 14.149375ms
2025-07-08 19:47:19,409 - INFO - 提取callId: 84208, callTime: 13.913125ms
2025-07-08 19:47:19,409 - INFO - 提取callId: 443728, callTime: 13.820312ms
2025-07-08 19:47:19,409 - INFO - 提取callId: 53956, callTime: 13.505468ms
2025-07-08 19:47:19,409 - INFO - 提取callId: 503519, callTime: 12.663281ms
2025-07-08 19:47:19,409 - INFO - 提取callId: 9672, callTime: 12.638437ms
2025-07-08 19:47:19,410 - INFO - 提取callId: 115860, callTime: 12.125313ms
2025-07-08 19:47:19,410 - INFO - 提取callId: 56769, callTime: 11.493906ms
2025-07-08 19:47:19,410 - INFO - 提取callId: 422226, callTime: 11.403125ms
2025-07-08 19:47:19,410 - INFO - 提取callId: 32006, callTime: 10.960469ms
2025-07-08 19:47:19,410 - INFO - 提取callId: 84547, callTime: 10.932031ms
2025-07-08 19:47:19,410 - INFO - 提取callId: 487816, callTime: 10.640938ms
2025-07-08 19:47:19,410 - INFO - 提取callId: 321959, callTime: 10.637812ms
2025-07-08 19:47:19,410 - INFO - 提取callId: 473755, callTime: 10.430312ms
2025-07-08 19:47:19,410 - INFO - 提取callId: 84880, callTime: 10.363125ms
2025-07-08 19:47:19,410 - INFO - 提取callId: 494492, callTime: 10.291719ms
2025-07-08 19:47:19,410 - INFO - 提取callId: 76461, callTime: 10.2275ms
2025-07-08 19:47:19,410 - INFO - 提取callId: 479294, callTime: 10.189844ms
2025-07-08 19:47:19,410 - INFO - 提取callId: 481258, callTime: 10.167031ms
2025-07-08 19:47:19,410 - INFO - 提取callId: 12392, callTime: 10.139219ms
2025-07-08 19:47:19,410 - INFO - 提取callId: 82788, callTime: 10.039062ms
2025-07-08 19:47:19,410 - INFO - 提取callId: 118741, callTime: 10.025625ms
2025-07-08 19:47:19,410 - INFO - 提取到 40 个event相关的callId
2025-07-08 19:47:19,411 - INFO - 处理 callId=33157, callTime=66.029375ms, rank=1
2025-07-08 19:47:19,411 - INFO - 执行详细分析: callId=33157
2025-07-08 19:47:19,573 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\1-33157.txt
2025-07-08 19:47:19,573 - INFO - 处理 callId=70243, callTime=32.899219ms, rank=2
2025-07-08 19:47:19,573 - INFO - 执行详细分析: callId=70243
2025-07-08 19:47:19,747 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\2-70243.txt
2025-07-08 19:47:19,747 - INFO - 处理 callId=64043, callTime=28.17875ms, rank=3
2025-07-08 19:47:19,748 - INFO - 执行详细分析: callId=64043
2025-07-08 19:47:19,915 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\3-64043.txt
2025-07-08 19:47:19,915 - INFO - 处理 callId=29229, callTime=25.495312ms, rank=4
2025-07-08 19:47:19,915 - INFO - 执行详细分析: callId=29229
2025-07-08 19:47:20,037 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\4-29229.txt
2025-07-08 19:47:20,037 - INFO - 处理 callId=1642, callTime=25.325156ms, rank=5
2025-07-08 19:47:20,037 - INFO - 执行详细分析: callId=1642
2025-07-08 19:47:20,088 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\5-1642.txt
2025-07-08 19:47:20,088 - INFO - 处理 callId=36292, callTime=25.248125ms, rank=6
2025-07-08 19:47:20,088 - INFO - 执行详细分析: callId=36292
2025-07-08 19:47:20,233 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\6-36292.txt
2025-07-08 19:47:20,233 - INFO - 处理 callId=26578, callTime=25.006094ms, rank=7
2025-07-08 19:47:20,233 - INFO - 执行详细分析: callId=26578
2025-07-08 19:47:20,353 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\7-26578.txt
2025-07-08 19:47:20,353 - INFO - 处理 callId=73609, callTime=24.228125ms, rank=8
2025-07-08 19:47:20,353 - INFO - 执行详细分析: callId=73609
2025-07-08 19:47:20,478 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\8-73609.txt
2025-07-08 19:47:20,478 - INFO - 处理 callId=19758, callTime=23.822657ms, rank=9
2025-07-08 19:47:20,478 - INFO - 执行详细分析: callId=19758
2025-07-08 19:47:20,573 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\9-19758.txt
2025-07-08 19:47:20,574 - INFO - 处理 callId=3190, callTime=20.980157ms, rank=10
2025-07-08 19:47:20,574 - INFO - 执行详细分析: callId=3190
2025-07-08 19:47:20,647 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\10-3190.txt
2025-07-08 19:47:20,647 - INFO - 处理 callId=45232, callTime=17.291562ms, rank=11
2025-07-08 19:47:20,647 - INFO - 执行详细分析: callId=45232
2025-07-08 19:47:20,724 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\11-45232.txt
2025-07-08 19:47:20,724 - INFO - 处理 callId=40306, callTime=16.462656ms, rank=12
2025-07-08 19:47:20,724 - INFO - 执行详细分析: callId=40306
2025-07-08 19:47:20,824 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\12-40306.txt
2025-07-08 19:47:20,824 - INFO - 处理 callId=83478, callTime=15.441094ms, rank=13
2025-07-08 19:47:20,824 - INFO - 执行详细分析: callId=83478
2025-07-08 19:47:20,909 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\13-83478.txt
2025-07-08 19:47:20,909 - INFO - 处理 callId=66512, callTime=14.78375ms, rank=14
2025-07-08 19:47:20,909 - INFO - 执行详细分析: callId=66512
2025-07-08 19:47:21,001 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\14-66512.txt
2025-07-08 19:47:21,001 - INFO - 处理 callId=114633, callTime=14.69875ms, rank=15
2025-07-08 19:47:21,001 - INFO - 执行详细分析: callId=114633
2025-07-08 19:47:21,103 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\15-114633.txt
2025-07-08 19:47:21,103 - INFO - 处理 callId=81556, callTime=14.670781ms, rank=16
2025-07-08 19:47:21,104 - INFO - 执行详细分析: callId=81556
2025-07-08 19:47:21,185 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\16-81556.txt
2025-07-08 19:47:21,185 - INFO - 处理 callId=58552, callTime=14.637656ms, rank=17
2025-07-08 19:47:21,185 - INFO - 执行详细分析: callId=58552
2025-07-08 19:47:21,274 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\17-58552.txt
2025-07-08 19:47:21,274 - INFO - 处理 callId=75320, callTime=14.372813ms, rank=18
2025-07-08 19:47:21,274 - INFO - 执行详细分析: callId=75320
2025-07-08 19:47:21,370 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\18-75320.txt
2025-07-08 19:47:21,371 - INFO - 处理 callId=23557, callTime=14.149375ms, rank=19
2025-07-08 19:47:21,371 - INFO - 执行详细分析: callId=23557
2025-07-08 19:47:21,431 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\19-23557.txt
2025-07-08 19:47:21,431 - INFO - 处理 callId=84208, callTime=13.913125ms, rank=20
2025-07-08 19:47:21,431 - INFO - 执行详细分析: callId=84208
2025-07-08 19:47:21,514 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\20-84208.txt
2025-07-08 19:47:21,515 - INFO - 处理 callId=443728, callTime=13.820312ms, rank=21
2025-07-08 19:47:21,515 - INFO - 执行详细分析: callId=443728
2025-07-08 19:47:21,852 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\21-443728.txt
2025-07-08 19:47:21,852 - INFO - 处理 callId=53956, callTime=13.505468ms, rank=22
2025-07-08 19:47:21,852 - INFO - 执行详细分析: callId=53956
2025-07-08 19:47:21,959 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\22-53956.txt
2025-07-08 19:47:21,960 - INFO - 处理 callId=503519, callTime=12.663281ms, rank=23
2025-07-08 19:47:21,960 - INFO - 执行详细分析: callId=503519
2025-07-08 19:47:22,347 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\23-503519.txt
2025-07-08 19:47:22,347 - INFO - 处理 callId=9672, callTime=12.638437ms, rank=24
2025-07-08 19:47:22,347 - INFO - 执行详细分析: callId=9672
2025-07-08 19:47:22,454 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\24-9672.txt
2025-07-08 19:47:22,454 - INFO - 处理 callId=115860, callTime=12.125313ms, rank=25
2025-07-08 19:47:22,454 - INFO - 执行详细分析: callId=115860
2025-07-08 19:47:22,559 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\25-115860.txt
2025-07-08 19:47:22,559 - INFO - 处理 callId=56769, callTime=11.493906ms, rank=26
2025-07-08 19:47:22,559 - INFO - 执行详细分析: callId=56769
2025-07-08 19:47:22,669 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\26-56769.txt
2025-07-08 19:47:22,669 - INFO - 处理 callId=422226, callTime=11.403125ms, rank=27
2025-07-08 19:47:22,669 - INFO - 执行详细分析: callId=422226
2025-07-08 19:47:22,994 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\27-422226.txt
2025-07-08 19:47:22,995 - INFO - 处理 callId=32006, callTime=10.960469ms, rank=28
2025-07-08 19:47:22,995 - INFO - 执行详细分析: callId=32006
2025-07-08 19:47:23,043 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\28-32006.txt
2025-07-08 19:47:23,044 - INFO - 处理 callId=84547, callTime=10.932031ms, rank=29
2025-07-08 19:47:23,044 - INFO - 执行详细分析: callId=84547
2025-07-08 19:47:23,125 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\29-84547.txt
2025-07-08 19:47:23,125 - INFO - 处理 callId=487816, callTime=10.640938ms, rank=30
2025-07-08 19:47:23,126 - INFO - 执行详细分析: callId=487816
2025-07-08 19:47:23,499 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\30-487816.txt
2025-07-08 19:47:23,499 - INFO - 处理 callId=321959, callTime=10.637812ms, rank=31
2025-07-08 19:47:23,499 - INFO - 执行详细分析: callId=321959
2025-07-08 19:47:23,752 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\31-321959.txt
2025-07-08 19:47:23,752 - INFO - 处理 callId=473755, callTime=10.430312ms, rank=32
2025-07-08 19:47:23,752 - INFO - 执行详细分析: callId=473755
2025-07-08 19:47:24,113 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\32-473755.txt
2025-07-08 19:47:24,113 - INFO - 处理 callId=84880, callTime=10.363125ms, rank=33
2025-07-08 19:47:24,113 - INFO - 执行详细分析: callId=84880
2025-07-08 19:47:24,197 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\33-84880.txt
2025-07-08 19:47:24,198 - INFO - 处理 callId=494492, callTime=10.291719ms, rank=34
2025-07-08 19:47:24,198 - INFO - 执行详细分析: callId=494492
2025-07-08 19:47:24,573 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\34-494492.txt
2025-07-08 19:47:24,573 - INFO - 处理 callId=76461, callTime=10.2275ms, rank=35
2025-07-08 19:47:24,573 - INFO - 执行详细分析: callId=76461
2025-07-08 19:47:24,648 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\35-76461.txt
2025-07-08 19:47:24,648 - INFO - 处理 callId=479294, callTime=10.189844ms, rank=36
2025-07-08 19:47:24,648 - INFO - 执行详细分析: callId=479294
2025-07-08 19:47:25,014 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\36-479294.txt
2025-07-08 19:47:25,014 - INFO - 处理 callId=481258, callTime=10.167031ms, rank=37
2025-07-08 19:47:25,014 - INFO - 执行详细分析: callId=481258
2025-07-08 19:47:25,380 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\37-481258.txt
2025-07-08 19:47:25,380 - INFO - 处理 callId=12392, callTime=10.139219ms, rank=38
2025-07-08 19:47:25,381 - INFO - 执行详细分析: callId=12392
2025-07-08 19:47:25,409 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\38-12392.txt
2025-07-08 19:47:25,409 - INFO - 处理 callId=82788, callTime=10.039062ms, rank=39
2025-07-08 19:47:25,409 - INFO - 执行详细分析: callId=82788
2025-07-08 19:47:25,490 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\39-82788.txt
2025-07-08 19:47:25,490 - INFO - 处理 callId=118741, callTime=10.025625ms, rank=40
2025-07-08 19:47:25,490 - INFO - 执行详细分析: callId=118741
2025-07-08 19:47:25,596 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161351_wps滚动文档\40-118741.txt
2025-07-08 19:47:25,596 - INFO - 完成处理文件: perfmon-angle-20250707_161351_wps滚动文档.dat
2025-07-08 19:47:25,596 - INFO - 处理进度: 13/30
2025-07-08 19:47:25,596 - INFO - 开始处理文件: perfmon-angle-20250707_161508_wps缩放文档.dat
2025-07-08 19:47:25,596 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-angle-20250707_161508_wps缩放文档.dat
2025-07-08 19:47:25,826 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-angle-20250707_161508_wps缩放文档.dat.txt
2025-07-08 19:47:25,826 - INFO - 找到event函数: E:\wpsskia\qt\source\qtbase\src\widgets\kernel\qwidget.cpp:9682 event
2025-07-08 19:47:25,827 - INFO - 提取callId: 113342, callTime: 134.76ms
2025-07-08 19:47:25,827 - INFO - 提取callId: 205907, callTime: 133.78ms
2025-07-08 19:47:25,827 - INFO - 提取callId: 136089, callTime: 131.001094ms
2025-07-08 19:47:25,827 - INFO - 提取callId: 217273, callTime: 129.35125ms
2025-07-08 19:47:25,827 - INFO - 提取callId: 240108, callTime: 129.264688ms
2025-07-08 19:47:25,827 - INFO - 提取callId: 158948, callTime: 128.908437ms
2025-07-08 19:47:25,827 - INFO - 提取callId: 251434, callTime: 128.878125ms
2025-07-08 19:47:25,827 - INFO - 提取callId: 124708, callTime: 128.792656ms
2025-07-08 19:47:25,827 - INFO - 提取callId: 147508, callTime: 128.471875ms
2025-07-08 19:47:25,827 - INFO - 提取callId: 228782, callTime: 127.866875ms
2025-07-08 19:47:25,828 - INFO - 提取callId: 170398, callTime: 59.382813ms
2025-07-08 19:47:25,828 - INFO - 提取callId: 199911, callTime: 56.32875ms
2025-07-08 19:47:25,828 - INFO - 提取callId: 28485, callTime: 54.207969ms
2025-07-08 19:47:25,828 - INFO - 提取callId: 109396, callTime: 50.518593ms
2025-07-08 19:47:25,828 - INFO - 提取callId: 14940, callTime: 28.804375ms
2025-07-08 19:47:25,828 - INFO - 提取callId: 58346, callTime: 28.148593ms
2025-07-08 19:47:25,828 - INFO - 提取callId: 107122, callTime: 27.999063ms
2025-07-08 19:47:25,828 - INFO - 提取callId: 1642, callTime: 25.412344ms
2025-07-08 19:47:25,828 - INFO - 提取callId: 197543, callTime: 25.225625ms
2025-07-08 19:47:25,828 - INFO - 提取callId: 3190, callTime: 22.542032ms
2025-07-08 19:47:25,828 - INFO - 提取callId: 195921, callTime: 21.593593ms
2025-07-08 19:47:25,828 - INFO - 提取callId: 105603, callTime: 20.816719ms
2025-07-08 19:47:25,829 - INFO - 提取callId: 24557, callTime: 19.777344ms
2025-07-08 19:47:25,829 - INFO - 提取callId: 21906, callTime: 19.772656ms
2025-07-08 19:47:25,829 - INFO - 提取callId: 61689, callTime: 19.291875ms
2025-07-08 19:47:25,829 - INFO - 提取callId: 175617, callTime: 17.354219ms
2025-07-08 19:47:25,829 - INFO - 提取callId: 205192, callTime: 16.839063ms
2025-07-08 19:47:25,829 - INFO - 提取callId: 62046, callTime: 16.584844ms
2025-07-08 19:47:25,829 - INFO - 提取callId: 30827, callTime: 16.378438ms
2025-07-08 19:47:25,829 - INFO - 提取callId: 92608, callTime: 15.022032ms
2025-07-08 19:47:25,829 - INFO - 提取callId: 188286, callTime: 13.909218ms
2025-07-08 19:47:25,830 - INFO - 提取callId: 88262, callTime: 13.792032ms
2025-07-08 19:47:25,830 - INFO - 提取callId: 195312, callTime: 13.750938ms
2025-07-08 19:47:25,830 - INFO - 提取callId: 189081, callTime: 13.009219ms
2025-07-08 19:47:25,830 - INFO - 提取callId: 103880, callTime: 12.958906ms
2025-07-08 19:47:25,830 - INFO - 提取callId: 187861, callTime: 12.76125ms
2025-07-08 19:47:25,830 - INFO - 提取callId: 181731, callTime: 12.726562ms
2025-07-08 19:47:25,830 - INFO - 提取callId: 104557, callTime: 12.725ms
2025-07-08 19:47:25,830 - INFO - 提取callId: 182603, callTime: 12.705938ms
2025-07-08 19:47:25,830 - INFO - 提取callId: 50609, callTime: 12.530313ms
2025-07-08 19:47:25,830 - INFO - 提取到 40 个event相关的callId
2025-07-08 19:47:25,830 - INFO - 处理 callId=113342, callTime=134.76ms, rank=1
2025-07-08 19:47:25,830 - INFO - 执行详细分析: callId=113342
2025-07-08 19:47:28,106 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\1-113342.txt
2025-07-08 19:47:28,106 - INFO - 处理 callId=205907, callTime=133.78ms, rank=2
2025-07-08 19:47:28,106 - INFO - 执行详细分析: callId=205907
2025-07-08 19:47:30,447 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\2-205907.txt
2025-07-08 19:47:30,448 - INFO - 处理 callId=136089, callTime=131.001094ms, rank=3
2025-07-08 19:47:30,448 - INFO - 执行详细分析: callId=136089
2025-07-08 19:47:32,734 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\3-136089.txt
2025-07-08 19:47:32,735 - INFO - 处理 callId=217273, callTime=129.35125ms, rank=4
2025-07-08 19:47:32,735 - INFO - 执行详细分析: callId=217273
2025-07-08 19:47:35,055 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\4-217273.txt
2025-07-08 19:47:35,055 - INFO - 处理 callId=240108, callTime=129.264688ms, rank=5
2025-07-08 19:47:35,056 - INFO - 执行详细分析: callId=240108
2025-07-08 19:47:37,388 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\5-240108.txt
2025-07-08 19:47:37,388 - INFO - 处理 callId=158948, callTime=128.908437ms, rank=6
2025-07-08 19:47:37,388 - INFO - 执行详细分析: callId=158948
2025-07-08 19:47:39,652 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\6-158948.txt
2025-07-08 19:47:39,653 - INFO - 处理 callId=251434, callTime=128.878125ms, rank=7
2025-07-08 19:47:39,653 - INFO - 执行详细分析: callId=251434
2025-07-08 19:47:41,995 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\7-251434.txt
2025-07-08 19:47:41,995 - INFO - 处理 callId=124708, callTime=128.792656ms, rank=8
2025-07-08 19:47:41,995 - INFO - 执行详细分析: callId=124708
2025-07-08 19:47:44,241 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\8-124708.txt
2025-07-08 19:47:44,241 - INFO - 处理 callId=147508, callTime=128.471875ms, rank=9
2025-07-08 19:47:44,242 - INFO - 执行详细分析: callId=147508
2025-07-08 19:47:46,536 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\9-147508.txt
2025-07-08 19:47:46,537 - INFO - 处理 callId=228782, callTime=127.866875ms, rank=10
2025-07-08 19:47:46,537 - INFO - 执行详细分析: callId=228782
2025-07-08 19:47:48,895 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\10-228782.txt
2025-07-08 19:47:48,896 - INFO - 处理 callId=170398, callTime=59.382813ms, rank=11
2025-07-08 19:47:48,896 - INFO - 执行详细分析: callId=170398
2025-07-08 19:47:49,495 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\11-170398.txt
2025-07-08 19:47:49,495 - INFO - 处理 callId=199911, callTime=56.32875ms, rank=12
2025-07-08 19:47:49,495 - INFO - 执行详细分析: callId=199911
2025-07-08 19:47:50,116 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\12-199911.txt
2025-07-08 19:47:50,117 - INFO - 处理 callId=28485, callTime=54.207969ms, rank=13
2025-07-08 19:47:50,117 - INFO - 执行详细分析: callId=28485
2025-07-08 19:47:50,224 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\13-28485.txt
2025-07-08 19:47:50,224 - INFO - 处理 callId=109396, callTime=50.518593ms, rank=14
2025-07-08 19:47:50,224 - INFO - 执行详细分析: callId=109396
2025-07-08 19:47:50,596 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\14-109396.txt
2025-07-08 19:47:50,596 - INFO - 处理 callId=14940, callTime=28.804375ms, rank=15
2025-07-08 19:47:50,596 - INFO - 执行详细分析: callId=14940
2025-07-08 19:47:50,685 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\15-14940.txt
2025-07-08 19:47:50,686 - INFO - 处理 callId=58346, callTime=28.148593ms, rank=16
2025-07-08 19:47:50,686 - INFO - 执行详细分析: callId=58346
2025-07-08 19:47:50,769 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\16-58346.txt
2025-07-08 19:47:50,770 - INFO - 处理 callId=107122, callTime=27.999063ms, rank=17
2025-07-08 19:47:50,770 - INFO - 执行详细分析: callId=107122
2025-07-08 19:47:50,964 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\17-107122.txt
2025-07-08 19:47:50,964 - INFO - 处理 callId=1642, callTime=25.412344ms, rank=18
2025-07-08 19:47:50,965 - INFO - 执行详细分析: callId=1642
2025-07-08 19:47:51,011 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\18-1642.txt
2025-07-08 19:47:51,011 - INFO - 处理 callId=197543, callTime=25.225625ms, rank=19
2025-07-08 19:47:51,011 - INFO - 执行详细分析: callId=197543
2025-07-08 19:47:51,270 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\19-197543.txt
2025-07-08 19:47:51,270 - INFO - 处理 callId=3190, callTime=22.542032ms, rank=20
2025-07-08 19:47:51,270 - INFO - 执行详细分析: callId=3190
2025-07-08 19:47:51,337 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\20-3190.txt
2025-07-08 19:47:51,337 - INFO - 处理 callId=195921, callTime=21.593593ms, rank=21
2025-07-08 19:47:51,338 - INFO - 执行详细分析: callId=195921
2025-07-08 19:47:51,540 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\21-195921.txt
2025-07-08 19:47:51,541 - INFO - 处理 callId=105603, callTime=20.816719ms, rank=22
2025-07-08 19:47:51,541 - INFO - 执行详细分析: callId=105603
2025-07-08 19:47:51,680 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\22-105603.txt
2025-07-08 19:47:51,681 - INFO - 处理 callId=24557, callTime=19.777344ms, rank=23
2025-07-08 19:47:51,681 - INFO - 执行详细分析: callId=24557
2025-07-08 19:47:51,800 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\23-24557.txt
2025-07-08 19:47:51,800 - INFO - 处理 callId=21906, callTime=19.772656ms, rank=24
2025-07-08 19:47:51,800 - INFO - 执行详细分析: callId=21906
2025-07-08 19:47:51,915 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\24-21906.txt
2025-07-08 19:47:51,915 - INFO - 处理 callId=61689, callTime=19.291875ms, rank=25
2025-07-08 19:47:51,916 - INFO - 执行详细分析: callId=61689
2025-07-08 19:47:51,980 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\25-61689.txt
2025-07-08 19:47:51,980 - INFO - 处理 callId=175617, callTime=17.354219ms, rank=26
2025-07-08 19:47:51,981 - INFO - 执行详细分析: callId=175617
2025-07-08 19:47:52,172 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\26-175617.txt
2025-07-08 19:47:52,172 - INFO - 处理 callId=205192, callTime=16.839063ms, rank=27
2025-07-08 19:47:52,172 - INFO - 执行详细分析: callId=205192
2025-07-08 19:47:52,333 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\27-205192.txt
2025-07-08 19:47:52,334 - INFO - 处理 callId=62046, callTime=16.584844ms, rank=28
2025-07-08 19:47:52,334 - INFO - 执行详细分析: callId=62046
2025-07-08 19:47:52,393 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\28-62046.txt
2025-07-08 19:47:52,393 - INFO - 处理 callId=30827, callTime=16.378438ms, rank=29
2025-07-08 19:47:52,393 - INFO - 执行详细分析: callId=30827
2025-07-08 19:47:52,486 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\29-30827.txt
2025-07-08 19:47:52,486 - INFO - 处理 callId=92608, callTime=15.022032ms, rank=30
2025-07-08 19:47:52,487 - INFO - 执行详细分析: callId=92608
2025-07-08 19:47:52,567 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\30-92608.txt
2025-07-08 19:47:52,568 - INFO - 处理 callId=188286, callTime=13.909218ms, rank=31
2025-07-08 19:47:52,568 - INFO - 执行详细分析: callId=188286
2025-07-08 19:47:52,719 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\31-188286.txt
2025-07-08 19:47:52,719 - INFO - 处理 callId=88262, callTime=13.792032ms, rank=32
2025-07-08 19:47:52,719 - INFO - 执行详细分析: callId=88262
2025-07-08 19:47:52,801 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\32-88262.txt
2025-07-08 19:47:52,802 - INFO - 处理 callId=195312, callTime=13.750938ms, rank=33
2025-07-08 19:47:52,802 - INFO - 执行详细分析: callId=195312
2025-07-08 19:47:52,956 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\33-195312.txt
2025-07-08 19:47:52,956 - INFO - 处理 callId=189081, callTime=13.009219ms, rank=34
2025-07-08 19:47:52,956 - INFO - 执行详细分析: callId=189081
2025-07-08 19:47:53,105 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\34-189081.txt
2025-07-08 19:47:53,105 - INFO - 处理 callId=103880, callTime=12.958906ms, rank=35
2025-07-08 19:47:53,105 - INFO - 执行详细分析: callId=103880
2025-07-08 19:47:53,205 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\35-103880.txt
2025-07-08 19:47:53,205 - INFO - 处理 callId=187861, callTime=12.76125ms, rank=36
2025-07-08 19:47:53,205 - INFO - 执行详细分析: callId=187861
2025-07-08 19:47:53,356 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\36-187861.txt
2025-07-08 19:47:53,357 - INFO - 处理 callId=181731, callTime=12.726562ms, rank=37
2025-07-08 19:47:53,357 - INFO - 执行详细分析: callId=181731
2025-07-08 19:47:53,505 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\37-181731.txt
2025-07-08 19:47:53,505 - INFO - 处理 callId=104557, callTime=12.725ms, rank=38
2025-07-08 19:47:53,505 - INFO - 执行详细分析: callId=104557
2025-07-08 19:47:53,616 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\38-104557.txt
2025-07-08 19:47:53,616 - INFO - 处理 callId=182603, callTime=12.705938ms, rank=39
2025-07-08 19:47:53,616 - INFO - 执行详细分析: callId=182603
2025-07-08 19:47:53,760 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\39-182603.txt
2025-07-08 19:47:53,760 - INFO - 处理 callId=50609, callTime=12.530313ms, rank=40
2025-07-08 19:47:53,760 - INFO - 执行详细分析: callId=50609
2025-07-08 19:47:53,861 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-angle-20250707_161508_wps缩放文档\40-50609.txt
2025-07-08 19:47:53,861 - INFO - 完成处理文件: perfmon-angle-20250707_161508_wps缩放文档.dat
2025-07-08 19:47:53,861 - INFO - 处理进度: 14/30
2025-07-08 19:47:53,861 - INFO - 开始处理文件: perfmon-raster-20250707_095208-wpp文档绘制.dat
2025-07-08 19:47:53,862 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-raster-20250707_095208-wpp文档绘制.dat
2025-07-08 19:47:53,875 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-raster-20250707_095208-wpp文档绘制.dat.txt
2025-07-08 19:47:53,875 - INFO - 找到event函数: E:\wpsskia\qt\source\qtbase\src\widgets\kernel\qwidget.cpp:9682 event
2025-07-08 19:47:53,875 - INFO - 提取callId: 2181, callTime: 43.857343ms
2025-07-08 19:47:53,875 - INFO - 提取callId: 3069, callTime: 34.023594ms
2025-07-08 19:47:53,875 - INFO - 提取callId: 14, callTime: 24.928281ms
2025-07-08 19:47:53,876 - INFO - 提取callId: 450, callTime: 15.360469ms
2025-07-08 19:47:53,876 - INFO - 提取callId: 3221, callTime: 13.602031ms
2025-07-08 19:47:53,876 - INFO - 提取callId: 886, callTime: 13.474531ms
2025-07-08 19:47:53,876 - INFO - 提取callId: 3217, callTime: 12.9625ms
2025-07-08 19:47:53,876 - INFO - 提取callId: 1303, callTime: 12.110781ms
2025-07-08 19:47:53,876 - INFO - 提取callId: 3077, callTime: 11.017968ms
2025-07-08 19:47:53,876 - INFO - 提取callId: 1764, callTime: 10.63875ms
2025-07-08 19:47:53,876 - INFO - 提取callId: 3137, callTime: 9.913281ms
2025-07-08 19:47:53,877 - INFO - 提取callId: 3129, callTime: 9.614844ms
2025-07-08 19:47:53,877 - INFO - 提取callId: 3277, callTime: 9.441563ms
2025-07-08 19:47:53,877 - INFO - 提取callId: 3201, callTime: 9.171407ms
2025-07-08 19:47:53,877 - INFO - 提取callId: 3273, callTime: 9.065782ms
2025-07-08 19:47:53,877 - INFO - 提取callId: 3209, callTime: 8.918906ms
2025-07-08 19:47:53,877 - INFO - 提取callId: 3185, callTime: 8.814687ms
2025-07-08 19:47:53,877 - INFO - 提取callId: 3165, callTime: 8.780625ms
2025-07-08 19:47:53,877 - INFO - 提取callId: 3145, callTime: 8.649531ms
2025-07-08 19:47:53,877 - INFO - 提取callId: 3081, callTime: 8.649219ms
2025-07-08 19:47:53,877 - INFO - 提取callId: 3253, callTime: 8.584219ms
2025-07-08 19:47:53,878 - INFO - 提取callId: 3233, callTime: 8.554375ms
2025-07-08 19:47:53,878 - INFO - 提取callId: 3197, callTime: 8.499531ms
2025-07-08 19:47:53,878 - INFO - 提取callId: 3133, callTime: 8.358438ms
2025-07-08 19:47:53,878 - INFO - 提取callId: 3121, callTime: 8.3075ms
2025-07-08 19:47:53,878 - INFO - 提取callId: 3177, callTime: 8.305781ms
2025-07-08 19:47:53,878 - INFO - 提取callId: 3125, callTime: 8.215781ms
2025-07-08 19:47:53,878 - INFO - 提取callId: 3289, callTime: 8.021719ms
2025-07-08 19:47:53,878 - INFO - 提取callId: 3153, callTime: 7.997656ms
2025-07-08 19:47:53,878 - INFO - 提取callId: 3189, callTime: 7.9625ms
2025-07-08 19:47:53,878 - INFO - 提取callId: 3261, callTime: 7.916094ms
2025-07-08 19:47:53,878 - INFO - 提取callId: 3237, callTime: 7.806093ms
2025-07-08 19:47:53,878 - INFO - 提取callId: 3225, callTime: 7.707812ms
2025-07-08 19:47:53,879 - INFO - 提取callId: 3249, callTime: 7.599844ms
2025-07-08 19:47:53,879 - INFO - 提取callId: 3169, callTime: 7.590156ms
2025-07-08 19:47:53,879 - INFO - 提取callId: 1739, callTime: 7.579844ms
2025-07-08 19:47:53,879 - INFO - 提取callId: 3073, callTime: 7.499687ms
2025-07-08 19:47:53,879 - INFO - 提取callId: 3241, callTime: 7.460313ms
2025-07-08 19:47:53,879 - INFO - 提取callId: 3389, callTime: 7.4375ms
2025-07-08 19:47:53,879 - INFO - 提取callId: 3245, callTime: 7.384531ms
2025-07-08 19:47:53,879 - INFO - 提取到 40 个event相关的callId
2025-07-08 19:47:53,879 - INFO - 处理 callId=2181, callTime=43.857343ms, rank=1
2025-07-08 19:47:53,879 - INFO - 执行详细分析: callId=2181
2025-07-08 19:47:53,906 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\1-2181.txt
2025-07-08 19:47:53,906 - INFO - 处理 callId=3069, callTime=34.023594ms, rank=2
2025-07-08 19:47:53,907 - INFO - 执行详细分析: callId=3069
2025-07-08 19:47:53,917 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\2-3069.txt
2025-07-08 19:47:53,918 - INFO - 处理 callId=14, callTime=24.928281ms, rank=3
2025-07-08 19:47:53,918 - INFO - 执行详细分析: callId=14
2025-07-08 19:47:53,933 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\3-14.txt
2025-07-08 19:47:53,933 - INFO - 处理 callId=450, callTime=15.360469ms, rank=4
2025-07-08 19:47:53,933 - INFO - 执行详细分析: callId=450
2025-07-08 19:47:53,948 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\4-450.txt
2025-07-08 19:47:53,949 - INFO - 处理 callId=3221, callTime=13.602031ms, rank=5
2025-07-08 19:47:53,949 - INFO - 执行详细分析: callId=3221
2025-07-08 19:47:53,960 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\5-3221.txt
2025-07-08 19:47:53,960 - INFO - 处理 callId=886, callTime=13.474531ms, rank=6
2025-07-08 19:47:53,960 - INFO - 执行详细分析: callId=886
2025-07-08 19:47:53,975 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\6-886.txt
2025-07-08 19:47:53,975 - INFO - 处理 callId=3217, callTime=12.9625ms, rank=7
2025-07-08 19:47:53,975 - INFO - 执行详细分析: callId=3217
2025-07-08 19:47:53,987 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\7-3217.txt
2025-07-08 19:47:53,987 - INFO - 处理 callId=1303, callTime=12.110781ms, rank=8
2025-07-08 19:47:53,987 - INFO - 执行详细分析: callId=1303
2025-07-08 19:47:54,003 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\8-1303.txt
2025-07-08 19:47:54,003 - INFO - 处理 callId=3077, callTime=11.017968ms, rank=9
2025-07-08 19:47:54,004 - INFO - 执行详细分析: callId=3077
2025-07-08 19:47:54,015 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\9-3077.txt
2025-07-08 19:47:54,015 - INFO - 处理 callId=1764, callTime=10.63875ms, rank=10
2025-07-08 19:47:54,016 - INFO - 执行详细分析: callId=1764
2025-07-08 19:47:54,033 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\10-1764.txt
2025-07-08 19:47:54,033 - INFO - 处理 callId=3137, callTime=9.913281ms, rank=11
2025-07-08 19:47:54,033 - INFO - 执行详细分析: callId=3137
2025-07-08 19:47:54,045 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\11-3137.txt
2025-07-08 19:47:54,045 - INFO - 处理 callId=3129, callTime=9.614844ms, rank=12
2025-07-08 19:47:54,045 - INFO - 执行详细分析: callId=3129
2025-07-08 19:47:54,056 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\12-3129.txt
2025-07-08 19:47:54,056 - INFO - 处理 callId=3277, callTime=9.441563ms, rank=13
2025-07-08 19:47:54,056 - INFO - 执行详细分析: callId=3277
2025-07-08 19:47:54,067 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\13-3277.txt
2025-07-08 19:47:54,067 - INFO - 处理 callId=3201, callTime=9.171407ms, rank=14
2025-07-08 19:47:54,068 - INFO - 执行详细分析: callId=3201
2025-07-08 19:47:54,078 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\14-3201.txt
2025-07-08 19:47:54,079 - INFO - 处理 callId=3273, callTime=9.065782ms, rank=15
2025-07-08 19:47:54,079 - INFO - 执行详细分析: callId=3273
2025-07-08 19:47:54,089 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\15-3273.txt
2025-07-08 19:47:54,089 - INFO - 处理 callId=3209, callTime=8.918906ms, rank=16
2025-07-08 19:47:54,090 - INFO - 执行详细分析: callId=3209
2025-07-08 19:47:54,100 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\16-3209.txt
2025-07-08 19:47:54,101 - INFO - 处理 callId=3185, callTime=8.814687ms, rank=17
2025-07-08 19:47:54,101 - INFO - 执行详细分析: callId=3185
2025-07-08 19:47:54,112 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\17-3185.txt
2025-07-08 19:47:54,112 - INFO - 处理 callId=3165, callTime=8.780625ms, rank=18
2025-07-08 19:47:54,113 - INFO - 执行详细分析: callId=3165
2025-07-08 19:47:54,123 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\18-3165.txt
2025-07-08 19:47:54,124 - INFO - 处理 callId=3145, callTime=8.649531ms, rank=19
2025-07-08 19:47:54,124 - INFO - 执行详细分析: callId=3145
2025-07-08 19:47:54,135 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\19-3145.txt
2025-07-08 19:47:54,135 - INFO - 处理 callId=3081, callTime=8.649219ms, rank=20
2025-07-08 19:47:54,135 - INFO - 执行详细分析: callId=3081
2025-07-08 19:47:54,146 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\20-3081.txt
2025-07-08 19:47:54,146 - INFO - 处理 callId=3253, callTime=8.584219ms, rank=21
2025-07-08 19:47:54,146 - INFO - 执行详细分析: callId=3253
2025-07-08 19:47:54,157 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\21-3253.txt
2025-07-08 19:47:54,157 - INFO - 处理 callId=3233, callTime=8.554375ms, rank=22
2025-07-08 19:47:54,157 - INFO - 执行详细分析: callId=3233
2025-07-08 19:47:54,168 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\22-3233.txt
2025-07-08 19:47:54,169 - INFO - 处理 callId=3197, callTime=8.499531ms, rank=23
2025-07-08 19:47:54,169 - INFO - 执行详细分析: callId=3197
2025-07-08 19:47:54,180 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\23-3197.txt
2025-07-08 19:47:54,180 - INFO - 处理 callId=3133, callTime=8.358438ms, rank=24
2025-07-08 19:47:54,180 - INFO - 执行详细分析: callId=3133
2025-07-08 19:47:54,191 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\24-3133.txt
2025-07-08 19:47:54,191 - INFO - 处理 callId=3121, callTime=8.3075ms, rank=25
2025-07-08 19:47:54,191 - INFO - 执行详细分析: callId=3121
2025-07-08 19:47:54,202 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\25-3121.txt
2025-07-08 19:47:54,202 - INFO - 处理 callId=3177, callTime=8.305781ms, rank=26
2025-07-08 19:47:54,202 - INFO - 执行详细分析: callId=3177
2025-07-08 19:47:54,213 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\26-3177.txt
2025-07-08 19:47:54,213 - INFO - 处理 callId=3125, callTime=8.215781ms, rank=27
2025-07-08 19:47:54,213 - INFO - 执行详细分析: callId=3125
2025-07-08 19:47:54,224 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\27-3125.txt
2025-07-08 19:47:54,224 - INFO - 处理 callId=3289, callTime=8.021719ms, rank=28
2025-07-08 19:47:54,224 - INFO - 执行详细分析: callId=3289
2025-07-08 19:47:54,235 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\28-3289.txt
2025-07-08 19:47:54,235 - INFO - 处理 callId=3153, callTime=7.997656ms, rank=29
2025-07-08 19:47:54,235 - INFO - 执行详细分析: callId=3153
2025-07-08 19:47:54,246 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\29-3153.txt
2025-07-08 19:47:54,246 - INFO - 处理 callId=3189, callTime=7.9625ms, rank=30
2025-07-08 19:47:54,246 - INFO - 执行详细分析: callId=3189
2025-07-08 19:47:54,257 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\30-3189.txt
2025-07-08 19:47:54,257 - INFO - 处理 callId=3261, callTime=7.916094ms, rank=31
2025-07-08 19:47:54,257 - INFO - 执行详细分析: callId=3261
2025-07-08 19:47:54,269 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\31-3261.txt
2025-07-08 19:47:54,269 - INFO - 处理 callId=3237, callTime=7.806093ms, rank=32
2025-07-08 19:47:54,269 - INFO - 执行详细分析: callId=3237
2025-07-08 19:47:54,281 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\32-3237.txt
2025-07-08 19:47:54,282 - INFO - 处理 callId=3225, callTime=7.707812ms, rank=33
2025-07-08 19:47:54,282 - INFO - 执行详细分析: callId=3225
2025-07-08 19:47:54,294 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\33-3225.txt
2025-07-08 19:47:54,294 - INFO - 处理 callId=3249, callTime=7.599844ms, rank=34
2025-07-08 19:47:54,294 - INFO - 执行详细分析: callId=3249
2025-07-08 19:47:54,306 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\34-3249.txt
2025-07-08 19:47:54,306 - INFO - 处理 callId=3169, callTime=7.590156ms, rank=35
2025-07-08 19:47:54,306 - INFO - 执行详细分析: callId=3169
2025-07-08 19:47:54,318 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\35-3169.txt
2025-07-08 19:47:54,318 - INFO - 处理 callId=1739, callTime=7.579844ms, rank=36
2025-07-08 19:47:54,318 - INFO - 执行详细分析: callId=1739
2025-07-08 19:47:54,328 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\36-1739.txt
2025-07-08 19:47:54,328 - INFO - 处理 callId=3073, callTime=7.499687ms, rank=37
2025-07-08 19:47:54,329 - INFO - 执行详细分析: callId=3073
2025-07-08 19:47:54,340 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\37-3073.txt
2025-07-08 19:47:54,340 - INFO - 处理 callId=3241, callTime=7.460313ms, rank=38
2025-07-08 19:47:54,340 - INFO - 执行详细分析: callId=3241
2025-07-08 19:47:54,351 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\38-3241.txt
2025-07-08 19:47:54,351 - INFO - 处理 callId=3389, callTime=7.4375ms, rank=39
2025-07-08 19:47:54,351 - INFO - 执行详细分析: callId=3389
2025-07-08 19:47:54,363 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\39-3389.txt
2025-07-08 19:47:54,363 - INFO - 处理 callId=3245, callTime=7.384531ms, rank=40
2025-07-08 19:47:54,363 - INFO - 执行详细分析: callId=3245
2025-07-08 19:47:54,384 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_095208-wpp文档绘制\40-3245.txt
2025-07-08 19:47:54,385 - INFO - 完成处理文件: perfmon-raster-20250707_095208-wpp文档绘制.dat
2025-07-08 19:47:54,385 - INFO - 处理进度: 15/30
2025-07-08 19:47:54,385 - INFO - 开始处理文件: perfmon-raster-20250707_101228-wpp旋转.dat
2025-07-08 19:47:54,385 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-raster-20250707_101228-wpp旋转.dat
2025-07-08 19:47:54,424 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-raster-20250707_101228-wpp旋转.dat.txt
2025-07-08 19:47:54,424 - INFO - 找到event函数: E:\wpsskia\qt\source\qtbase\src\widgets\kernel\qwidget.cpp:9682 event
2025-07-08 19:47:54,424 - INFO - 提取callId: 17321, callTime: 53.58625ms
2025-07-08 19:47:54,424 - INFO - 提取callId: 18629, callTime: 35.810313ms
2025-07-08 19:47:54,424 - INFO - 提取callId: 18209, callTime: 33.041094ms
2025-07-08 19:47:54,424 - INFO - 提取callId: 20297, callTime: 27.32625ms
2025-07-08 19:47:54,424 - INFO - 提取callId: 14, callTime: 25.202813ms
2025-07-08 19:47:54,425 - INFO - 提取callId: 20225, callTime: 24.716718ms
2025-07-08 19:47:54,425 - INFO - 提取callId: 18597, callTime: 23.349843ms
2025-07-08 19:47:54,425 - INFO - 提取callId: 20314, callTime: 22.540157ms
2025-07-08 19:47:54,425 - INFO - 提取callId: 20237, callTime: 21.377031ms
2025-07-08 19:47:54,425 - INFO - 提取callId: 18581, callTime: 20.351563ms
2025-07-08 19:47:54,425 - INFO - 提取callId: 19957, callTime: 17.394063ms
2025-07-08 19:47:54,425 - INFO - 提取callId: 18677, callTime: 17.084844ms
2025-07-08 19:47:54,425 - INFO - 提取callId: 19157, callTime: 17.002031ms
2025-07-08 19:47:54,425 - INFO - 提取callId: 19077, callTime: 16.935625ms
2025-07-08 19:47:54,425 - INFO - 提取callId: 18357, callTime: 16.614375ms
2025-07-08 19:47:54,425 - INFO - 提取callId: 19065, callTime: 16.358281ms
2025-07-08 19:47:54,425 - INFO - 提取callId: 19897, callTime: 16.337187ms
2025-07-08 19:47:54,425 - INFO - 提取callId: 19401, callTime: 16.333281ms
2025-07-08 19:47:54,426 - INFO - 提取callId: 19689, callTime: 16.329687ms
2025-07-08 19:47:54,426 - INFO - 提取callId: 19849, callTime: 16.295937ms
2025-07-08 19:47:54,426 - INFO - 提取callId: 19589, callTime: 16.211406ms
2025-07-08 19:47:54,426 - INFO - 提取callId: 18809, callTime: 16.168281ms
2025-07-08 19:47:54,426 - INFO - 提取callId: 19257, callTime: 16.138438ms
2025-07-08 19:47:54,426 - INFO - 提取callId: 19517, callTime: 16.126406ms
2025-07-08 19:47:54,426 - INFO - 提取callId: 19469, callTime: 16.106718ms
2025-07-08 19:47:54,426 - INFO - 提取callId: 18745, callTime: 15.960781ms
2025-07-08 19:47:54,426 - INFO - 提取callId: 19817, callTime: 15.836719ms
2025-07-08 19:47:54,427 - INFO - 提取callId: 19285, callTime: 15.83375ms
2025-07-08 19:47:54,427 - INFO - 提取callId: 18665, callTime: 15.824688ms
2025-07-08 19:47:54,427 - INFO - 提取callId: 19217, callTime: 15.802968ms
2025-07-08 19:47:54,427 - INFO - 提取callId: 20145, callTime: 15.773437ms
2025-07-08 19:47:54,427 - INFO - 提取callId: 20093, callTime: 15.73875ms
2025-07-08 19:47:54,427 - INFO - 提取callId: 20149, callTime: 15.653125ms
2025-07-08 19:47:54,427 - INFO - 提取callId: 18921, callTime: 15.431093ms
2025-07-08 19:47:54,427 - INFO - 提取callId: 20141, callTime: 15.390313ms
2025-07-08 19:47:54,427 - INFO - 提取callId: 450, callTime: 15.34875ms
2025-07-08 19:47:54,427 - INFO - 提取callId: 20025, callTime: 15.217812ms
2025-07-08 19:47:54,428 - INFO - 提取callId: 19161, callTime: 15.194687ms
2025-07-08 19:47:54,428 - INFO - 提取callId: 18661, callTime: 15.091094ms
2025-07-08 19:47:54,428 - INFO - 提取callId: 18829, callTime: 15.090156ms
2025-07-08 19:47:54,428 - INFO - 提取到 40 个event相关的callId
2025-07-08 19:47:54,428 - INFO - 处理 callId=17321, callTime=53.58625ms, rank=1
2025-07-08 19:47:54,428 - INFO - 执行详细分析: callId=17321
2025-07-08 19:47:54,467 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\1-17321.txt
2025-07-08 19:47:54,467 - INFO - 处理 callId=18629, callTime=35.810313ms, rank=2
2025-07-08 19:47:54,467 - INFO - 执行详细分析: callId=18629
2025-07-08 19:47:54,490 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\2-18629.txt
2025-07-08 19:47:54,490 - INFO - 处理 callId=18209, callTime=33.041094ms, rank=3
2025-07-08 19:47:54,490 - INFO - 执行详细分析: callId=18209
2025-07-08 19:47:54,513 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\3-18209.txt
2025-07-08 19:47:54,513 - INFO - 处理 callId=20297, callTime=27.32625ms, rank=4
2025-07-08 19:47:54,513 - INFO - 执行详细分析: callId=20297
2025-07-08 19:47:54,537 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\4-20297.txt
2025-07-08 19:47:54,538 - INFO - 处理 callId=14, callTime=25.202813ms, rank=5
2025-07-08 19:47:54,538 - INFO - 执行详细分析: callId=14
2025-07-08 19:47:54,554 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\5-14.txt
2025-07-08 19:47:54,554 - INFO - 处理 callId=20225, callTime=24.716718ms, rank=6
2025-07-08 19:47:54,554 - INFO - 执行详细分析: callId=20225
2025-07-08 19:47:54,579 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\6-20225.txt
2025-07-08 19:47:54,579 - INFO - 处理 callId=18597, callTime=23.349843ms, rank=7
2025-07-08 19:47:54,579 - INFO - 执行详细分析: callId=18597
2025-07-08 19:47:54,602 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\7-18597.txt
2025-07-08 19:47:54,602 - INFO - 处理 callId=20314, callTime=22.540157ms, rank=8
2025-07-08 19:47:54,602 - INFO - 执行详细分析: callId=20314
2025-07-08 19:47:54,627 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\8-20314.txt
2025-07-08 19:47:54,627 - INFO - 处理 callId=20237, callTime=21.377031ms, rank=9
2025-07-08 19:47:54,627 - INFO - 执行详细分析: callId=20237
2025-07-08 19:47:54,651 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\9-20237.txt
2025-07-08 19:47:54,652 - INFO - 处理 callId=18581, callTime=20.351563ms, rank=10
2025-07-08 19:47:54,652 - INFO - 执行详细分析: callId=18581
2025-07-08 19:47:54,675 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\10-18581.txt
2025-07-08 19:47:54,675 - INFO - 处理 callId=19957, callTime=17.394063ms, rank=11
2025-07-08 19:47:54,675 - INFO - 执行详细分析: callId=19957
2025-07-08 19:47:54,699 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\11-19957.txt
2025-07-08 19:47:54,699 - INFO - 处理 callId=18677, callTime=17.084844ms, rank=12
2025-07-08 19:47:54,699 - INFO - 执行详细分析: callId=18677
2025-07-08 19:47:54,722 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\12-18677.txt
2025-07-08 19:47:54,723 - INFO - 处理 callId=19157, callTime=17.002031ms, rank=13
2025-07-08 19:47:54,723 - INFO - 执行详细分析: callId=19157
2025-07-08 19:47:54,746 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\13-19157.txt
2025-07-08 19:47:54,747 - INFO - 处理 callId=19077, callTime=16.935625ms, rank=14
2025-07-08 19:47:54,747 - INFO - 执行详细分析: callId=19077
2025-07-08 19:47:54,769 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\14-19077.txt
2025-07-08 19:47:54,769 - INFO - 处理 callId=18357, callTime=16.614375ms, rank=15
2025-07-08 19:47:54,769 - INFO - 执行详细分析: callId=18357
2025-07-08 19:47:54,792 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\15-18357.txt
2025-07-08 19:47:54,792 - INFO - 处理 callId=19065, callTime=16.358281ms, rank=16
2025-07-08 19:47:54,792 - INFO - 执行详细分析: callId=19065
2025-07-08 19:47:54,815 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\16-19065.txt
2025-07-08 19:47:54,815 - INFO - 处理 callId=19897, callTime=16.337187ms, rank=17
2025-07-08 19:47:54,815 - INFO - 执行详细分析: callId=19897
2025-07-08 19:47:54,839 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\17-19897.txt
2025-07-08 19:47:54,839 - INFO - 处理 callId=19401, callTime=16.333281ms, rank=18
2025-07-08 19:47:54,839 - INFO - 执行详细分析: callId=19401
2025-07-08 19:47:54,863 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\18-19401.txt
2025-07-08 19:47:54,863 - INFO - 处理 callId=19689, callTime=16.329687ms, rank=19
2025-07-08 19:47:54,863 - INFO - 执行详细分析: callId=19689
2025-07-08 19:47:54,890 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\19-19689.txt
2025-07-08 19:47:54,890 - INFO - 处理 callId=19849, callTime=16.295937ms, rank=20
2025-07-08 19:47:54,890 - INFO - 执行详细分析: callId=19849
2025-07-08 19:47:54,915 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\20-19849.txt
2025-07-08 19:47:54,915 - INFO - 处理 callId=19589, callTime=16.211406ms, rank=21
2025-07-08 19:47:54,915 - INFO - 执行详细分析: callId=19589
2025-07-08 19:47:54,940 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\21-19589.txt
2025-07-08 19:47:54,940 - INFO - 处理 callId=18809, callTime=16.168281ms, rank=22
2025-07-08 19:47:54,940 - INFO - 执行详细分析: callId=18809
2025-07-08 19:47:54,963 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\22-18809.txt
2025-07-08 19:47:54,963 - INFO - 处理 callId=19257, callTime=16.138438ms, rank=23
2025-07-08 19:47:54,963 - INFO - 执行详细分析: callId=19257
2025-07-08 19:47:54,986 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\23-19257.txt
2025-07-08 19:47:54,987 - INFO - 处理 callId=19517, callTime=16.126406ms, rank=24
2025-07-08 19:47:54,987 - INFO - 执行详细分析: callId=19517
2025-07-08 19:47:55,011 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\24-19517.txt
2025-07-08 19:47:55,011 - INFO - 处理 callId=19469, callTime=16.106718ms, rank=25
2025-07-08 19:47:55,011 - INFO - 执行详细分析: callId=19469
2025-07-08 19:47:55,035 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\25-19469.txt
2025-07-08 19:47:55,035 - INFO - 处理 callId=18745, callTime=15.960781ms, rank=26
2025-07-08 19:47:55,036 - INFO - 执行详细分析: callId=18745
2025-07-08 19:47:55,058 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\26-18745.txt
2025-07-08 19:47:55,059 - INFO - 处理 callId=19817, callTime=15.836719ms, rank=27
2025-07-08 19:47:55,059 - INFO - 执行详细分析: callId=19817
2025-07-08 19:47:55,082 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\27-19817.txt
2025-07-08 19:47:55,082 - INFO - 处理 callId=19285, callTime=15.83375ms, rank=28
2025-07-08 19:47:55,082 - INFO - 执行详细分析: callId=19285
2025-07-08 19:47:55,105 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\28-19285.txt
2025-07-08 19:47:55,105 - INFO - 处理 callId=18665, callTime=15.824688ms, rank=29
2025-07-08 19:47:55,105 - INFO - 执行详细分析: callId=18665
2025-07-08 19:47:55,128 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\29-18665.txt
2025-07-08 19:47:55,128 - INFO - 处理 callId=19217, callTime=15.802968ms, rank=30
2025-07-08 19:47:55,128 - INFO - 执行详细分析: callId=19217
2025-07-08 19:47:55,152 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\30-19217.txt
2025-07-08 19:47:55,152 - INFO - 处理 callId=20145, callTime=15.773437ms, rank=31
2025-07-08 19:47:55,152 - INFO - 执行详细分析: callId=20145
2025-07-08 19:47:55,176 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\31-20145.txt
2025-07-08 19:47:55,176 - INFO - 处理 callId=20093, callTime=15.73875ms, rank=32
2025-07-08 19:47:55,176 - INFO - 执行详细分析: callId=20093
2025-07-08 19:47:55,200 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\32-20093.txt
2025-07-08 19:47:55,200 - INFO - 处理 callId=20149, callTime=15.653125ms, rank=33
2025-07-08 19:47:55,201 - INFO - 执行详细分析: callId=20149
2025-07-08 19:47:55,224 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\33-20149.txt
2025-07-08 19:47:55,225 - INFO - 处理 callId=18921, callTime=15.431093ms, rank=34
2025-07-08 19:47:55,225 - INFO - 执行详细分析: callId=18921
2025-07-08 19:47:55,248 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\34-18921.txt
2025-07-08 19:47:55,248 - INFO - 处理 callId=20141, callTime=15.390313ms, rank=35
2025-07-08 19:47:55,248 - INFO - 执行详细分析: callId=20141
2025-07-08 19:47:55,272 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\35-20141.txt
2025-07-08 19:47:55,272 - INFO - 处理 callId=450, callTime=15.34875ms, rank=36
2025-07-08 19:47:55,272 - INFO - 执行详细分析: callId=450
2025-07-08 19:47:55,289 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\36-450.txt
2025-07-08 19:47:55,289 - INFO - 处理 callId=20025, callTime=15.217812ms, rank=37
2025-07-08 19:47:55,289 - INFO - 执行详细分析: callId=20025
2025-07-08 19:47:55,313 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\37-20025.txt
2025-07-08 19:47:55,313 - INFO - 处理 callId=19161, callTime=15.194687ms, rank=38
2025-07-08 19:47:55,313 - INFO - 执行详细分析: callId=19161
2025-07-08 19:47:55,335 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\38-19161.txt
2025-07-08 19:47:55,335 - INFO - 处理 callId=18661, callTime=15.091094ms, rank=39
2025-07-08 19:47:55,335 - INFO - 执行详细分析: callId=18661
2025-07-08 19:47:55,357 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\39-18661.txt
2025-07-08 19:47:55,358 - INFO - 处理 callId=18829, callTime=15.090156ms, rank=40
2025-07-08 19:47:55,358 - INFO - 执行详细分析: callId=18829
2025-07-08 19:47:55,380 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101228-wpp旋转\40-18829.txt
2025-07-08 19:47:55,380 - INFO - 完成处理文件: perfmon-raster-20250707_101228-wpp旋转.dat
2025-07-08 19:47:55,380 - INFO - 处理进度: 16/30
2025-07-08 19:47:55,380 - INFO - 开始处理文件: perfmon-raster-20250707_101348_wpp缩放.dat
2025-07-08 19:47:55,380 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-raster-20250707_101348_wpp缩放.dat
2025-07-08 19:47:55,395 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-raster-20250707_101348_wpp缩放.dat.txt
2025-07-08 19:47:55,395 - INFO - 找到event函数: E:\wpsskia\qt\source\qtbase\src\widgets\kernel\qwidget.cpp:9682 event
2025-07-08 19:47:55,395 - INFO - 提取callId: 3625, callTime: 150.752656ms
2025-07-08 19:47:55,395 - INFO - 提取callId: 3649, callTime: 133.1725ms
2025-07-08 19:47:55,395 - INFO - 提取callId: 3621, callTime: 123.087187ms
2025-07-08 19:47:55,395 - INFO - 提取callId: 3641, callTime: 110.026563ms
2025-07-08 19:47:55,396 - INFO - 提取callId: 3645, callTime: 109.344062ms
2025-07-08 19:47:55,396 - INFO - 提取callId: 3629, callTime: 108.416875ms
2025-07-08 19:47:55,396 - INFO - 提取callId: 2189, callTime: 57.770313ms
2025-07-08 19:47:55,396 - INFO - 提取callId: 3605, callTime: 44.280781ms
2025-07-08 19:47:55,396 - INFO - 提取callId: 3077, callTime: 32.935781ms
2025-07-08 19:47:55,396 - INFO - 提取callId: 3609, callTime: 26.461875ms
2025-07-08 19:47:55,396 - INFO - 提取callId: 14, callTime: 26.136563ms
2025-07-08 19:47:55,396 - INFO - 提取callId: 3653, callTime: 25.03125ms
2025-07-08 19:47:55,396 - INFO - 提取callId: 3341, callTime: 24.112656ms
2025-07-08 19:47:55,396 - INFO - 提取callId: 3457, callTime: 22.779375ms
2025-07-08 19:47:55,397 - INFO - 提取callId: 3469, callTime: 20.178281ms
2025-07-08 19:47:55,397 - INFO - 提取callId: 3481, callTime: 19.962813ms
2025-07-08 19:47:55,397 - INFO - 提取callId: 3665, callTime: 18.368125ms
2025-07-08 19:47:55,397 - INFO - 提取callId: 3669, callTime: 17.376094ms
2025-07-08 19:47:55,397 - INFO - 提取callId: 3493, callTime: 16.295469ms
2025-07-08 19:47:55,397 - INFO - 提取callId: 3593, callTime: 16.025ms
2025-07-08 19:47:55,397 - INFO - 提取callId: 3581, callTime: 15.178281ms
2025-07-08 19:47:55,397 - INFO - 提取callId: 450, callTime: 15.063594ms
2025-07-08 19:47:55,397 - INFO - 提取callId: 1303, callTime: 13.747813ms
2025-07-08 19:47:55,397 - INFO - 提取callId: 886, callTime: 13.523906ms
2025-07-08 19:47:55,397 - INFO - 提取callId: 3225, callTime: 13.154375ms
2025-07-08 19:47:55,398 - INFO - 提取callId: 3521, callTime: 13.103438ms
2025-07-08 19:47:55,398 - INFO - 提取callId: 3505, callTime: 12.986718ms
2025-07-08 19:47:55,398 - INFO - 提取callId: 3673, callTime: 12.907188ms
2025-07-08 19:47:55,398 - INFO - 提取callId: 3685, callTime: 12.781094ms
2025-07-08 19:47:55,398 - INFO - 提取callId: 3517, callTime: 12.329375ms
2025-07-08 19:47:55,398 - INFO - 提取callId: 3569, callTime: 11.410625ms
2025-07-08 19:47:55,398 - INFO - 提取callId: 3085, callTime: 11.352344ms
2025-07-08 19:47:55,398 - INFO - 提取callId: 3537, callTime: 11.008125ms
2025-07-08 19:47:55,398 - INFO - 提取callId: 3565, callTime: 10.825156ms
2025-07-08 19:47:55,398 - INFO - 提取callId: 3689, callTime: 10.417031ms
2025-07-08 19:47:55,398 - INFO - 提取callId: 3701, callTime: 10.370157ms
2025-07-08 19:47:55,398 - INFO - 提取callId: 3221, callTime: 10.284843ms
2025-07-08 19:47:55,398 - INFO - 提取callId: 3209, callTime: 10.155469ms
2025-07-08 19:47:55,398 - INFO - 提取callId: 3145, callTime: 10.084219ms
2025-07-08 19:47:55,399 - INFO - 提取callId: 3293, callTime: 9.688906ms
2025-07-08 19:47:55,399 - INFO - 提取到 40 个event相关的callId
2025-07-08 19:47:55,399 - INFO - 处理 callId=3625, callTime=150.752656ms, rank=1
2025-07-08 19:47:55,399 - INFO - 执行详细分析: callId=3625
2025-07-08 19:47:55,411 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\1-3625.txt
2025-07-08 19:47:55,411 - INFO - 处理 callId=3649, callTime=133.1725ms, rank=2
2025-07-08 19:47:55,411 - INFO - 执行详细分析: callId=3649
2025-07-08 19:47:55,423 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\2-3649.txt
2025-07-08 19:47:55,423 - INFO - 处理 callId=3621, callTime=123.087187ms, rank=3
2025-07-08 19:47:55,423 - INFO - 执行详细分析: callId=3621
2025-07-08 19:47:55,434 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\3-3621.txt
2025-07-08 19:47:55,434 - INFO - 处理 callId=3641, callTime=110.026563ms, rank=4
2025-07-08 19:47:55,435 - INFO - 执行详细分析: callId=3641
2025-07-08 19:47:55,446 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\4-3641.txt
2025-07-08 19:47:55,446 - INFO - 处理 callId=3645, callTime=109.344062ms, rank=5
2025-07-08 19:47:55,446 - INFO - 执行详细分析: callId=3645
2025-07-08 19:47:55,458 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\5-3645.txt
2025-07-08 19:47:55,459 - INFO - 处理 callId=3629, callTime=108.416875ms, rank=6
2025-07-08 19:47:55,459 - INFO - 执行详细分析: callId=3629
2025-07-08 19:47:55,470 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\6-3629.txt
2025-07-08 19:47:55,470 - INFO - 处理 callId=2189, callTime=57.770313ms, rank=7
2025-07-08 19:47:55,470 - INFO - 执行详细分析: callId=2189
2025-07-08 19:47:55,498 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\7-2189.txt
2025-07-08 19:47:55,498 - INFO - 处理 callId=3605, callTime=44.280781ms, rank=8
2025-07-08 19:47:55,498 - INFO - 执行详细分析: callId=3605
2025-07-08 19:47:55,510 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\8-3605.txt
2025-07-08 19:47:55,510 - INFO - 处理 callId=3077, callTime=32.935781ms, rank=9
2025-07-08 19:47:55,510 - INFO - 执行详细分析: callId=3077
2025-07-08 19:47:55,521 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\9-3077.txt
2025-07-08 19:47:55,521 - INFO - 处理 callId=3609, callTime=26.461875ms, rank=10
2025-07-08 19:47:55,521 - INFO - 执行详细分析: callId=3609
2025-07-08 19:47:55,532 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\10-3609.txt
2025-07-08 19:47:55,532 - INFO - 处理 callId=14, callTime=26.136563ms, rank=11
2025-07-08 19:47:55,533 - INFO - 执行详细分析: callId=14
2025-07-08 19:47:55,547 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\11-14.txt
2025-07-08 19:47:55,547 - INFO - 处理 callId=3653, callTime=25.03125ms, rank=12
2025-07-08 19:47:55,547 - INFO - 执行详细分析: callId=3653
2025-07-08 19:47:55,559 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\12-3653.txt
2025-07-08 19:47:55,559 - INFO - 处理 callId=3341, callTime=24.112656ms, rank=13
2025-07-08 19:47:55,559 - INFO - 执行详细分析: callId=3341
2025-07-08 19:47:55,570 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\13-3341.txt
2025-07-08 19:47:55,570 - INFO - 处理 callId=3457, callTime=22.779375ms, rank=14
2025-07-08 19:47:55,570 - INFO - 执行详细分析: callId=3457
2025-07-08 19:47:55,581 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\14-3457.txt
2025-07-08 19:47:55,581 - INFO - 处理 callId=3469, callTime=20.178281ms, rank=15
2025-07-08 19:47:55,582 - INFO - 执行详细分析: callId=3469
2025-07-08 19:47:55,592 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\15-3469.txt
2025-07-08 19:47:55,592 - INFO - 处理 callId=3481, callTime=19.962813ms, rank=16
2025-07-08 19:47:55,593 - INFO - 执行详细分析: callId=3481
2025-07-08 19:47:55,604 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\16-3481.txt
2025-07-08 19:47:55,604 - INFO - 处理 callId=3665, callTime=18.368125ms, rank=17
2025-07-08 19:47:55,604 - INFO - 执行详细分析: callId=3665
2025-07-08 19:47:55,615 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\17-3665.txt
2025-07-08 19:47:55,616 - INFO - 处理 callId=3669, callTime=17.376094ms, rank=18
2025-07-08 19:47:55,616 - INFO - 执行详细分析: callId=3669
2025-07-08 19:47:55,627 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\18-3669.txt
2025-07-08 19:47:55,627 - INFO - 处理 callId=3493, callTime=16.295469ms, rank=19
2025-07-08 19:47:55,627 - INFO - 执行详细分析: callId=3493
2025-07-08 19:47:55,638 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\19-3493.txt
2025-07-08 19:47:55,638 - INFO - 处理 callId=3593, callTime=16.025ms, rank=20
2025-07-08 19:47:55,638 - INFO - 执行详细分析: callId=3593
2025-07-08 19:47:55,650 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\20-3593.txt
2025-07-08 19:47:55,650 - INFO - 处理 callId=3581, callTime=15.178281ms, rank=21
2025-07-08 19:47:55,650 - INFO - 执行详细分析: callId=3581
2025-07-08 19:47:55,661 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\21-3581.txt
2025-07-08 19:47:55,661 - INFO - 处理 callId=450, callTime=15.063594ms, rank=22
2025-07-08 19:47:55,661 - INFO - 执行详细分析: callId=450
2025-07-08 19:47:55,676 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\22-450.txt
2025-07-08 19:47:55,677 - INFO - 处理 callId=1303, callTime=13.747813ms, rank=23
2025-07-08 19:47:55,677 - INFO - 执行详细分析: callId=1303
2025-07-08 19:47:55,693 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\23-1303.txt
2025-07-08 19:47:55,693 - INFO - 处理 callId=886, callTime=13.523906ms, rank=24
2025-07-08 19:47:55,693 - INFO - 执行详细分析: callId=886
2025-07-08 19:47:55,709 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\24-886.txt
2025-07-08 19:47:55,709 - INFO - 处理 callId=3225, callTime=13.154375ms, rank=25
2025-07-08 19:47:55,710 - INFO - 执行详细分析: callId=3225
2025-07-08 19:47:55,721 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\25-3225.txt
2025-07-08 19:47:55,721 - INFO - 处理 callId=3521, callTime=13.103438ms, rank=26
2025-07-08 19:47:55,721 - INFO - 执行详细分析: callId=3521
2025-07-08 19:47:55,733 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\26-3521.txt
2025-07-08 19:47:55,733 - INFO - 处理 callId=3505, callTime=12.986718ms, rank=27
2025-07-08 19:47:55,733 - INFO - 执行详细分析: callId=3505
2025-07-08 19:47:55,745 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\27-3505.txt
2025-07-08 19:47:55,745 - INFO - 处理 callId=3673, callTime=12.907188ms, rank=28
2025-07-08 19:47:55,745 - INFO - 执行详细分析: callId=3673
2025-07-08 19:47:55,756 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\28-3673.txt
2025-07-08 19:47:55,756 - INFO - 处理 callId=3685, callTime=12.781094ms, rank=29
2025-07-08 19:47:55,756 - INFO - 执行详细分析: callId=3685
2025-07-08 19:47:55,768 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\29-3685.txt
2025-07-08 19:47:55,768 - INFO - 处理 callId=3517, callTime=12.329375ms, rank=30
2025-07-08 19:47:55,768 - INFO - 执行详细分析: callId=3517
2025-07-08 19:47:55,779 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\30-3517.txt
2025-07-08 19:47:55,779 - INFO - 处理 callId=3569, callTime=11.410625ms, rank=31
2025-07-08 19:47:55,779 - INFO - 执行详细分析: callId=3569
2025-07-08 19:47:55,790 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\31-3569.txt
2025-07-08 19:47:55,790 - INFO - 处理 callId=3085, callTime=11.352344ms, rank=32
2025-07-08 19:47:55,790 - INFO - 执行详细分析: callId=3085
2025-07-08 19:47:55,801 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\32-3085.txt
2025-07-08 19:47:55,801 - INFO - 处理 callId=3537, callTime=11.008125ms, rank=33
2025-07-08 19:47:55,801 - INFO - 执行详细分析: callId=3537
2025-07-08 19:47:55,812 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\33-3537.txt
2025-07-08 19:47:55,812 - INFO - 处理 callId=3565, callTime=10.825156ms, rank=34
2025-07-08 19:47:55,812 - INFO - 执行详细分析: callId=3565
2025-07-08 19:47:55,823 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\34-3565.txt
2025-07-08 19:47:55,823 - INFO - 处理 callId=3689, callTime=10.417031ms, rank=35
2025-07-08 19:47:55,823 - INFO - 执行详细分析: callId=3689
2025-07-08 19:47:55,834 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\35-3689.txt
2025-07-08 19:47:55,835 - INFO - 处理 callId=3701, callTime=10.370157ms, rank=36
2025-07-08 19:47:55,835 - INFO - 执行详细分析: callId=3701
2025-07-08 19:47:55,846 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\36-3701.txt
2025-07-08 19:47:55,846 - INFO - 处理 callId=3221, callTime=10.284843ms, rank=37
2025-07-08 19:47:55,846 - INFO - 执行详细分析: callId=3221
2025-07-08 19:47:55,857 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\37-3221.txt
2025-07-08 19:47:55,857 - INFO - 处理 callId=3209, callTime=10.155469ms, rank=38
2025-07-08 19:47:55,857 - INFO - 执行详细分析: callId=3209
2025-07-08 19:47:55,868 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\38-3209.txt
2025-07-08 19:47:55,868 - INFO - 处理 callId=3145, callTime=10.084219ms, rank=39
2025-07-08 19:47:55,868 - INFO - 执行详细分析: callId=3145
2025-07-08 19:47:55,879 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\39-3145.txt
2025-07-08 19:47:55,879 - INFO - 处理 callId=3293, callTime=9.688906ms, rank=40
2025-07-08 19:47:55,879 - INFO - 执行详细分析: callId=3293
2025-07-08 19:47:55,890 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101348_wpp缩放\40-3293.txt
2025-07-08 19:47:55,890 - INFO - 完成处理文件: perfmon-raster-20250707_101348_wpp缩放.dat
2025-07-08 19:47:55,890 - INFO - 处理进度: 17/30
2025-07-08 19:47:55,890 - INFO - 开始处理文件: perfmon-raster-20250707_101933_wpp切换页面.dat
2025-07-08 19:47:55,891 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-raster-20250707_101933_wpp切换页面.dat
2025-07-08 19:47:55,907 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-raster-20250707_101933_wpp切换页面.dat.txt
2025-07-08 19:47:55,907 - INFO - 找到event函数: E:\wpsskia\qt\source\qtbase\src\widgets\kernel\qwidget.cpp:9682 event
2025-07-08 19:47:55,907 - INFO - 提取callId: 4855, callTime: 133.352344ms
2025-07-08 19:47:55,908 - INFO - 提取callId: 4839, callTime: 101.153125ms
2025-07-08 19:47:55,908 - INFO - 提取callId: 5143, callTime: 82.995312ms
2025-07-08 19:47:55,908 - INFO - 提取callId: 4859, callTime: 63.297031ms
2025-07-08 19:47:55,908 - INFO - 提取callId: 5039, callTime: 57.298125ms
2025-07-08 19:47:55,908 - INFO - 提取callId: 5115, callTime: 56.533438ms
2025-07-08 19:47:55,908 - INFO - 提取callId: 3499, callTime: 48.505938ms
2025-07-08 19:47:55,908 - INFO - 提取callId: 4883, callTime: 46.550937ms
2025-07-08 19:47:55,908 - INFO - 提取callId: 5015, callTime: 44.624843ms
2025-07-08 19:47:55,908 - INFO - 提取callId: 5203, callTime: 41.652344ms
2025-07-08 19:47:55,909 - INFO - 提取callId: 4947, callTime: 37.559844ms
2025-07-08 19:47:55,909 - INFO - 提取callId: 5131, callTime: 36.786875ms
2025-07-08 19:47:55,909 - INFO - 提取callId: 4975, callTime: 35.977187ms
2025-07-08 19:47:55,909 - INFO - 提取callId: 4991, callTime: 33.997031ms
2025-07-08 19:47:55,909 - INFO - 提取callId: 4387, callTime: 32.978125ms
2025-07-08 19:47:55,909 - INFO - 提取callId: 5043, callTime: 30.971406ms
2025-07-08 19:47:55,909 - INFO - 提取callId: 4907, callTime: 30.520625ms
2025-07-08 19:47:55,909 - INFO - 提取callId: 5231, callTime: 30.433125ms
2025-07-08 19:47:55,909 - INFO - 提取callId: 5191, callTime: 29.531406ms
2025-07-08 19:47:55,909 - INFO - 提取callId: 4979, callTime: 29.095938ms
2025-07-08 19:47:55,909 - INFO - 提取callId: 5003, callTime: 27.950312ms
2025-07-08 19:47:55,909 - INFO - 提取callId: 4875, callTime: 27.721562ms
2025-07-08 19:47:55,910 - INFO - 提取callId: 4923, callTime: 27.569063ms
2025-07-08 19:47:55,910 - INFO - 提取callId: 4795, callTime: 27.19ms
2025-07-08 19:47:55,910 - INFO - 提取callId: 4807, callTime: 26.946093ms
2025-07-08 19:47:55,910 - INFO - 提取callId: 4951, callTime: 26.811406ms
2025-07-08 19:47:55,910 - INFO - 提取callId: 14, callTime: 26.180157ms
2025-07-08 19:47:55,910 - INFO - 提取callId: 5179, callTime: 25.884375ms
2025-07-08 19:47:55,910 - INFO - 提取callId: 5067, callTime: 25.572031ms
2025-07-08 19:47:55,910 - INFO - 提取callId: 4831, callTime: 25.322187ms
2025-07-08 19:47:55,911 - INFO - 提取callId: 5263, callTime: 25.140312ms
2025-07-08 19:47:55,911 - INFO - 提取callId: 4899, callTime: 24.639687ms
2025-07-08 19:47:55,911 - INFO - 提取callId: 5207, callTime: 24.625625ms
2025-07-08 19:47:55,911 - INFO - 提取callId: 5155, callTime: 24.416094ms
2025-07-08 19:47:55,911 - INFO - 提取callId: 5119, callTime: 24.314218ms
2025-07-08 19:47:55,911 - INFO - 提取callId: 4891, callTime: 24.141875ms
2025-07-08 19:47:55,911 - INFO - 提取callId: 5055, callTime: 23.947969ms
2025-07-08 19:47:55,911 - INFO - 提取callId: 4555, callTime: 23.9125ms
2025-07-08 19:47:55,911 - INFO - 提取callId: 4867, callTime: 23.855469ms
2025-07-08 19:47:55,911 - INFO - 提取callId: 4963, callTime: 23.460938ms
2025-07-08 19:47:55,912 - INFO - 提取到 40 个event相关的callId
2025-07-08 19:47:55,912 - INFO - 处理 callId=4855, callTime=133.352344ms, rank=1
2025-07-08 19:47:55,912 - INFO - 执行详细分析: callId=4855
2025-07-08 19:47:55,924 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\1-4855.txt
2025-07-08 19:47:55,925 - INFO - 处理 callId=4839, callTime=101.153125ms, rank=2
2025-07-08 19:47:55,925 - INFO - 执行详细分析: callId=4839
2025-07-08 19:47:55,937 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\2-4839.txt
2025-07-08 19:47:55,938 - INFO - 处理 callId=5143, callTime=82.995312ms, rank=3
2025-07-08 19:47:55,938 - INFO - 执行详细分析: callId=5143
2025-07-08 19:47:55,951 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\3-5143.txt
2025-07-08 19:47:55,951 - INFO - 处理 callId=4859, callTime=63.297031ms, rank=4
2025-07-08 19:47:55,951 - INFO - 执行详细分析: callId=4859
2025-07-08 19:47:55,964 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\4-4859.txt
2025-07-08 19:47:55,964 - INFO - 处理 callId=5039, callTime=57.298125ms, rank=5
2025-07-08 19:47:55,964 - INFO - 执行详细分析: callId=5039
2025-07-08 19:47:55,978 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\5-5039.txt
2025-07-08 19:47:55,978 - INFO - 处理 callId=5115, callTime=56.533438ms, rank=6
2025-07-08 19:47:55,979 - INFO - 执行详细分析: callId=5115
2025-07-08 19:47:55,993 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\6-5115.txt
2025-07-08 19:47:55,993 - INFO - 处理 callId=3499, callTime=48.505938ms, rank=7
2025-07-08 19:47:55,993 - INFO - 执行详细分析: callId=3499
2025-07-08 19:47:56,020 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\7-3499.txt
2025-07-08 19:47:56,020 - INFO - 处理 callId=4883, callTime=46.550937ms, rank=8
2025-07-08 19:47:56,020 - INFO - 执行详细分析: callId=4883
2025-07-08 19:47:56,034 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\8-4883.txt
2025-07-08 19:47:56,034 - INFO - 处理 callId=5015, callTime=44.624843ms, rank=9
2025-07-08 19:47:56,034 - INFO - 执行详细分析: callId=5015
2025-07-08 19:47:56,047 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\9-5015.txt
2025-07-08 19:47:56,047 - INFO - 处理 callId=5203, callTime=41.652344ms, rank=10
2025-07-08 19:47:56,047 - INFO - 执行详细分析: callId=5203
2025-07-08 19:47:56,061 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\10-5203.txt
2025-07-08 19:47:56,061 - INFO - 处理 callId=4947, callTime=37.559844ms, rank=11
2025-07-08 19:47:56,061 - INFO - 执行详细分析: callId=4947
2025-07-08 19:47:56,074 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\11-4947.txt
2025-07-08 19:47:56,074 - INFO - 处理 callId=5131, callTime=36.786875ms, rank=12
2025-07-08 19:47:56,074 - INFO - 执行详细分析: callId=5131
2025-07-08 19:47:56,087 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\12-5131.txt
2025-07-08 19:47:56,087 - INFO - 处理 callId=4975, callTime=35.977187ms, rank=13
2025-07-08 19:47:56,087 - INFO - 执行详细分析: callId=4975
2025-07-08 19:47:56,101 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\13-4975.txt
2025-07-08 19:47:56,101 - INFO - 处理 callId=4991, callTime=33.997031ms, rank=14
2025-07-08 19:47:56,101 - INFO - 执行详细分析: callId=4991
2025-07-08 19:47:56,114 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\14-4991.txt
2025-07-08 19:47:56,114 - INFO - 处理 callId=4387, callTime=32.978125ms, rank=15
2025-07-08 19:47:56,115 - INFO - 执行详细分析: callId=4387
2025-07-08 19:47:56,127 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\15-4387.txt
2025-07-08 19:47:56,127 - INFO - 处理 callId=5043, callTime=30.971406ms, rank=16
2025-07-08 19:47:56,128 - INFO - 执行详细分析: callId=5043
2025-07-08 19:47:56,141 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\16-5043.txt
2025-07-08 19:47:56,141 - INFO - 处理 callId=4907, callTime=30.520625ms, rank=17
2025-07-08 19:47:56,141 - INFO - 执行详细分析: callId=4907
2025-07-08 19:47:56,154 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\17-4907.txt
2025-07-08 19:47:56,154 - INFO - 处理 callId=5231, callTime=30.433125ms, rank=18
2025-07-08 19:47:56,154 - INFO - 执行详细分析: callId=5231
2025-07-08 19:47:56,167 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\18-5231.txt
2025-07-08 19:47:56,167 - INFO - 处理 callId=5191, callTime=29.531406ms, rank=19
2025-07-08 19:47:56,167 - INFO - 执行详细分析: callId=5191
2025-07-08 19:47:56,180 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\19-5191.txt
2025-07-08 19:47:56,180 - INFO - 处理 callId=4979, callTime=29.095938ms, rank=20
2025-07-08 19:47:56,180 - INFO - 执行详细分析: callId=4979
2025-07-08 19:47:56,192 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\20-4979.txt
2025-07-08 19:47:56,193 - INFO - 处理 callId=5003, callTime=27.950312ms, rank=21
2025-07-08 19:47:56,193 - INFO - 执行详细分析: callId=5003
2025-07-08 19:47:56,205 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\21-5003.txt
2025-07-08 19:47:56,205 - INFO - 处理 callId=4875, callTime=27.721562ms, rank=22
2025-07-08 19:47:56,205 - INFO - 执行详细分析: callId=4875
2025-07-08 19:47:56,218 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\22-4875.txt
2025-07-08 19:47:56,218 - INFO - 处理 callId=4923, callTime=27.569063ms, rank=23
2025-07-08 19:47:56,218 - INFO - 执行详细分析: callId=4923
2025-07-08 19:47:56,230 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\23-4923.txt
2025-07-08 19:47:56,230 - INFO - 处理 callId=4795, callTime=27.19ms, rank=24
2025-07-08 19:47:56,230 - INFO - 执行详细分析: callId=4795
2025-07-08 19:47:56,243 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\24-4795.txt
2025-07-08 19:47:56,243 - INFO - 处理 callId=4807, callTime=26.946093ms, rank=25
2025-07-08 19:47:56,243 - INFO - 执行详细分析: callId=4807
2025-07-08 19:47:56,256 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\25-4807.txt
2025-07-08 19:47:56,256 - INFO - 处理 callId=4951, callTime=26.811406ms, rank=26
2025-07-08 19:47:56,256 - INFO - 执行详细分析: callId=4951
2025-07-08 19:47:56,269 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\26-4951.txt
2025-07-08 19:47:56,269 - INFO - 处理 callId=14, callTime=26.180157ms, rank=27
2025-07-08 19:47:56,269 - INFO - 执行详细分析: callId=14
2025-07-08 19:47:56,284 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\27-14.txt
2025-07-08 19:47:56,284 - INFO - 处理 callId=5179, callTime=25.884375ms, rank=28
2025-07-08 19:47:56,284 - INFO - 执行详细分析: callId=5179
2025-07-08 19:47:56,297 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\28-5179.txt
2025-07-08 19:47:56,297 - INFO - 处理 callId=5067, callTime=25.572031ms, rank=29
2025-07-08 19:47:56,297 - INFO - 执行详细分析: callId=5067
2025-07-08 19:47:56,309 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\29-5067.txt
2025-07-08 19:47:56,310 - INFO - 处理 callId=4831, callTime=25.322187ms, rank=30
2025-07-08 19:47:56,310 - INFO - 执行详细分析: callId=4831
2025-07-08 19:47:56,322 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\30-4831.txt
2025-07-08 19:47:56,322 - INFO - 处理 callId=5263, callTime=25.140312ms, rank=31
2025-07-08 19:47:56,322 - INFO - 执行详细分析: callId=5263
2025-07-08 19:47:56,335 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\31-5263.txt
2025-07-08 19:47:56,335 - INFO - 处理 callId=4899, callTime=24.639687ms, rank=32
2025-07-08 19:47:56,335 - INFO - 执行详细分析: callId=4899
2025-07-08 19:47:56,347 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\32-4899.txt
2025-07-08 19:47:56,348 - INFO - 处理 callId=5207, callTime=24.625625ms, rank=33
2025-07-08 19:47:56,348 - INFO - 执行详细分析: callId=5207
2025-07-08 19:47:56,360 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\33-5207.txt
2025-07-08 19:47:56,361 - INFO - 处理 callId=5155, callTime=24.416094ms, rank=34
2025-07-08 19:47:56,361 - INFO - 执行详细分析: callId=5155
2025-07-08 19:47:56,373 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\34-5155.txt
2025-07-08 19:47:56,373 - INFO - 处理 callId=5119, callTime=24.314218ms, rank=35
2025-07-08 19:47:56,373 - INFO - 执行详细分析: callId=5119
2025-07-08 19:47:56,386 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\35-5119.txt
2025-07-08 19:47:56,386 - INFO - 处理 callId=4891, callTime=24.141875ms, rank=36
2025-07-08 19:47:56,386 - INFO - 执行详细分析: callId=4891
2025-07-08 19:47:56,399 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\36-4891.txt
2025-07-08 19:47:56,399 - INFO - 处理 callId=5055, callTime=23.947969ms, rank=37
2025-07-08 19:47:56,399 - INFO - 执行详细分析: callId=5055
2025-07-08 19:47:56,411 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\37-5055.txt
2025-07-08 19:47:56,411 - INFO - 处理 callId=4555, callTime=23.9125ms, rank=38
2025-07-08 19:47:56,412 - INFO - 执行详细分析: callId=4555
2025-07-08 19:47:56,424 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\38-4555.txt
2025-07-08 19:47:56,424 - INFO - 处理 callId=4867, callTime=23.855469ms, rank=39
2025-07-08 19:47:56,424 - INFO - 执行详细分析: callId=4867
2025-07-08 19:47:56,440 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\39-4867.txt
2025-07-08 19:47:56,440 - INFO - 处理 callId=4963, callTime=23.460938ms, rank=40
2025-07-08 19:47:56,440 - INFO - 执行详细分析: callId=4963
2025-07-08 19:47:56,453 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_101933_wpp切换页面\40-4963.txt
2025-07-08 19:47:56,453 - INFO - 完成处理文件: perfmon-raster-20250707_101933_wpp切换页面.dat
2025-07-08 19:47:56,453 - INFO - 处理进度: 18/30
2025-07-08 19:47:56,453 - INFO - 开始处理文件: perfmon-raster-20250707_102233_wpp缩略图生成.dat
2025-07-08 19:47:56,454 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-raster-20250707_102233_wpp缩略图生成.dat
2025-07-08 19:47:56,468 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-raster-20250707_102233_wpp缩略图生成.dat.txt
2025-07-08 19:47:56,468 - INFO - 找到event函数: E:\wpsskia\qt\source\qtbase\src\widgets\kernel\qwidget.cpp:9682 event
2025-07-08 19:47:56,468 - INFO - 提取callId: 4331, callTime: 109.914531ms
2025-07-08 19:47:56,469 - INFO - 提取callId: 4403, callTime: 106.134687ms
2025-07-08 19:47:56,469 - INFO - 提取callId: 4323, callTime: 59.8675ms
2025-07-08 19:47:56,469 - INFO - 提取callId: 4359, callTime: 54.534688ms
2025-07-08 19:47:56,469 - INFO - 提取callId: 3031, callTime: 42.176094ms
2025-07-08 19:47:56,469 - INFO - 提取callId: 3919, callTime: 33.873906ms
2025-07-08 19:47:56,469 - INFO - 提取callId: 14, callTime: 25.320156ms
2025-07-08 19:47:56,469 - INFO - 提取callId: 4347, callTime: 25.129531ms
2025-07-08 19:47:56,469 - INFO - 提取callId: 4295, callTime: 23.517188ms
2025-07-08 19:47:56,469 - INFO - 提取callId: 4335, callTime: 21.00875ms
2025-07-08 19:47:56,469 - INFO - 提取callId: 4307, callTime: 16.151562ms
2025-07-08 19:47:56,470 - INFO - 提取callId: 450, callTime: 14.874844ms
2025-07-08 19:47:56,470 - INFO - 提取callId: 4311, callTime: 14.632188ms
2025-07-08 19:47:56,470 - INFO - 提取callId: 4299, callTime: 14.3825ms
2025-07-08 19:47:56,470 - INFO - 提取callId: 4099, callTime: 14.264062ms
2025-07-08 19:47:56,470 - INFO - 提取callId: 4075, callTime: 14.024219ms
2025-07-08 19:47:56,470 - INFO - 提取callId: 886, callTime: 13.498438ms
2025-07-08 19:47:56,470 - INFO - 提取callId: 1303, callTime: 13.408125ms
2025-07-08 19:47:56,470 - INFO - 提取callId: 4303, callTime: 13.245ms
2025-07-08 19:47:56,470 - INFO - 提取callId: 3999, callTime: 13.151407ms
2025-07-08 19:47:56,471 - INFO - 提取callId: 4211, callTime: 13.032031ms
2025-07-08 19:47:56,471 - INFO - 提取callId: 4071, callTime: 12.231407ms
2025-07-08 19:47:56,471 - INFO - 提取callId: 4319, callTime: 12.219844ms
2025-07-08 19:47:56,471 - INFO - 提取callId: 3927, callTime: 11.366875ms
2025-07-08 19:47:56,471 - INFO - 提取callId: 1739, callTime: 10.461718ms
2025-07-08 19:47:56,471 - INFO - 提取callId: 4011, callTime: 9.843281ms
2025-07-08 19:47:56,471 - INFO - 提取callId: 1749, callTime: 9.415781ms
2025-07-08 19:47:56,471 - INFO - 提取callId: 2614, callTime: 9.309375ms
2025-07-08 19:47:56,472 - INFO - 提取callId: 2166, callTime: 9.013906ms
2025-07-08 19:47:56,472 - INFO - 提取callId: 4047, callTime: 8.821406ms
2025-07-08 19:47:56,472 - INFO - 提取callId: 4343, callTime: 8.810157ms
2025-07-08 19:47:56,472 - INFO - 提取callId: 4327, callTime: 8.780157ms
2025-07-08 19:47:56,472 - INFO - 提取callId: 4083, callTime: 8.661719ms
2025-07-08 19:47:56,472 - INFO - 提取callId: 4055, callTime: 8.635312ms
2025-07-08 19:47:56,472 - INFO - 提取callId: 3995, callTime: 8.527812ms
2025-07-08 19:47:56,473 - INFO - 提取callId: 4135, callTime: 8.371093ms
2025-07-08 19:47:56,473 - INFO - 提取callId: 3983, callTime: 8.335625ms
2025-07-08 19:47:56,473 - INFO - 提取callId: 4315, callTime: 8.333594ms
2025-07-08 19:47:56,473 - INFO - 提取callId: 3979, callTime: 8.302188ms
2025-07-08 19:47:56,473 - INFO - 提取callId: 3931, callTime: 8.289844ms
2025-07-08 19:47:56,473 - INFO - 提取到 40 个event相关的callId
2025-07-08 19:47:56,473 - INFO - 处理 callId=4331, callTime=109.914531ms, rank=1
2025-07-08 19:47:56,473 - INFO - 执行详细分析: callId=4331
2025-07-08 19:47:56,486 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\1-4331.txt
2025-07-08 19:47:56,486 - INFO - 处理 callId=4403, callTime=106.134687ms, rank=2
2025-07-08 19:47:56,486 - INFO - 执行详细分析: callId=4403
2025-07-08 19:47:56,499 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\2-4403.txt
2025-07-08 19:47:56,499 - INFO - 处理 callId=4323, callTime=59.8675ms, rank=3
2025-07-08 19:47:56,499 - INFO - 执行详细分析: callId=4323
2025-07-08 19:47:56,512 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\3-4323.txt
2025-07-08 19:47:56,512 - INFO - 处理 callId=4359, callTime=54.534688ms, rank=4
2025-07-08 19:47:56,512 - INFO - 执行详细分析: callId=4359
2025-07-08 19:47:56,524 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\4-4359.txt
2025-07-08 19:47:56,524 - INFO - 处理 callId=3031, callTime=42.176094ms, rank=5
2025-07-08 19:47:56,524 - INFO - 执行详细分析: callId=3031
2025-07-08 19:47:56,552 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\5-3031.txt
2025-07-08 19:47:56,552 - INFO - 处理 callId=3919, callTime=33.873906ms, rank=6
2025-07-08 19:47:56,552 - INFO - 执行详细分析: callId=3919
2025-07-08 19:47:56,563 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\6-3919.txt
2025-07-08 19:47:56,564 - INFO - 处理 callId=14, callTime=25.320156ms, rank=7
2025-07-08 19:47:56,564 - INFO - 执行详细分析: callId=14
2025-07-08 19:47:56,580 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\7-14.txt
2025-07-08 19:47:56,580 - INFO - 处理 callId=4347, callTime=25.129531ms, rank=8
2025-07-08 19:47:56,580 - INFO - 执行详细分析: callId=4347
2025-07-08 19:47:56,592 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\8-4347.txt
2025-07-08 19:47:56,592 - INFO - 处理 callId=4295, callTime=23.517188ms, rank=9
2025-07-08 19:47:56,593 - INFO - 执行详细分析: callId=4295
2025-07-08 19:47:56,605 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\9-4295.txt
2025-07-08 19:47:56,605 - INFO - 处理 callId=4335, callTime=21.00875ms, rank=10
2025-07-08 19:47:56,605 - INFO - 执行详细分析: callId=4335
2025-07-08 19:47:56,618 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\10-4335.txt
2025-07-08 19:47:56,618 - INFO - 处理 callId=4307, callTime=16.151562ms, rank=11
2025-07-08 19:47:56,618 - INFO - 执行详细分析: callId=4307
2025-07-08 19:47:56,630 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\11-4307.txt
2025-07-08 19:47:56,631 - INFO - 处理 callId=450, callTime=14.874844ms, rank=12
2025-07-08 19:47:56,631 - INFO - 执行详细分析: callId=450
2025-07-08 19:47:56,647 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\12-450.txt
2025-07-08 19:47:56,647 - INFO - 处理 callId=4311, callTime=14.632188ms, rank=13
2025-07-08 19:47:56,647 - INFO - 执行详细分析: callId=4311
2025-07-08 19:47:56,660 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\13-4311.txt
2025-07-08 19:47:56,660 - INFO - 处理 callId=4299, callTime=14.3825ms, rank=14
2025-07-08 19:47:56,660 - INFO - 执行详细分析: callId=4299
2025-07-08 19:47:56,672 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\14-4299.txt
2025-07-08 19:47:56,672 - INFO - 处理 callId=4099, callTime=14.264062ms, rank=15
2025-07-08 19:47:56,673 - INFO - 执行详细分析: callId=4099
2025-07-08 19:47:56,684 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\15-4099.txt
2025-07-08 19:47:56,685 - INFO - 处理 callId=4075, callTime=14.024219ms, rank=16
2025-07-08 19:47:56,685 - INFO - 执行详细分析: callId=4075
2025-07-08 19:47:56,696 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\16-4075.txt
2025-07-08 19:47:56,696 - INFO - 处理 callId=886, callTime=13.498438ms, rank=17
2025-07-08 19:47:56,697 - INFO - 执行详细分析: callId=886
2025-07-08 19:47:56,712 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\17-886.txt
2025-07-08 19:47:56,712 - INFO - 处理 callId=1303, callTime=13.408125ms, rank=18
2025-07-08 19:47:56,713 - INFO - 执行详细分析: callId=1303
2025-07-08 19:47:56,729 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\18-1303.txt
2025-07-08 19:47:56,729 - INFO - 处理 callId=4303, callTime=13.245ms, rank=19
2025-07-08 19:47:56,729 - INFO - 执行详细分析: callId=4303
2025-07-08 19:47:56,742 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\19-4303.txt
2025-07-08 19:47:56,742 - INFO - 处理 callId=3999, callTime=13.151407ms, rank=20
2025-07-08 19:47:56,742 - INFO - 执行详细分析: callId=3999
2025-07-08 19:47:56,755 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\20-3999.txt
2025-07-08 19:47:56,755 - INFO - 处理 callId=4211, callTime=13.032031ms, rank=21
2025-07-08 19:47:56,755 - INFO - 执行详细分析: callId=4211
2025-07-08 19:47:56,767 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\21-4211.txt
2025-07-08 19:47:56,768 - INFO - 处理 callId=4071, callTime=12.231407ms, rank=22
2025-07-08 19:47:56,768 - INFO - 执行详细分析: callId=4071
2025-07-08 19:47:56,780 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\22-4071.txt
2025-07-08 19:47:56,780 - INFO - 处理 callId=4319, callTime=12.219844ms, rank=23
2025-07-08 19:47:56,780 - INFO - 执行详细分析: callId=4319
2025-07-08 19:47:56,793 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\23-4319.txt
2025-07-08 19:47:56,793 - INFO - 处理 callId=3927, callTime=11.366875ms, rank=24
2025-07-08 19:47:56,793 - INFO - 执行详细分析: callId=3927
2025-07-08 19:47:56,805 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\24-3927.txt
2025-07-08 19:47:56,805 - INFO - 处理 callId=1739, callTime=10.461718ms, rank=25
2025-07-08 19:47:56,805 - INFO - 执行详细分析: callId=1739
2025-07-08 19:47:56,815 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\25-1739.txt
2025-07-08 19:47:56,815 - INFO - 处理 callId=4011, callTime=9.843281ms, rank=26
2025-07-08 19:47:56,815 - INFO - 执行详细分析: callId=4011
2025-07-08 19:47:56,827 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\26-4011.txt
2025-07-08 19:47:56,827 - INFO - 处理 callId=1749, callTime=9.415781ms, rank=27
2025-07-08 19:47:56,827 - INFO - 执行详细分析: callId=1749
2025-07-08 19:47:56,844 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\27-1749.txt
2025-07-08 19:47:56,844 - INFO - 处理 callId=2614, callTime=9.309375ms, rank=28
2025-07-08 19:47:56,844 - INFO - 执行详细分析: callId=2614
2025-07-08 19:47:56,862 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\28-2614.txt
2025-07-08 19:47:56,862 - INFO - 处理 callId=2166, callTime=9.013906ms, rank=29
2025-07-08 19:47:56,862 - INFO - 执行详细分析: callId=2166
2025-07-08 19:47:56,879 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\29-2166.txt
2025-07-08 19:47:56,879 - INFO - 处理 callId=4047, callTime=8.821406ms, rank=30
2025-07-08 19:47:56,879 - INFO - 执行详细分析: callId=4047
2025-07-08 19:47:56,891 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\30-4047.txt
2025-07-08 19:47:56,891 - INFO - 处理 callId=4343, callTime=8.810157ms, rank=31
2025-07-08 19:47:56,891 - INFO - 执行详细分析: callId=4343
2025-07-08 19:47:56,903 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\31-4343.txt
2025-07-08 19:47:56,903 - INFO - 处理 callId=4327, callTime=8.780157ms, rank=32
2025-07-08 19:47:56,903 - INFO - 执行详细分析: callId=4327
2025-07-08 19:47:56,915 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\32-4327.txt
2025-07-08 19:47:56,915 - INFO - 处理 callId=4083, callTime=8.661719ms, rank=33
2025-07-08 19:47:56,915 - INFO - 执行详细分析: callId=4083
2025-07-08 19:47:56,926 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\33-4083.txt
2025-07-08 19:47:56,927 - INFO - 处理 callId=4055, callTime=8.635312ms, rank=34
2025-07-08 19:47:56,927 - INFO - 执行详细分析: callId=4055
2025-07-08 19:47:56,938 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\34-4055.txt
2025-07-08 19:47:56,938 - INFO - 处理 callId=3995, callTime=8.527812ms, rank=35
2025-07-08 19:47:56,938 - INFO - 执行详细分析: callId=3995
2025-07-08 19:47:56,950 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\35-3995.txt
2025-07-08 19:47:56,951 - INFO - 处理 callId=4135, callTime=8.371093ms, rank=36
2025-07-08 19:47:56,951 - INFO - 执行详细分析: callId=4135
2025-07-08 19:47:56,962 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\36-4135.txt
2025-07-08 19:47:56,963 - INFO - 处理 callId=3983, callTime=8.335625ms, rank=37
2025-07-08 19:47:56,963 - INFO - 执行详细分析: callId=3983
2025-07-08 19:47:56,974 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\37-3983.txt
2025-07-08 19:47:56,974 - INFO - 处理 callId=4315, callTime=8.333594ms, rank=38
2025-07-08 19:47:56,974 - INFO - 执行详细分析: callId=4315
2025-07-08 19:47:56,987 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\38-4315.txt
2025-07-08 19:47:56,987 - INFO - 处理 callId=3979, callTime=8.302188ms, rank=39
2025-07-08 19:47:56,987 - INFO - 执行详细分析: callId=3979
2025-07-08 19:47:57,000 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\39-3979.txt
2025-07-08 19:47:57,000 - INFO - 处理 callId=3931, callTime=8.289844ms, rank=40
2025-07-08 19:47:57,000 - INFO - 执行详细分析: callId=3931
2025-07-08 19:47:57,012 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102233_wpp缩略图生成\40-3931.txt
2025-07-08 19:47:57,012 - INFO - 完成处理文件: perfmon-raster-20250707_102233_wpp缩略图生成.dat
2025-07-08 19:47:57,013 - INFO - 处理进度: 19/30
2025-07-08 19:47:57,013 - INFO - 开始处理文件: perfmon-raster-20250707_102721_wpp动画播放对象动画.dat
2025-07-08 19:47:57,013 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-raster-20250707_102721_wpp动画播放对象动画.dat
2025-07-08 19:47:57,031 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-raster-20250707_102721_wpp动画播放对象动画.dat.txt
2025-07-08 19:47:57,032 - INFO - 找到event函数: E:\wpsskia\qt\source\qtbase\src\widgets\kernel\qwidget.cpp:9682 event
2025-07-08 19:47:57,032 - INFO - 提取callId: 4763, callTime: 41.253437ms
2025-07-08 19:47:57,032 - INFO - 提取callId: 5648, callTime: 29.805469ms
2025-07-08 19:47:57,032 - INFO - 提取callId: 14, callTime: 28.803594ms
2025-07-08 19:47:57,032 - INFO - 提取callId: 6156, callTime: 27.743125ms
2025-07-08 19:47:57,032 - INFO - 提取callId: 450, callTime: 15.6875ms
2025-07-08 19:47:57,032 - INFO - 提取callId: 6048, callTime: 15.204219ms
2025-07-08 19:47:57,032 - INFO - 提取callId: 6116, callTime: 14.77ms
2025-07-08 19:47:57,032 - INFO - 提取callId: 6439, callTime: 14.159844ms
2025-07-08 19:47:57,032 - INFO - 提取callId: 5652, callTime: 13.647813ms
2025-07-08 19:47:57,033 - INFO - 提取callId: 5912, callTime: 13.447657ms
2025-07-08 19:47:57,033 - INFO - 提取callId: 886, callTime: 13.430937ms
2025-07-08 19:47:57,033 - INFO - 提取callId: 6459, callTime: 13.21ms
2025-07-08 19:47:57,033 - INFO - 提取callId: 6419, callTime: 13.132344ms
2025-07-08 19:47:57,033 - INFO - 提取callId: 6471, callTime: 13.070312ms
2025-07-08 19:47:57,033 - INFO - 提取callId: 6443, callTime: 12.859531ms
2025-07-08 19:47:57,033 - INFO - 提取callId: 6239, callTime: 12.496875ms
2025-07-08 19:47:57,033 - INFO - 提取callId: 6415, callTime: 12.180937ms
2025-07-08 19:47:57,033 - INFO - 提取callId: 6172, callTime: 12.176406ms
2025-07-08 19:47:57,033 - INFO - 提取callId: 1303, callTime: 12.134532ms
2025-07-08 19:47:57,033 - INFO - 提取callId: 6407, callTime: 12.053906ms
2025-07-08 19:47:57,033 - INFO - 提取callId: 6447, callTime: 11.889687ms
2025-07-08 19:47:57,034 - INFO - 提取callId: 6251, callTime: 11.774844ms
2025-07-08 19:47:57,034 - INFO - 提取callId: 5908, callTime: 11.755625ms
2025-07-08 19:47:57,034 - INFO - 提取callId: 6211, callTime: 11.655ms
2025-07-08 19:47:57,034 - INFO - 提取callId: 6355, callTime: 11.611719ms
2025-07-08 19:47:57,034 - INFO - 提取callId: 6367, callTime: 11.515312ms
2025-07-08 19:47:57,034 - INFO - 提取callId: 6463, callTime: 11.501094ms
2025-07-08 19:47:57,034 - INFO - 提取callId: 6379, callTime: 11.454688ms
2025-07-08 19:47:57,034 - INFO - 提取callId: 6435, callTime: 11.445313ms
2025-07-08 19:47:57,034 - INFO - 提取callId: 6283, callTime: 11.434375ms
2025-07-08 19:47:57,034 - INFO - 提取callId: 6395, callTime: 11.339531ms
2025-07-08 19:47:57,034 - INFO - 提取callId: 6279, callTime: 11.281719ms
2025-07-08 19:47:57,034 - INFO - 提取callId: 6331, callTime: 11.261718ms
2025-07-08 19:47:57,035 - INFO - 提取callId: 6455, callTime: 11.144375ms
2025-07-08 19:47:57,035 - INFO - 提取callId: 6483, callTime: 11.141719ms
2025-07-08 19:47:57,035 - INFO - 提取callId: 6319, callTime: 11.135156ms
2025-07-08 19:47:57,035 - INFO - 提取callId: 6227, callTime: 11.130156ms
2025-07-08 19:47:57,035 - INFO - 提取callId: 1739, callTime: 11.077343ms
2025-07-08 19:47:57,035 - INFO - 提取callId: 6487, callTime: 10.981407ms
2025-07-08 19:47:57,035 - INFO - 提取callId: 6343, callTime: 10.960468ms
2025-07-08 19:47:57,035 - INFO - 提取到 40 个event相关的callId
2025-07-08 19:47:57,035 - INFO - 处理 callId=4763, callTime=41.253437ms, rank=1
2025-07-08 19:47:57,035 - INFO - 执行详细分析: callId=4763
2025-07-08 19:47:57,064 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\1-4763.txt
2025-07-08 19:47:57,065 - INFO - 处理 callId=5648, callTime=29.805469ms, rank=2
2025-07-08 19:47:57,065 - INFO - 执行详细分析: callId=5648
2025-07-08 19:47:57,078 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\2-5648.txt
2025-07-08 19:47:57,079 - INFO - 处理 callId=14, callTime=28.803594ms, rank=3
2025-07-08 19:47:57,079 - INFO - 执行详细分析: callId=14
2025-07-08 19:47:57,095 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\3-14.txt
2025-07-08 19:47:57,095 - INFO - 处理 callId=6156, callTime=27.743125ms, rank=4
2025-07-08 19:47:57,095 - INFO - 执行详细分析: callId=6156
2025-07-08 19:47:57,110 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\4-6156.txt
2025-07-08 19:47:57,111 - INFO - 处理 callId=450, callTime=15.6875ms, rank=5
2025-07-08 19:47:57,111 - INFO - 执行详细分析: callId=450
2025-07-08 19:47:57,127 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\5-450.txt
2025-07-08 19:47:57,127 - INFO - 处理 callId=6048, callTime=15.204219ms, rank=6
2025-07-08 19:47:57,127 - INFO - 执行详细分析: callId=6048
2025-07-08 19:47:57,142 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\6-6048.txt
2025-07-08 19:47:57,142 - INFO - 处理 callId=6116, callTime=14.77ms, rank=7
2025-07-08 19:47:57,142 - INFO - 执行详细分析: callId=6116
2025-07-08 19:47:57,156 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\7-6116.txt
2025-07-08 19:47:57,156 - INFO - 处理 callId=6439, callTime=14.159844ms, rank=8
2025-07-08 19:47:57,156 - INFO - 执行详细分析: callId=6439
2025-07-08 19:47:57,170 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\8-6439.txt
2025-07-08 19:47:57,170 - INFO - 处理 callId=5652, callTime=13.647813ms, rank=9
2025-07-08 19:47:57,170 - INFO - 执行详细分析: callId=5652
2025-07-08 19:47:57,183 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\9-5652.txt
2025-07-08 19:47:57,183 - INFO - 处理 callId=5912, callTime=13.447657ms, rank=10
2025-07-08 19:47:57,183 - INFO - 执行详细分析: callId=5912
2025-07-08 19:47:57,197 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\10-5912.txt
2025-07-08 19:47:57,198 - INFO - 处理 callId=886, callTime=13.430937ms, rank=11
2025-07-08 19:47:57,198 - INFO - 执行详细分析: callId=886
2025-07-08 19:47:57,213 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\11-886.txt
2025-07-08 19:47:57,214 - INFO - 处理 callId=6459, callTime=13.21ms, rank=12
2025-07-08 19:47:57,214 - INFO - 执行详细分析: callId=6459
2025-07-08 19:47:57,228 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\12-6459.txt
2025-07-08 19:47:57,228 - INFO - 处理 callId=6419, callTime=13.132344ms, rank=13
2025-07-08 19:47:57,228 - INFO - 执行详细分析: callId=6419
2025-07-08 19:47:57,242 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\13-6419.txt
2025-07-08 19:47:57,242 - INFO - 处理 callId=6471, callTime=13.070312ms, rank=14
2025-07-08 19:47:57,242 - INFO - 执行详细分析: callId=6471
2025-07-08 19:47:57,256 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\14-6471.txt
2025-07-08 19:47:57,256 - INFO - 处理 callId=6443, callTime=12.859531ms, rank=15
2025-07-08 19:47:57,257 - INFO - 执行详细分析: callId=6443
2025-07-08 19:47:57,270 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\15-6443.txt
2025-07-08 19:47:57,270 - INFO - 处理 callId=6239, callTime=12.496875ms, rank=16
2025-07-08 19:47:57,270 - INFO - 执行详细分析: callId=6239
2025-07-08 19:47:57,284 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\16-6239.txt
2025-07-08 19:47:57,284 - INFO - 处理 callId=6415, callTime=12.180937ms, rank=17
2025-07-08 19:47:57,284 - INFO - 执行详细分析: callId=6415
2025-07-08 19:47:57,297 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\17-6415.txt
2025-07-08 19:47:57,297 - INFO - 处理 callId=6172, callTime=12.176406ms, rank=18
2025-07-08 19:47:57,297 - INFO - 执行详细分析: callId=6172
2025-07-08 19:47:57,310 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\18-6172.txt
2025-07-08 19:47:57,311 - INFO - 处理 callId=1303, callTime=12.134532ms, rank=19
2025-07-08 19:47:57,311 - INFO - 执行详细分析: callId=1303
2025-07-08 19:47:57,327 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\19-1303.txt
2025-07-08 19:47:57,327 - INFO - 处理 callId=6407, callTime=12.053906ms, rank=20
2025-07-08 19:47:57,327 - INFO - 执行详细分析: callId=6407
2025-07-08 19:47:57,341 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\20-6407.txt
2025-07-08 19:47:57,341 - INFO - 处理 callId=6447, callTime=11.889687ms, rank=21
2025-07-08 19:47:57,341 - INFO - 执行详细分析: callId=6447
2025-07-08 19:47:57,354 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\21-6447.txt
2025-07-08 19:47:57,355 - INFO - 处理 callId=6251, callTime=11.774844ms, rank=22
2025-07-08 19:47:57,355 - INFO - 执行详细分析: callId=6251
2025-07-08 19:47:57,368 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\22-6251.txt
2025-07-08 19:47:57,369 - INFO - 处理 callId=5908, callTime=11.755625ms, rank=23
2025-07-08 19:47:57,369 - INFO - 执行详细分析: callId=5908
2025-07-08 19:47:57,382 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\23-5908.txt
2025-07-08 19:47:57,382 - INFO - 处理 callId=6211, callTime=11.655ms, rank=24
2025-07-08 19:47:57,382 - INFO - 执行详细分析: callId=6211
2025-07-08 19:47:57,395 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\24-6211.txt
2025-07-08 19:47:57,395 - INFO - 处理 callId=6355, callTime=11.611719ms, rank=25
2025-07-08 19:47:57,396 - INFO - 执行详细分析: callId=6355
2025-07-08 19:47:57,409 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\25-6355.txt
2025-07-08 19:47:57,409 - INFO - 处理 callId=6367, callTime=11.515312ms, rank=26
2025-07-08 19:47:57,409 - INFO - 执行详细分析: callId=6367
2025-07-08 19:47:57,423 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\26-6367.txt
2025-07-08 19:47:57,423 - INFO - 处理 callId=6463, callTime=11.501094ms, rank=27
2025-07-08 19:47:57,423 - INFO - 执行详细分析: callId=6463
2025-07-08 19:47:57,439 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\27-6463.txt
2025-07-08 19:47:57,439 - INFO - 处理 callId=6379, callTime=11.454688ms, rank=28
2025-07-08 19:47:57,439 - INFO - 执行详细分析: callId=6379
2025-07-08 19:47:57,458 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\28-6379.txt
2025-07-08 19:47:57,458 - INFO - 处理 callId=6435, callTime=11.445313ms, rank=29
2025-07-08 19:47:57,458 - INFO - 执行详细分析: callId=6435
2025-07-08 19:47:57,473 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\29-6435.txt
2025-07-08 19:47:57,473 - INFO - 处理 callId=6283, callTime=11.434375ms, rank=30
2025-07-08 19:47:57,473 - INFO - 执行详细分析: callId=6283
2025-07-08 19:47:57,487 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\30-6283.txt
2025-07-08 19:47:57,487 - INFO - 处理 callId=6395, callTime=11.339531ms, rank=31
2025-07-08 19:47:57,488 - INFO - 执行详细分析: callId=6395
2025-07-08 19:47:57,502 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\31-6395.txt
2025-07-08 19:47:57,502 - INFO - 处理 callId=6279, callTime=11.281719ms, rank=32
2025-07-08 19:47:57,502 - INFO - 执行详细分析: callId=6279
2025-07-08 19:47:57,517 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\32-6279.txt
2025-07-08 19:47:57,517 - INFO - 处理 callId=6331, callTime=11.261718ms, rank=33
2025-07-08 19:47:57,517 - INFO - 执行详细分析: callId=6331
2025-07-08 19:47:57,532 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\33-6331.txt
2025-07-08 19:47:57,532 - INFO - 处理 callId=6455, callTime=11.144375ms, rank=34
2025-07-08 19:47:57,532 - INFO - 执行详细分析: callId=6455
2025-07-08 19:47:57,548 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\34-6455.txt
2025-07-08 19:47:57,548 - INFO - 处理 callId=6483, callTime=11.141719ms, rank=35
2025-07-08 19:47:57,548 - INFO - 执行详细分析: callId=6483
2025-07-08 19:47:57,563 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\35-6483.txt
2025-07-08 19:47:57,563 - INFO - 处理 callId=6319, callTime=11.135156ms, rank=36
2025-07-08 19:47:57,564 - INFO - 执行详细分析: callId=6319
2025-07-08 19:47:57,578 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\36-6319.txt
2025-07-08 19:47:57,578 - INFO - 处理 callId=6227, callTime=11.130156ms, rank=37
2025-07-08 19:47:57,579 - INFO - 执行详细分析: callId=6227
2025-07-08 19:47:57,593 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\37-6227.txt
2025-07-08 19:47:57,593 - INFO - 处理 callId=1739, callTime=11.077343ms, rank=38
2025-07-08 19:47:57,593 - INFO - 执行详细分析: callId=1739
2025-07-08 19:47:57,604 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\38-1739.txt
2025-07-08 19:47:57,604 - INFO - 处理 callId=6487, callTime=10.981407ms, rank=39
2025-07-08 19:47:57,604 - INFO - 执行详细分析: callId=6487
2025-07-08 19:47:57,618 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\39-6487.txt
2025-07-08 19:47:57,618 - INFO - 处理 callId=6343, callTime=10.960468ms, rank=40
2025-07-08 19:47:57,618 - INFO - 执行详细分析: callId=6343
2025-07-08 19:47:57,632 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_102721_wpp动画播放对象动画\40-6343.txt
2025-07-08 19:47:57,632 - INFO - 完成处理文件: perfmon-raster-20250707_102721_wpp动画播放对象动画.dat
2025-07-08 19:47:57,632 - INFO - 处理进度: 20/30
2025-07-08 19:47:57,632 - INFO - 开始处理文件: perfmon-raster-20250707_103507_wpp动画页切换.dat
2025-07-08 19:47:57,633 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-raster-20250707_103507_wpp动画页切换.dat
2025-07-08 19:47:57,654 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-raster-20250707_103507_wpp动画页切换.dat.txt
2025-07-08 19:47:57,654 - INFO - 找到event函数: E:\wpsskia\qt\source\qtbase\src\widgets\kernel\qwidget.cpp:9682 event
2025-07-08 19:47:57,654 - INFO - 提取callId: 5228, callTime: 67.783593ms
2025-07-08 19:47:57,654 - INFO - 提取callId: 5244, callTime: 57.741094ms
2025-07-08 19:47:57,654 - INFO - 提取callId: 4333, callTime: 51.903594ms
2025-07-08 19:47:57,654 - INFO - 提取callId: 5261, callTime: 43.113281ms
2025-07-08 19:47:57,654 - INFO - 提取callId: 5599, callTime: 35.887344ms
2025-07-08 19:47:57,655 - INFO - 提取callId: 14, callTime: 25.719219ms
2025-07-08 19:47:57,655 - INFO - 提取callId: 5363, callTime: 17.457031ms
2025-07-08 19:47:57,655 - INFO - 提取callId: 5315, callTime: 16.937969ms
2025-07-08 19:47:57,655 - INFO - 提取callId: 5431, callTime: 16.284687ms
2025-07-08 19:47:57,655 - INFO - 提取callId: 6159, callTime: 15.764844ms
2025-07-08 19:47:57,655 - INFO - 提取callId: 450, callTime: 15.045625ms
2025-07-08 19:47:57,655 - INFO - 提取callId: 6135, callTime: 15.010157ms
2025-07-08 19:47:57,655 - INFO - 提取callId: 5729, callTime: 14.922187ms
2025-07-08 19:47:57,655 - INFO - 提取callId: 6191, callTime: 14.884063ms
2025-07-08 19:47:57,655 - INFO - 提取callId: 886, callTime: 14.87875ms
2025-07-08 19:47:57,655 - INFO - 提取callId: 6007, callTime: 14.135468ms
2025-07-08 19:47:57,656 - INFO - 提取callId: 5879, callTime: 13.404375ms
2025-07-08 19:47:57,656 - INFO - 提取callId: 6211, callTime: 13.044219ms
2025-07-08 19:47:57,656 - INFO - 提取callId: 6147, callTime: 13.030625ms
2025-07-08 19:47:57,656 - INFO - 提取callId: 5883, callTime: 12.9ms
2025-07-08 19:47:57,656 - INFO - 提取callId: 5871, callTime: 12.799219ms
2025-07-08 19:47:57,656 - INFO - 提取callId: 6175, callTime: 12.750156ms
2025-07-08 19:47:57,656 - INFO - 提取callId: 5923, callTime: 12.718906ms
2025-07-08 19:47:57,656 - INFO - 提取callId: 5935, callTime: 12.708593ms
2025-07-08 19:47:57,656 - INFO - 提取callId: 6003, callTime: 12.655156ms
2025-07-08 19:47:57,656 - INFO - 提取callId: 6235, callTime: 12.642187ms
2025-07-08 19:47:57,657 - INFO - 提取callId: 5875, callTime: 12.622656ms
2025-07-08 19:47:57,657 - INFO - 提取callId: 6215, callTime: 12.60875ms
2025-07-08 19:47:57,657 - INFO - 提取callId: 5323, callTime: 12.5875ms
2025-07-08 19:47:57,657 - INFO - 提取callId: 6227, callTime: 12.417343ms
2025-07-08 19:47:57,657 - INFO - 提取callId: 6143, callTime: 12.414062ms
2025-07-08 19:47:57,657 - INFO - 提取callId: 6207, callTime: 12.413125ms
2025-07-08 19:47:57,657 - INFO - 提取callId: 6247, callTime: 12.402188ms
2025-07-08 19:47:57,657 - INFO - 提取callId: 5867, callTime: 12.389532ms
2025-07-08 19:47:57,657 - INFO - 提取callId: 6231, callTime: 12.365782ms
2025-07-08 19:47:57,658 - INFO - 提取callId: 5811, callTime: 12.364375ms
2025-07-08 19:47:57,658 - INFO - 提取callId: 6187, callTime: 12.35125ms
2025-07-08 19:47:57,658 - INFO - 提取callId: 6139, callTime: 12.328594ms
2025-07-08 19:47:57,658 - INFO - 提取callId: 6219, callTime: 12.303907ms
2025-07-08 19:47:57,658 - INFO - 提取callId: 6203, callTime: 12.261875ms
2025-07-08 19:47:57,658 - INFO - 提取到 40 个event相关的callId
2025-07-08 19:47:57,658 - INFO - 处理 callId=5228, callTime=67.783593ms, rank=1
2025-07-08 19:47:57,658 - INFO - 执行详细分析: callId=5228
2025-07-08 19:47:57,671 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\1-5228.txt
2025-07-08 19:47:57,672 - INFO - 处理 callId=5244, callTime=57.741094ms, rank=2
2025-07-08 19:47:57,672 - INFO - 执行详细分析: callId=5244
2025-07-08 19:47:57,684 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\2-5244.txt
2025-07-08 19:47:57,685 - INFO - 处理 callId=4333, callTime=51.903594ms, rank=3
2025-07-08 19:47:57,685 - INFO - 执行详细分析: callId=4333
2025-07-08 19:47:57,712 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\3-4333.txt
2025-07-08 19:47:57,713 - INFO - 处理 callId=5261, callTime=43.113281ms, rank=4
2025-07-08 19:47:57,713 - INFO - 执行详细分析: callId=5261
2025-07-08 19:47:57,725 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\4-5261.txt
2025-07-08 19:47:57,725 - INFO - 处理 callId=5599, callTime=35.887344ms, rank=5
2025-07-08 19:47:57,726 - INFO - 执行详细分析: callId=5599
2025-07-08 19:47:57,739 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\5-5599.txt
2025-07-08 19:47:57,739 - INFO - 处理 callId=14, callTime=25.719219ms, rank=6
2025-07-08 19:47:57,739 - INFO - 执行详细分析: callId=14
2025-07-08 19:47:57,755 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\6-14.txt
2025-07-08 19:47:57,755 - INFO - 处理 callId=5363, callTime=17.457031ms, rank=7
2025-07-08 19:47:57,755 - INFO - 执行详细分析: callId=5363
2025-07-08 19:47:57,768 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\7-5363.txt
2025-07-08 19:47:57,768 - INFO - 处理 callId=5315, callTime=16.937969ms, rank=8
2025-07-08 19:47:57,769 - INFO - 执行详细分析: callId=5315
2025-07-08 19:47:57,781 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\8-5315.txt
2025-07-08 19:47:57,781 - INFO - 处理 callId=5431, callTime=16.284687ms, rank=9
2025-07-08 19:47:57,781 - INFO - 执行详细分析: callId=5431
2025-07-08 19:47:57,794 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\9-5431.txt
2025-07-08 19:47:57,794 - INFO - 处理 callId=6159, callTime=15.764844ms, rank=10
2025-07-08 19:47:57,794 - INFO - 执行详细分析: callId=6159
2025-07-08 19:47:57,808 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\10-6159.txt
2025-07-08 19:47:57,809 - INFO - 处理 callId=450, callTime=15.045625ms, rank=11
2025-07-08 19:47:57,809 - INFO - 执行详细分析: callId=450
2025-07-08 19:47:57,826 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\11-450.txt
2025-07-08 19:47:57,826 - INFO - 处理 callId=6135, callTime=15.010157ms, rank=12
2025-07-08 19:47:57,826 - INFO - 执行详细分析: callId=6135
2025-07-08 19:47:57,840 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\12-6135.txt
2025-07-08 19:47:57,841 - INFO - 处理 callId=5729, callTime=14.922187ms, rank=13
2025-07-08 19:47:57,841 - INFO - 执行详细分析: callId=5729
2025-07-08 19:47:57,854 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\13-5729.txt
2025-07-08 19:47:57,854 - INFO - 处理 callId=6191, callTime=14.884063ms, rank=14
2025-07-08 19:47:57,854 - INFO - 执行详细分析: callId=6191
2025-07-08 19:47:57,868 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\14-6191.txt
2025-07-08 19:47:57,868 - INFO - 处理 callId=886, callTime=14.87875ms, rank=15
2025-07-08 19:47:57,868 - INFO - 执行详细分析: callId=886
2025-07-08 19:47:57,884 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\15-886.txt
2025-07-08 19:47:57,885 - INFO - 处理 callId=6007, callTime=14.135468ms, rank=16
2025-07-08 19:47:57,885 - INFO - 执行详细分析: callId=6007
2025-07-08 19:47:57,898 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\16-6007.txt
2025-07-08 19:47:57,898 - INFO - 处理 callId=5879, callTime=13.404375ms, rank=17
2025-07-08 19:47:57,898 - INFO - 执行详细分析: callId=5879
2025-07-08 19:47:57,911 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\17-5879.txt
2025-07-08 19:47:57,912 - INFO - 处理 callId=6211, callTime=13.044219ms, rank=18
2025-07-08 19:47:57,912 - INFO - 执行详细分析: callId=6211
2025-07-08 19:47:57,925 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\18-6211.txt
2025-07-08 19:47:57,925 - INFO - 处理 callId=6147, callTime=13.030625ms, rank=19
2025-07-08 19:47:57,925 - INFO - 执行详细分析: callId=6147
2025-07-08 19:47:57,938 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\19-6147.txt
2025-07-08 19:47:57,938 - INFO - 处理 callId=5883, callTime=12.9ms, rank=20
2025-07-08 19:47:57,939 - INFO - 执行详细分析: callId=5883
2025-07-08 19:47:57,952 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\20-5883.txt
2025-07-08 19:47:57,952 - INFO - 处理 callId=5871, callTime=12.799219ms, rank=21
2025-07-08 19:47:57,952 - INFO - 执行详细分析: callId=5871
2025-07-08 19:47:57,966 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\21-5871.txt
2025-07-08 19:47:57,967 - INFO - 处理 callId=6175, callTime=12.750156ms, rank=22
2025-07-08 19:47:57,967 - INFO - 执行详细分析: callId=6175
2025-07-08 19:47:57,981 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\22-6175.txt
2025-07-08 19:47:57,982 - INFO - 处理 callId=5923, callTime=12.718906ms, rank=23
2025-07-08 19:47:57,982 - INFO - 执行详细分析: callId=5923
2025-07-08 19:47:57,996 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\23-5923.txt
2025-07-08 19:47:57,996 - INFO - 处理 callId=5935, callTime=12.708593ms, rank=24
2025-07-08 19:47:57,996 - INFO - 执行详细分析: callId=5935
2025-07-08 19:47:58,009 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\24-5935.txt
2025-07-08 19:47:58,010 - INFO - 处理 callId=6003, callTime=12.655156ms, rank=25
2025-07-08 19:47:58,010 - INFO - 执行详细分析: callId=6003
2025-07-08 19:47:58,023 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\25-6003.txt
2025-07-08 19:47:58,024 - INFO - 处理 callId=6235, callTime=12.642187ms, rank=26
2025-07-08 19:47:58,024 - INFO - 执行详细分析: callId=6235
2025-07-08 19:47:58,037 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\26-6235.txt
2025-07-08 19:47:58,037 - INFO - 处理 callId=5875, callTime=12.622656ms, rank=27
2025-07-08 19:47:58,038 - INFO - 执行详细分析: callId=5875
2025-07-08 19:47:58,051 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\27-5875.txt
2025-07-08 19:47:58,051 - INFO - 处理 callId=6215, callTime=12.60875ms, rank=28
2025-07-08 19:47:58,051 - INFO - 执行详细分析: callId=6215
2025-07-08 19:47:58,065 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\28-6215.txt
2025-07-08 19:47:58,065 - INFO - 处理 callId=5323, callTime=12.5875ms, rank=29
2025-07-08 19:47:58,066 - INFO - 执行详细分析: callId=5323
2025-07-08 19:47:58,079 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\29-5323.txt
2025-07-08 19:47:58,079 - INFO - 处理 callId=6227, callTime=12.417343ms, rank=30
2025-07-08 19:47:58,079 - INFO - 执行详细分析: callId=6227
2025-07-08 19:47:58,093 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\30-6227.txt
2025-07-08 19:47:58,093 - INFO - 处理 callId=6143, callTime=12.414062ms, rank=31
2025-07-08 19:47:58,093 - INFO - 执行详细分析: callId=6143
2025-07-08 19:47:58,107 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\31-6143.txt
2025-07-08 19:47:58,107 - INFO - 处理 callId=6207, callTime=12.413125ms, rank=32
2025-07-08 19:47:58,108 - INFO - 执行详细分析: callId=6207
2025-07-08 19:47:58,122 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\32-6207.txt
2025-07-08 19:47:58,122 - INFO - 处理 callId=6247, callTime=12.402188ms, rank=33
2025-07-08 19:47:58,122 - INFO - 执行详细分析: callId=6247
2025-07-08 19:47:58,136 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\33-6247.txt
2025-07-08 19:47:58,136 - INFO - 处理 callId=5867, callTime=12.389532ms, rank=34
2025-07-08 19:47:58,136 - INFO - 执行详细分析: callId=5867
2025-07-08 19:47:58,150 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\34-5867.txt
2025-07-08 19:47:58,150 - INFO - 处理 callId=6231, callTime=12.365782ms, rank=35
2025-07-08 19:47:58,150 - INFO - 执行详细分析: callId=6231
2025-07-08 19:47:58,165 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\35-6231.txt
2025-07-08 19:47:58,165 - INFO - 处理 callId=5811, callTime=12.364375ms, rank=36
2025-07-08 19:47:58,165 - INFO - 执行详细分析: callId=5811
2025-07-08 19:47:58,179 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\36-5811.txt
2025-07-08 19:47:58,180 - INFO - 处理 callId=6187, callTime=12.35125ms, rank=37
2025-07-08 19:47:58,180 - INFO - 执行详细分析: callId=6187
2025-07-08 19:47:58,194 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\37-6187.txt
2025-07-08 19:47:58,194 - INFO - 处理 callId=6139, callTime=12.328594ms, rank=38
2025-07-08 19:47:58,194 - INFO - 执行详细分析: callId=6139
2025-07-08 19:47:58,208 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\38-6139.txt
2025-07-08 19:47:58,208 - INFO - 处理 callId=6219, callTime=12.303907ms, rank=39
2025-07-08 19:47:58,208 - INFO - 执行详细分析: callId=6219
2025-07-08 19:47:58,221 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\39-6219.txt
2025-07-08 19:47:58,222 - INFO - 处理 callId=6203, callTime=12.261875ms, rank=40
2025-07-08 19:47:58,222 - INFO - 执行详细分析: callId=6203
2025-07-08 19:47:58,235 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103507_wpp动画页切换\40-6203.txt
2025-07-08 19:47:58,235 - INFO - 完成处理文件: perfmon-raster-20250707_103507_wpp动画页切换.dat
2025-07-08 19:47:58,236 - INFO - 处理进度: 21/30
2025-07-08 19:47:58,236 - INFO - 开始处理文件: perfmon-raster-20250707_103924_wpp动画测试荧光笔.dat
2025-07-08 19:47:58,236 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-raster-20250707_103924_wpp动画测试荧光笔.dat
2025-07-08 19:47:58,253 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-raster-20250707_103924_wpp动画测试荧光笔.dat.txt
2025-07-08 19:47:58,253 - INFO - 找到event函数: E:\wpsskia\qt\source\qtbase\src\widgets\kernel\qwidget.cpp:9682 event
2025-07-08 19:47:58,253 - INFO - 提取callId: 3076, callTime: 69.236875ms
2025-07-08 19:47:58,254 - INFO - 提取callId: 3092, callTime: 57.456875ms
2025-07-08 19:47:58,254 - INFO - 提取callId: 3109, callTime: 43.943906ms
2025-07-08 19:47:58,254 - INFO - 提取callId: 2181, callTime: 43.048594ms
2025-07-08 19:47:58,254 - INFO - 提取callId: 3467, callTime: 35.304843ms
2025-07-08 19:47:58,254 - INFO - 提取callId: 14, callTime: 25.719375ms
2025-07-08 19:47:58,254 - INFO - 提取callId: 3151, callTime: 17.509687ms
2025-07-08 19:47:58,254 - INFO - 提取callId: 450, callTime: 15.101875ms
2025-07-08 19:47:58,254 - INFO - 提取callId: 3557, callTime: 13.953281ms
2025-07-08 19:47:58,254 - INFO - 提取callId: 3283, callTime: 13.507343ms
2025-07-08 19:47:58,254 - INFO - 提取callId: 886, callTime: 13.453906ms
2025-07-08 19:47:58,255 - INFO - 提取callId: 3159, callTime: 13.130781ms
2025-07-08 19:47:58,255 - INFO - 提取callId: 1303, callTime: 12.949532ms
2025-07-08 19:47:58,255 - INFO - 提取callId: 3666, callTime: 12.665312ms
2025-07-08 19:47:58,255 - INFO - 提取callId: 3662, callTime: 11.473906ms
2025-07-08 19:47:58,255 - INFO - 提取callId: 3115, callTime: 11.313437ms
2025-07-08 19:47:58,255 - INFO - 提取callId: 3654, callTime: 10.920157ms
2025-07-08 19:47:58,255 - INFO - 提取callId: 3630, callTime: 10.837032ms
2025-07-08 19:47:58,255 - INFO - 提取callId: 3602, callTime: 10.812812ms
2025-07-08 19:47:58,255 - INFO - 提取callId: 3495, callTime: 10.775938ms
2025-07-08 19:47:58,256 - INFO - 提取callId: 3642, callTime: 10.313594ms
2025-07-08 19:47:58,256 - INFO - 提取callId: 4040, callTime: 10.079687ms
2025-07-08 19:47:58,256 - INFO - 提取callId: 3391, callTime: 10.071563ms
2025-07-08 19:47:58,256 - INFO - 提取callId: 3776, callTime: 9.905ms
2025-07-08 19:47:58,256 - INFO - 提取callId: 3463, callTime: 9.750312ms
2025-07-08 19:47:58,256 - INFO - 提取callId: 3646, callTime: 9.29875ms
2025-07-08 19:47:58,256 - INFO - 提取callId: 3712, callTime: 9.245781ms
2025-07-08 19:47:58,256 - INFO - 提取callId: 3634, callTime: 9.195469ms
2025-07-08 19:47:58,256 - INFO - 提取callId: 3590, callTime: 9.161562ms
2025-07-08 19:47:58,257 - INFO - 提取callId: 3267, callTime: 9.046563ms
2025-07-08 19:47:58,257 - INFO - 提取callId: 1764, callTime: 8.985ms
2025-07-08 19:47:58,257 - INFO - 提取callId: 3606, callTime: 8.930156ms
2025-07-08 19:47:58,257 - INFO - 提取callId: 3796, callTime: 8.898594ms
2025-07-08 19:47:58,257 - INFO - 提取callId: 3820, callTime: 8.805781ms
2025-07-08 19:47:58,257 - INFO - 提取callId: 3736, callTime: 8.433593ms
2025-07-08 19:47:58,257 - INFO - 提取callId: 3303, callTime: 8.360157ms
2025-07-08 19:47:58,257 - INFO - 提取callId: 3658, callTime: 8.271719ms
2025-07-08 19:47:58,257 - INFO - 提取callId: 3598, callTime: 8.259062ms
2025-07-08 19:47:58,258 - INFO - 提取callId: 3307, callTime: 8.227031ms
2025-07-08 19:47:58,258 - INFO - 提取callId: 4020, callTime: 8.194688ms
2025-07-08 19:47:58,258 - INFO - 提取到 40 个event相关的callId
2025-07-08 19:47:58,258 - INFO - 处理 callId=3076, callTime=69.236875ms, rank=1
2025-07-08 19:47:58,258 - INFO - 执行详细分析: callId=3076
2025-07-08 19:47:58,270 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\1-3076.txt
2025-07-08 19:47:58,270 - INFO - 处理 callId=3092, callTime=57.456875ms, rank=2
2025-07-08 19:47:58,270 - INFO - 执行详细分析: callId=3092
2025-07-08 19:47:58,282 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\2-3092.txt
2025-07-08 19:47:58,282 - INFO - 处理 callId=3109, callTime=43.943906ms, rank=3
2025-07-08 19:47:58,282 - INFO - 执行详细分析: callId=3109
2025-07-08 19:47:58,293 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\3-3109.txt
2025-07-08 19:47:58,293 - INFO - 处理 callId=2181, callTime=43.048594ms, rank=4
2025-07-08 19:47:58,293 - INFO - 执行详细分析: callId=2181
2025-07-08 19:47:58,320 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\4-2181.txt
2025-07-08 19:47:58,320 - INFO - 处理 callId=3467, callTime=35.304843ms, rank=5
2025-07-08 19:47:58,320 - INFO - 执行详细分析: callId=3467
2025-07-08 19:47:58,332 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\5-3467.txt
2025-07-08 19:47:58,332 - INFO - 处理 callId=14, callTime=25.719375ms, rank=6
2025-07-08 19:47:58,332 - INFO - 执行详细分析: callId=14
2025-07-08 19:47:58,347 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\6-14.txt
2025-07-08 19:47:58,348 - INFO - 处理 callId=3151, callTime=17.509687ms, rank=7
2025-07-08 19:47:58,348 - INFO - 执行详细分析: callId=3151
2025-07-08 19:47:58,359 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\7-3151.txt
2025-07-08 19:47:58,359 - INFO - 处理 callId=450, callTime=15.101875ms, rank=8
2025-07-08 19:47:58,359 - INFO - 执行详细分析: callId=450
2025-07-08 19:47:58,374 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\8-450.txt
2025-07-08 19:47:58,374 - INFO - 处理 callId=3557, callTime=13.953281ms, rank=9
2025-07-08 19:47:58,374 - INFO - 执行详细分析: callId=3557
2025-07-08 19:47:58,386 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\9-3557.txt
2025-07-08 19:47:58,386 - INFO - 处理 callId=3283, callTime=13.507343ms, rank=10
2025-07-08 19:47:58,386 - INFO - 执行详细分析: callId=3283
2025-07-08 19:47:58,398 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\10-3283.txt
2025-07-08 19:47:58,398 - INFO - 处理 callId=886, callTime=13.453906ms, rank=11
2025-07-08 19:47:58,399 - INFO - 执行详细分析: callId=886
2025-07-08 19:47:58,416 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\11-886.txt
2025-07-08 19:47:58,416 - INFO - 处理 callId=3159, callTime=13.130781ms, rank=12
2025-07-08 19:47:58,416 - INFO - 执行详细分析: callId=3159
2025-07-08 19:47:58,428 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\12-3159.txt
2025-07-08 19:47:58,428 - INFO - 处理 callId=1303, callTime=12.949532ms, rank=13
2025-07-08 19:47:58,428 - INFO - 执行详细分析: callId=1303
2025-07-08 19:47:58,446 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\13-1303.txt
2025-07-08 19:47:58,446 - INFO - 处理 callId=3666, callTime=12.665312ms, rank=14
2025-07-08 19:47:58,446 - INFO - 执行详细分析: callId=3666
2025-07-08 19:47:58,458 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\14-3666.txt
2025-07-08 19:47:58,459 - INFO - 处理 callId=3662, callTime=11.473906ms, rank=15
2025-07-08 19:47:58,459 - INFO - 执行详细分析: callId=3662
2025-07-08 19:47:58,480 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\15-3662.txt
2025-07-08 19:47:58,480 - INFO - 处理 callId=3115, callTime=11.313437ms, rank=16
2025-07-08 19:47:58,480 - INFO - 执行详细分析: callId=3115
2025-07-08 19:47:58,492 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\16-3115.txt
2025-07-08 19:47:58,492 - INFO - 处理 callId=3654, callTime=10.920157ms, rank=17
2025-07-08 19:47:58,492 - INFO - 执行详细分析: callId=3654
2025-07-08 19:47:58,504 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\17-3654.txt
2025-07-08 19:47:58,505 - INFO - 处理 callId=3630, callTime=10.837032ms, rank=18
2025-07-08 19:47:58,505 - INFO - 执行详细分析: callId=3630
2025-07-08 19:47:58,516 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\18-3630.txt
2025-07-08 19:47:58,517 - INFO - 处理 callId=3602, callTime=10.812812ms, rank=19
2025-07-08 19:47:58,517 - INFO - 执行详细分析: callId=3602
2025-07-08 19:47:58,528 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\19-3602.txt
2025-07-08 19:47:58,528 - INFO - 处理 callId=3495, callTime=10.775938ms, rank=20
2025-07-08 19:47:58,529 - INFO - 执行详细分析: callId=3495
2025-07-08 19:47:58,540 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\20-3495.txt
2025-07-08 19:47:58,540 - INFO - 处理 callId=3642, callTime=10.313594ms, rank=21
2025-07-08 19:47:58,540 - INFO - 执行详细分析: callId=3642
2025-07-08 19:47:58,552 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\21-3642.txt
2025-07-08 19:47:58,552 - INFO - 处理 callId=4040, callTime=10.079687ms, rank=22
2025-07-08 19:47:58,552 - INFO - 执行详细分析: callId=4040
2025-07-08 19:47:58,564 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\22-4040.txt
2025-07-08 19:47:58,564 - INFO - 处理 callId=3391, callTime=10.071563ms, rank=23
2025-07-08 19:47:58,565 - INFO - 执行详细分析: callId=3391
2025-07-08 19:47:58,576 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\23-3391.txt
2025-07-08 19:47:58,576 - INFO - 处理 callId=3776, callTime=9.905ms, rank=24
2025-07-08 19:47:58,576 - INFO - 执行详细分析: callId=3776
2025-07-08 19:47:58,587 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\24-3776.txt
2025-07-08 19:47:58,587 - INFO - 处理 callId=3463, callTime=9.750312ms, rank=25
2025-07-08 19:47:58,588 - INFO - 执行详细分析: callId=3463
2025-07-08 19:47:58,599 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\25-3463.txt
2025-07-08 19:47:58,599 - INFO - 处理 callId=3646, callTime=9.29875ms, rank=26
2025-07-08 19:47:58,599 - INFO - 执行详细分析: callId=3646
2025-07-08 19:47:58,611 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\26-3646.txt
2025-07-08 19:47:58,611 - INFO - 处理 callId=3712, callTime=9.245781ms, rank=27
2025-07-08 19:47:58,611 - INFO - 执行详细分析: callId=3712
2025-07-08 19:47:58,623 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\27-3712.txt
2025-07-08 19:47:58,623 - INFO - 处理 callId=3634, callTime=9.195469ms, rank=28
2025-07-08 19:47:58,624 - INFO - 执行详细分析: callId=3634
2025-07-08 19:47:58,635 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\28-3634.txt
2025-07-08 19:47:58,635 - INFO - 处理 callId=3590, callTime=9.161562ms, rank=29
2025-07-08 19:47:58,635 - INFO - 执行详细分析: callId=3590
2025-07-08 19:47:58,647 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\29-3590.txt
2025-07-08 19:47:58,647 - INFO - 处理 callId=3267, callTime=9.046563ms, rank=30
2025-07-08 19:47:58,647 - INFO - 执行详细分析: callId=3267
2025-07-08 19:47:58,658 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\30-3267.txt
2025-07-08 19:47:58,658 - INFO - 处理 callId=1764, callTime=8.985ms, rank=31
2025-07-08 19:47:58,658 - INFO - 执行详细分析: callId=1764
2025-07-08 19:47:58,674 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\31-1764.txt
2025-07-08 19:47:58,675 - INFO - 处理 callId=3606, callTime=8.930156ms, rank=32
2025-07-08 19:47:58,675 - INFO - 执行详细分析: callId=3606
2025-07-08 19:47:58,687 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\32-3606.txt
2025-07-08 19:47:58,687 - INFO - 处理 callId=3796, callTime=8.898594ms, rank=33
2025-07-08 19:47:58,687 - INFO - 执行详细分析: callId=3796
2025-07-08 19:47:58,699 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\33-3796.txt
2025-07-08 19:47:58,699 - INFO - 处理 callId=3820, callTime=8.805781ms, rank=34
2025-07-08 19:47:58,699 - INFO - 执行详细分析: callId=3820
2025-07-08 19:47:58,710 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\34-3820.txt
2025-07-08 19:47:58,710 - INFO - 处理 callId=3736, callTime=8.433593ms, rank=35
2025-07-08 19:47:58,711 - INFO - 执行详细分析: callId=3736
2025-07-08 19:47:58,722 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\35-3736.txt
2025-07-08 19:47:58,723 - INFO - 处理 callId=3303, callTime=8.360157ms, rank=36
2025-07-08 19:47:58,723 - INFO - 执行详细分析: callId=3303
2025-07-08 19:47:58,735 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\36-3303.txt
2025-07-08 19:47:58,735 - INFO - 处理 callId=3658, callTime=8.271719ms, rank=37
2025-07-08 19:47:58,735 - INFO - 执行详细分析: callId=3658
2025-07-08 19:47:58,748 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\37-3658.txt
2025-07-08 19:47:58,748 - INFO - 处理 callId=3598, callTime=8.259062ms, rank=38
2025-07-08 19:47:58,748 - INFO - 执行详细分析: callId=3598
2025-07-08 19:47:58,761 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\38-3598.txt
2025-07-08 19:47:58,761 - INFO - 处理 callId=3307, callTime=8.227031ms, rank=39
2025-07-08 19:47:58,761 - INFO - 执行详细分析: callId=3307
2025-07-08 19:47:58,773 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\39-3307.txt
2025-07-08 19:47:58,774 - INFO - 处理 callId=4020, callTime=8.194688ms, rank=40
2025-07-08 19:47:58,774 - INFO - 执行详细分析: callId=4020
2025-07-08 19:47:58,786 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_103924_wpp动画测试荧光笔\40-4020.txt
2025-07-08 19:47:58,786 - INFO - 完成处理文件: perfmon-raster-20250707_103924_wpp动画测试荧光笔.dat
2025-07-08 19:47:58,786 - INFO - 处理进度: 22/30
2025-07-08 19:47:58,787 - INFO - 开始处理文件: perfmon-raster-20250707_155912_wps文档绘制.dat
2025-07-08 19:47:58,787 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-raster-20250707_155912_wps文档绘制.dat
2025-07-08 19:47:58,803 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-raster-20250707_155912_wps文档绘制.dat.txt
2025-07-08 19:47:58,803 - INFO - 找到event函数: E:\wpsskia\qt\source\qtbase\src\widgets\kernel\qwidget.cpp:9682 event
2025-07-08 19:47:58,803 - INFO - 提取callId: 4482, callTime: 75.100938ms
2025-07-08 19:47:58,803 - INFO - 提取callId: 5063, callTime: 45.200156ms
2025-07-08 19:47:58,803 - INFO - 提取callId: 4977, callTime: 40.252031ms
2025-07-08 19:47:58,803 - INFO - 提取callId: 5035, callTime: 38.88875ms
2025-07-08 19:47:58,803 - INFO - 提取callId: 5174, callTime: 31.88375ms
2025-07-08 19:47:58,804 - INFO - 提取callId: 4949, callTime: 29.556875ms
2025-07-08 19:47:58,804 - INFO - 提取callId: 4961, callTime: 29.331094ms
2025-07-08 19:47:58,804 - INFO - 提取callId: 5190, callTime: 28.070781ms
2025-07-08 19:47:58,804 - INFO - 提取callId: 5166, callTime: 27.733907ms
2025-07-08 19:47:58,804 - INFO - 提取callId: 2185, callTime: 26.892343ms
2025-07-08 19:47:58,804 - INFO - 提取callId: 5158, callTime: 26.425ms
2025-07-08 19:47:58,804 - INFO - 提取callId: 4965, callTime: 26.284531ms
2025-07-08 19:47:58,805 - INFO - 提取callId: 4918, callTime: 25.457969ms
2025-07-08 19:47:58,805 - INFO - 提取callId: 5094, callTime: 25.413906ms
2025-07-08 19:47:58,805 - INFO - 提取callId: 14, callTime: 24.918282ms
2025-07-08 19:47:58,805 - INFO - 提取callId: 5162, callTime: 24.698906ms
2025-07-08 19:47:58,805 - INFO - 提取callId: 5154, callTime: 24.6975ms
2025-07-08 19:47:58,805 - INFO - 提取callId: 3872, callTime: 24.465ms
2025-07-08 19:47:58,805 - INFO - 提取callId: 4486, callTime: 23.920469ms
2025-07-08 19:47:58,805 - INFO - 提取callId: 5069, callTime: 23.394219ms
2025-07-08 19:47:58,805 - INFO - 提取callId: 5041, callTime: 23.153125ms
2025-07-08 19:47:58,805 - INFO - 提取callId: 4981, callTime: 22.823907ms
2025-07-08 19:47:58,805 - INFO - 提取callId: 5146, callTime: 22.817032ms
2025-07-08 19:47:58,806 - INFO - 提取callId: 4922, callTime: 22.595625ms
2025-07-08 19:47:58,806 - INFO - 提取callId: 5074, callTime: 22.189375ms
2025-07-08 19:47:58,806 - INFO - 提取callId: 5029, callTime: 21.862031ms
2025-07-08 19:47:58,806 - INFO - 提取callId: 4985, callTime: 19.5375ms
2025-07-08 19:47:58,806 - INFO - 提取callId: 5098, callTime: 19.430156ms
2025-07-08 19:47:58,806 - INFO - 提取callId: 5047, callTime: 19.151406ms
2025-07-08 19:47:58,806 - INFO - 提取callId: 5102, callTime: 18.856406ms
2025-07-08 19:47:58,806 - INFO - 提取callId: 5053, callTime: 18.787656ms
2025-07-08 19:47:58,806 - INFO - 提取callId: 5019, callTime: 18.573437ms
2025-07-08 19:47:58,806 - INFO - 提取callId: 4993, callTime: 18.3775ms
2025-07-08 19:47:58,806 - INFO - 提取callId: 4989, callTime: 18.037187ms
2025-07-08 19:47:58,807 - INFO - 提取callId: 5078, callTime: 17.683907ms
2025-07-08 19:47:58,807 - INFO - 提取callId: 5130, callTime: 17.472031ms
2025-07-08 19:47:58,807 - INFO - 提取callId: 4997, callTime: 17.239375ms
2025-07-08 19:47:58,807 - INFO - 提取callId: 1303, callTime: 17.117032ms
2025-07-08 19:47:58,807 - INFO - 提取callId: 5013, callTime: 17.056406ms
2025-07-08 19:47:58,807 - INFO - 提取callId: 5106, callTime: 16.755312ms
2025-07-08 19:47:58,807 - INFO - 提取到 40 个event相关的callId
2025-07-08 19:47:58,807 - INFO - 处理 callId=4482, callTime=75.100938ms, rank=1
2025-07-08 19:47:58,807 - INFO - 执行详细分析: callId=4482
2025-07-08 19:47:58,821 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\1-4482.txt
2025-07-08 19:47:58,821 - INFO - 处理 callId=5063, callTime=45.200156ms, rank=2
2025-07-08 19:47:58,821 - INFO - 执行详细分析: callId=5063
2025-07-08 19:47:58,834 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\2-5063.txt
2025-07-08 19:47:58,834 - INFO - 处理 callId=4977, callTime=40.252031ms, rank=3
2025-07-08 19:47:58,834 - INFO - 执行详细分析: callId=4977
2025-07-08 19:47:58,846 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\3-4977.txt
2025-07-08 19:47:58,846 - INFO - 处理 callId=5035, callTime=38.88875ms, rank=4
2025-07-08 19:47:58,847 - INFO - 执行详细分析: callId=5035
2025-07-08 19:47:58,859 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\4-5035.txt
2025-07-08 19:47:58,859 - INFO - 处理 callId=5174, callTime=31.88375ms, rank=5
2025-07-08 19:47:58,859 - INFO - 执行详细分析: callId=5174
2025-07-08 19:47:58,872 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\5-5174.txt
2025-07-08 19:47:58,872 - INFO - 处理 callId=4949, callTime=29.556875ms, rank=6
2025-07-08 19:47:58,872 - INFO - 执行详细分析: callId=4949
2025-07-08 19:47:58,884 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\6-4949.txt
2025-07-08 19:47:58,884 - INFO - 处理 callId=4961, callTime=29.331094ms, rank=7
2025-07-08 19:47:58,884 - INFO - 执行详细分析: callId=4961
2025-07-08 19:47:58,896 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\7-4961.txt
2025-07-08 19:47:58,897 - INFO - 处理 callId=5190, callTime=28.070781ms, rank=8
2025-07-08 19:47:58,897 - INFO - 执行详细分析: callId=5190
2025-07-08 19:47:58,909 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\8-5190.txt
2025-07-08 19:47:58,909 - INFO - 处理 callId=5166, callTime=27.733907ms, rank=9
2025-07-08 19:47:58,909 - INFO - 执行详细分析: callId=5166
2025-07-08 19:47:58,921 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\9-5166.txt
2025-07-08 19:47:58,921 - INFO - 处理 callId=2185, callTime=26.892343ms, rank=10
2025-07-08 19:47:58,921 - INFO - 执行详细分析: callId=2185
2025-07-08 19:47:58,938 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\10-2185.txt
2025-07-08 19:47:58,938 - INFO - 处理 callId=5158, callTime=26.425ms, rank=11
2025-07-08 19:47:58,938 - INFO - 执行详细分析: callId=5158
2025-07-08 19:47:58,950 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\11-5158.txt
2025-07-08 19:47:58,950 - INFO - 处理 callId=4965, callTime=26.284531ms, rank=12
2025-07-08 19:47:58,950 - INFO - 执行详细分析: callId=4965
2025-07-08 19:47:58,962 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\12-4965.txt
2025-07-08 19:47:58,962 - INFO - 处理 callId=4918, callTime=25.457969ms, rank=13
2025-07-08 19:47:58,962 - INFO - 执行详细分析: callId=4918
2025-07-08 19:47:58,975 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\13-4918.txt
2025-07-08 19:47:58,975 - INFO - 处理 callId=5094, callTime=25.413906ms, rank=14
2025-07-08 19:47:58,976 - INFO - 执行详细分析: callId=5094
2025-07-08 19:47:58,988 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\14-5094.txt
2025-07-08 19:47:58,988 - INFO - 处理 callId=14, callTime=24.918282ms, rank=15
2025-07-08 19:47:58,988 - INFO - 执行详细分析: callId=14
2025-07-08 19:47:59,004 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\15-14.txt
2025-07-08 19:47:59,004 - INFO - 处理 callId=5162, callTime=24.698906ms, rank=16
2025-07-08 19:47:59,004 - INFO - 执行详细分析: callId=5162
2025-07-08 19:47:59,017 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\16-5162.txt
2025-07-08 19:47:59,017 - INFO - 处理 callId=5154, callTime=24.6975ms, rank=17
2025-07-08 19:47:59,017 - INFO - 执行详细分析: callId=5154
2025-07-08 19:47:59,031 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\17-5154.txt
2025-07-08 19:47:59,031 - INFO - 处理 callId=3872, callTime=24.465ms, rank=18
2025-07-08 19:47:59,031 - INFO - 执行详细分析: callId=3872
2025-07-08 19:47:59,051 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\18-3872.txt
2025-07-08 19:47:59,052 - INFO - 处理 callId=4486, callTime=23.920469ms, rank=19
2025-07-08 19:47:59,052 - INFO - 执行详细分析: callId=4486
2025-07-08 19:47:59,064 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\19-4486.txt
2025-07-08 19:47:59,064 - INFO - 处理 callId=5069, callTime=23.394219ms, rank=20
2025-07-08 19:47:59,064 - INFO - 执行详细分析: callId=5069
2025-07-08 19:47:59,077 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\20-5069.txt
2025-07-08 19:47:59,077 - INFO - 处理 callId=5041, callTime=23.153125ms, rank=21
2025-07-08 19:47:59,077 - INFO - 执行详细分析: callId=5041
2025-07-08 19:47:59,090 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\21-5041.txt
2025-07-08 19:47:59,090 - INFO - 处理 callId=4981, callTime=22.823907ms, rank=22
2025-07-08 19:47:59,090 - INFO - 执行详细分析: callId=4981
2025-07-08 19:47:59,103 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\22-4981.txt
2025-07-08 19:47:59,103 - INFO - 处理 callId=5146, callTime=22.817032ms, rank=23
2025-07-08 19:47:59,103 - INFO - 执行详细分析: callId=5146
2025-07-08 19:47:59,116 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\23-5146.txt
2025-07-08 19:47:59,116 - INFO - 处理 callId=4922, callTime=22.595625ms, rank=24
2025-07-08 19:47:59,116 - INFO - 执行详细分析: callId=4922
2025-07-08 19:47:59,129 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\24-4922.txt
2025-07-08 19:47:59,129 - INFO - 处理 callId=5074, callTime=22.189375ms, rank=25
2025-07-08 19:47:59,129 - INFO - 执行详细分析: callId=5074
2025-07-08 19:47:59,143 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\25-5074.txt
2025-07-08 19:47:59,143 - INFO - 处理 callId=5029, callTime=21.862031ms, rank=26
2025-07-08 19:47:59,144 - INFO - 执行详细分析: callId=5029
2025-07-08 19:47:59,156 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\26-5029.txt
2025-07-08 19:47:59,156 - INFO - 处理 callId=4985, callTime=19.5375ms, rank=27
2025-07-08 19:47:59,157 - INFO - 执行详细分析: callId=4985
2025-07-08 19:47:59,169 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\27-4985.txt
2025-07-08 19:47:59,169 - INFO - 处理 callId=5098, callTime=19.430156ms, rank=28
2025-07-08 19:47:59,169 - INFO - 执行详细分析: callId=5098
2025-07-08 19:47:59,181 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\28-5098.txt
2025-07-08 19:47:59,181 - INFO - 处理 callId=5047, callTime=19.151406ms, rank=29
2025-07-08 19:47:59,182 - INFO - 执行详细分析: callId=5047
2025-07-08 19:47:59,194 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\29-5047.txt
2025-07-08 19:47:59,194 - INFO - 处理 callId=5102, callTime=18.856406ms, rank=30
2025-07-08 19:47:59,194 - INFO - 执行详细分析: callId=5102
2025-07-08 19:47:59,206 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\30-5102.txt
2025-07-08 19:47:59,206 - INFO - 处理 callId=5053, callTime=18.787656ms, rank=31
2025-07-08 19:47:59,206 - INFO - 执行详细分析: callId=5053
2025-07-08 19:47:59,218 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\31-5053.txt
2025-07-08 19:47:59,219 - INFO - 处理 callId=5019, callTime=18.573437ms, rank=32
2025-07-08 19:47:59,219 - INFO - 执行详细分析: callId=5019
2025-07-08 19:47:59,231 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\32-5019.txt
2025-07-08 19:47:59,231 - INFO - 处理 callId=4993, callTime=18.3775ms, rank=33
2025-07-08 19:47:59,231 - INFO - 执行详细分析: callId=4993
2025-07-08 19:47:59,243 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\33-4993.txt
2025-07-08 19:47:59,244 - INFO - 处理 callId=4989, callTime=18.037187ms, rank=34
2025-07-08 19:47:59,244 - INFO - 执行详细分析: callId=4989
2025-07-08 19:47:59,256 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\34-4989.txt
2025-07-08 19:47:59,256 - INFO - 处理 callId=5078, callTime=17.683907ms, rank=35
2025-07-08 19:47:59,257 - INFO - 执行详细分析: callId=5078
2025-07-08 19:47:59,269 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\35-5078.txt
2025-07-08 19:47:59,269 - INFO - 处理 callId=5130, callTime=17.472031ms, rank=36
2025-07-08 19:47:59,269 - INFO - 执行详细分析: callId=5130
2025-07-08 19:47:59,283 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\36-5130.txt
2025-07-08 19:47:59,283 - INFO - 处理 callId=4997, callTime=17.239375ms, rank=37
2025-07-08 19:47:59,283 - INFO - 执行详细分析: callId=4997
2025-07-08 19:47:59,295 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\37-4997.txt
2025-07-08 19:47:59,295 - INFO - 处理 callId=1303, callTime=17.117032ms, rank=38
2025-07-08 19:47:59,295 - INFO - 执行详细分析: callId=1303
2025-07-08 19:47:59,312 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\38-1303.txt
2025-07-08 19:47:59,312 - INFO - 处理 callId=5013, callTime=17.056406ms, rank=39
2025-07-08 19:47:59,312 - INFO - 执行详细分析: callId=5013
2025-07-08 19:47:59,325 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\39-5013.txt
2025-07-08 19:47:59,325 - INFO - 处理 callId=5106, callTime=16.755312ms, rank=40
2025-07-08 19:47:59,325 - INFO - 执行详细分析: callId=5106
2025-07-08 19:47:59,338 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_155912_wps文档绘制\40-5106.txt
2025-07-08 19:47:59,338 - INFO - 完成处理文件: perfmon-raster-20250707_155912_wps文档绘制.dat
2025-07-08 19:47:59,338 - INFO - 处理进度: 23/30
2025-07-08 19:47:59,338 - INFO - 开始处理文件: perfmon-raster-20250707_160622_wps块选.dat
2025-07-08 19:47:59,338 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-raster-20250707_160622_wps块选.dat
2025-07-08 19:47:59,360 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-raster-20250707_160622_wps块选.dat.txt
2025-07-08 19:47:59,360 - INFO - 找到event函数: E:\wpsskia\qt\source\qtbase\src\widgets\kernel\qwidget.cpp:9682 event
2025-07-08 19:47:59,361 - INFO - 提取callId: 5636, callTime: 42.371406ms
2025-07-08 19:47:59,361 - INFO - 提取callId: 7161, callTime: 29.144688ms
2025-07-08 19:47:59,361 - INFO - 提取callId: 7445, callTime: 26.059688ms
2025-07-08 19:47:59,361 - INFO - 提取callId: 7568, callTime: 25.370156ms
2025-07-08 19:47:59,361 - INFO - 提取callId: 14, callTime: 24.940313ms
2025-07-08 19:47:59,361 - INFO - 提取callId: 7694, callTime: 24.611875ms
2025-07-08 19:47:59,361 - INFO - 提取callId: 7353, callTime: 24.426563ms
2025-07-08 19:47:59,361 - INFO - 提取callId: 7149, callTime: 23.272344ms
2025-07-08 19:47:59,361 - INFO - 提取callId: 7623, callTime: 22.98ms
2025-07-08 19:47:59,361 - INFO - 提取callId: 7169, callTime: 22.797968ms
2025-07-08 19:47:59,361 - INFO - 提取callId: 7709, callTime: 22.466875ms
2025-07-08 19:47:59,362 - INFO - 提取callId: 2618, callTime: 22.441563ms
2025-07-08 19:47:59,362 - INFO - 提取callId: 7816, callTime: 22.068281ms
2025-07-08 19:47:59,362 - INFO - 提取callId: 6521, callTime: 21.992031ms
2025-07-08 19:47:59,362 - INFO - 提取callId: 7675, callTime: 21.882031ms
2025-07-08 19:47:59,362 - INFO - 提取callId: 7653, callTime: 20.7725ms
2025-07-08 19:47:59,362 - INFO - 提取callId: 7437, callTime: 20.102344ms
2025-07-08 19:47:59,362 - INFO - 提取callId: 6921, callTime: 19.945469ms
2025-07-08 19:47:59,362 - INFO - 提取callId: 7553, callTime: 19.853281ms
2025-07-08 19:47:59,362 - INFO - 提取callId: 7337, callTime: 19.685781ms
2025-07-08 19:47:59,362 - INFO - 提取callId: 7592, callTime: 19.642812ms
2025-07-08 19:47:59,363 - INFO - 提取callId: 7561, callTime: 19.514375ms
2025-07-08 19:47:59,363 - INFO - 提取callId: 7608, callTime: 19.358594ms
2025-07-08 19:47:59,363 - INFO - 提取callId: 7449, callTime: 19.168438ms
2025-07-08 19:47:59,363 - INFO - 提取callId: 7637, callTime: 18.784375ms
2025-07-08 19:47:59,363 - INFO - 提取callId: 7413, callTime: 18.773282ms
2025-07-08 19:47:59,363 - INFO - 提取callId: 7771, callTime: 18.643907ms
2025-07-08 19:47:59,363 - INFO - 提取callId: 1764, callTime: 18.529531ms
2025-07-08 19:47:59,364 - INFO - 提取callId: 7429, callTime: 18.506719ms
2025-07-08 19:47:59,364 - INFO - 提取callId: 6929, callTime: 18.503125ms
2025-07-08 19:47:59,364 - INFO - 提取callId: 7752, callTime: 18.430469ms
2025-07-08 19:47:59,364 - INFO - 提取callId: 7616, callTime: 17.888593ms
2025-07-08 19:47:59,364 - INFO - 提取callId: 450, callTime: 17.427812ms
2025-07-08 19:47:59,364 - INFO - 提取callId: 7698, callTime: 17.33625ms
2025-07-08 19:47:59,364 - INFO - 提取callId: 7205, callTime: 17.023594ms
2025-07-08 19:47:59,365 - INFO - 提取callId: 7345, callTime: 16.861719ms
2025-07-08 19:47:59,365 - INFO - 提取callId: 7365, callTime: 16.632812ms
2025-07-08 19:47:59,365 - INFO - 提取callId: 7453, callTime: 16.587188ms
2025-07-08 19:47:59,365 - INFO - 提取callId: 7473, callTime: 16.55125ms
2025-07-08 19:47:59,365 - INFO - 提取callId: 7485, callTime: 16.483281ms
2025-07-08 19:47:59,365 - INFO - 提取到 40 个event相关的callId
2025-07-08 19:47:59,365 - INFO - 处理 callId=5636, callTime=42.371406ms, rank=1
2025-07-08 19:47:59,365 - INFO - 执行详细分析: callId=5636
2025-07-08 19:47:59,396 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\1-5636.txt
2025-07-08 19:47:59,396 - INFO - 处理 callId=7161, callTime=29.144688ms, rank=2
2025-07-08 19:47:59,396 - INFO - 执行详细分析: callId=7161
2025-07-08 19:47:59,411 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\2-7161.txt
2025-07-08 19:47:59,411 - INFO - 处理 callId=7445, callTime=26.059688ms, rank=3
2025-07-08 19:47:59,411 - INFO - 执行详细分析: callId=7445
2025-07-08 19:47:59,426 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\3-7445.txt
2025-07-08 19:47:59,426 - INFO - 处理 callId=7568, callTime=25.370156ms, rank=4
2025-07-08 19:47:59,426 - INFO - 执行详细分析: callId=7568
2025-07-08 19:47:59,441 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\4-7568.txt
2025-07-08 19:47:59,441 - INFO - 处理 callId=14, callTime=24.940313ms, rank=5
2025-07-08 19:47:59,441 - INFO - 执行详细分析: callId=14
2025-07-08 19:47:59,456 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\5-14.txt
2025-07-08 19:47:59,456 - INFO - 处理 callId=7694, callTime=24.611875ms, rank=6
2025-07-08 19:47:59,457 - INFO - 执行详细分析: callId=7694
2025-07-08 19:47:59,471 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\6-7694.txt
2025-07-08 19:47:59,471 - INFO - 处理 callId=7353, callTime=24.426563ms, rank=7
2025-07-08 19:47:59,471 - INFO - 执行详细分析: callId=7353
2025-07-08 19:47:59,498 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\7-7353.txt
2025-07-08 19:47:59,498 - INFO - 处理 callId=7149, callTime=23.272344ms, rank=8
2025-07-08 19:47:59,499 - INFO - 执行详细分析: callId=7149
2025-07-08 19:47:59,514 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\8-7149.txt
2025-07-08 19:47:59,514 - INFO - 处理 callId=7623, callTime=22.98ms, rank=9
2025-07-08 19:47:59,514 - INFO - 执行详细分析: callId=7623
2025-07-08 19:47:59,530 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\9-7623.txt
2025-07-08 19:47:59,531 - INFO - 处理 callId=7169, callTime=22.797968ms, rank=10
2025-07-08 19:47:59,531 - INFO - 执行详细分析: callId=7169
2025-07-08 19:47:59,546 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\10-7169.txt
2025-07-08 19:47:59,546 - INFO - 处理 callId=7709, callTime=22.466875ms, rank=11
2025-07-08 19:47:59,546 - INFO - 执行详细分析: callId=7709
2025-07-08 19:47:59,561 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\11-7709.txt
2025-07-08 19:47:59,562 - INFO - 处理 callId=2618, callTime=22.441563ms, rank=12
2025-07-08 19:47:59,562 - INFO - 执行详细分析: callId=2618
2025-07-08 19:47:59,579 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\12-2618.txt
2025-07-08 19:47:59,580 - INFO - 处理 callId=7816, callTime=22.068281ms, rank=13
2025-07-08 19:47:59,580 - INFO - 执行详细分析: callId=7816
2025-07-08 19:47:59,596 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\13-7816.txt
2025-07-08 19:47:59,596 - INFO - 处理 callId=6521, callTime=21.992031ms, rank=14
2025-07-08 19:47:59,597 - INFO - 执行详细分析: callId=6521
2025-07-08 19:47:59,611 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\14-6521.txt
2025-07-08 19:47:59,611 - INFO - 处理 callId=7675, callTime=21.882031ms, rank=15
2025-07-08 19:47:59,611 - INFO - 执行详细分析: callId=7675
2025-07-08 19:47:59,627 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\15-7675.txt
2025-07-08 19:47:59,627 - INFO - 处理 callId=7653, callTime=20.7725ms, rank=16
2025-07-08 19:47:59,627 - INFO - 执行详细分析: callId=7653
2025-07-08 19:47:59,642 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\16-7653.txt
2025-07-08 19:47:59,642 - INFO - 处理 callId=7437, callTime=20.102344ms, rank=17
2025-07-08 19:47:59,642 - INFO - 执行详细分析: callId=7437
2025-07-08 19:47:59,657 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\17-7437.txt
2025-07-08 19:47:59,657 - INFO - 处理 callId=6921, callTime=19.945469ms, rank=18
2025-07-08 19:47:59,657 - INFO - 执行详细分析: callId=6921
2025-07-08 19:47:59,671 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\18-6921.txt
2025-07-08 19:47:59,671 - INFO - 处理 callId=7553, callTime=19.853281ms, rank=19
2025-07-08 19:47:59,672 - INFO - 执行详细分析: callId=7553
2025-07-08 19:47:59,686 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\19-7553.txt
2025-07-08 19:47:59,687 - INFO - 处理 callId=7337, callTime=19.685781ms, rank=20
2025-07-08 19:47:59,687 - INFO - 执行详细分析: callId=7337
2025-07-08 19:47:59,702 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\20-7337.txt
2025-07-08 19:47:59,702 - INFO - 处理 callId=7592, callTime=19.642812ms, rank=21
2025-07-08 19:47:59,702 - INFO - 执行详细分析: callId=7592
2025-07-08 19:47:59,717 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\21-7592.txt
2025-07-08 19:47:59,717 - INFO - 处理 callId=7561, callTime=19.514375ms, rank=22
2025-07-08 19:47:59,717 - INFO - 执行详细分析: callId=7561
2025-07-08 19:47:59,732 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\22-7561.txt
2025-07-08 19:47:59,732 - INFO - 处理 callId=7608, callTime=19.358594ms, rank=23
2025-07-08 19:47:59,732 - INFO - 执行详细分析: callId=7608
2025-07-08 19:47:59,749 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\23-7608.txt
2025-07-08 19:47:59,750 - INFO - 处理 callId=7449, callTime=19.168438ms, rank=24
2025-07-08 19:47:59,750 - INFO - 执行详细分析: callId=7449
2025-07-08 19:47:59,767 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\24-7449.txt
2025-07-08 19:47:59,767 - INFO - 处理 callId=7637, callTime=18.784375ms, rank=25
2025-07-08 19:47:59,767 - INFO - 执行详细分析: callId=7637
2025-07-08 19:47:59,783 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\25-7637.txt
2025-07-08 19:47:59,783 - INFO - 处理 callId=7413, callTime=18.773282ms, rank=26
2025-07-08 19:47:59,783 - INFO - 执行详细分析: callId=7413
2025-07-08 19:47:59,799 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\26-7413.txt
2025-07-08 19:47:59,799 - INFO - 处理 callId=7771, callTime=18.643907ms, rank=27
2025-07-08 19:47:59,799 - INFO - 执行详细分析: callId=7771
2025-07-08 19:47:59,814 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\27-7771.txt
2025-07-08 19:47:59,814 - INFO - 处理 callId=1764, callTime=18.529531ms, rank=28
2025-07-08 19:47:59,815 - INFO - 执行详细分析: callId=1764
2025-07-08 19:47:59,832 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\28-1764.txt
2025-07-08 19:47:59,832 - INFO - 处理 callId=7429, callTime=18.506719ms, rank=29
2025-07-08 19:47:59,832 - INFO - 执行详细分析: callId=7429
2025-07-08 19:47:59,847 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\29-7429.txt
2025-07-08 19:47:59,848 - INFO - 处理 callId=6929, callTime=18.503125ms, rank=30
2025-07-08 19:47:59,848 - INFO - 执行详细分析: callId=6929
2025-07-08 19:47:59,863 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\30-6929.txt
2025-07-08 19:47:59,863 - INFO - 处理 callId=7752, callTime=18.430469ms, rank=31
2025-07-08 19:47:59,863 - INFO - 执行详细分析: callId=7752
2025-07-08 19:47:59,878 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\31-7752.txt
2025-07-08 19:47:59,878 - INFO - 处理 callId=7616, callTime=17.888593ms, rank=32
2025-07-08 19:47:59,878 - INFO - 执行详细分析: callId=7616
2025-07-08 19:47:59,893 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\32-7616.txt
2025-07-08 19:47:59,893 - INFO - 处理 callId=450, callTime=17.427812ms, rank=33
2025-07-08 19:47:59,893 - INFO - 执行详细分析: callId=450
2025-07-08 19:47:59,909 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\33-450.txt
2025-07-08 19:47:59,909 - INFO - 处理 callId=7698, callTime=17.33625ms, rank=34
2025-07-08 19:47:59,910 - INFO - 执行详细分析: callId=7698
2025-07-08 19:47:59,924 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\34-7698.txt
2025-07-08 19:47:59,924 - INFO - 处理 callId=7205, callTime=17.023594ms, rank=35
2025-07-08 19:47:59,925 - INFO - 执行详细分析: callId=7205
2025-07-08 19:47:59,939 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\35-7205.txt
2025-07-08 19:47:59,939 - INFO - 处理 callId=7345, callTime=16.861719ms, rank=36
2025-07-08 19:47:59,939 - INFO - 执行详细分析: callId=7345
2025-07-08 19:47:59,953 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\36-7345.txt
2025-07-08 19:47:59,954 - INFO - 处理 callId=7365, callTime=16.632812ms, rank=37
2025-07-08 19:47:59,954 - INFO - 执行详细分析: callId=7365
2025-07-08 19:47:59,968 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\37-7365.txt
2025-07-08 19:47:59,969 - INFO - 处理 callId=7453, callTime=16.587188ms, rank=38
2025-07-08 19:47:59,969 - INFO - 执行详细分析: callId=7453
2025-07-08 19:47:59,984 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\38-7453.txt
2025-07-08 19:47:59,984 - INFO - 处理 callId=7473, callTime=16.55125ms, rank=39
2025-07-08 19:47:59,984 - INFO - 执行详细分析: callId=7473
2025-07-08 19:47:59,998 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\39-7473.txt
2025-07-08 19:47:59,998 - INFO - 处理 callId=7485, callTime=16.483281ms, rank=40
2025-07-08 19:47:59,999 - INFO - 执行详细分析: callId=7485
2025-07-08 19:48:00,014 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_160622_wps块选\40-7485.txt
2025-07-08 19:48:00,014 - INFO - 完成处理文件: perfmon-raster-20250707_160622_wps块选.dat
2025-07-08 19:48:00,014 - INFO - 处理进度: 24/30
2025-07-08 19:48:00,015 - INFO - 开始处理文件: perfmon-raster-20250707_161004_wps插入.dat
2025-07-08 19:48:00,015 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-raster-20250707_161004_wps插入.dat
2025-07-08 19:48:00,053 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-raster-20250707_161004_wps插入.dat.txt
2025-07-08 19:48:00,053 - INFO - 找到event函数: E:\wpsskia\qt\source\qtbase\src\widgets\kernel\qwidget.cpp:9682 event
2025-07-08 19:48:00,053 - INFO - 提取callId: 2579, callTime: 46.254688ms
2025-07-08 19:48:00,053 - INFO - 提取callId: 3267, callTime: 45.353907ms
2025-07-08 19:48:00,053 - INFO - 提取callId: 4978, callTime: 36.2275ms
2025-07-08 19:48:00,054 - INFO - 提取callId: 3263, callTime: 31.381875ms
2025-07-08 19:48:00,054 - INFO - 提取callId: 3255, callTime: 27.335ms
2025-07-08 19:48:00,054 - INFO - 提取callId: 1367, callTime: 25.683282ms
2025-07-08 19:48:00,054 - INFO - 提取callId: 3279, callTime: 23.660937ms
2025-07-08 19:48:00,054 - INFO - 提取callId: 1969, callTime: 21.7975ms
2025-07-08 19:48:00,054 - INFO - 提取callId: 5415, callTime: 21.239687ms
2025-07-08 19:48:00,054 - INFO - 提取callId: 5398, callTime: 21.078281ms
2025-07-08 19:48:00,054 - INFO - 提取callId: 5390, callTime: 19.03125ms
2025-07-08 19:48:00,054 - INFO - 提取callId: 16, callTime: 18.732344ms
2025-07-08 19:48:00,054 - INFO - 提取callId: 3295, callTime: 18.559844ms
2025-07-08 19:48:00,054 - INFO - 提取callId: 5394, callTime: 18.12125ms
2025-07-08 19:48:00,054 - INFO - 提取callId: 4966, callTime: 17.475938ms
2025-07-08 19:48:00,054 - INFO - 提取callId: 2938, callTime: 17.421562ms
2025-07-08 19:48:00,055 - INFO - 提取callId: 3030, callTime: 16.717031ms
2025-07-08 19:48:00,055 - INFO - 提取callId: 5090, callTime: 16.239688ms
2025-07-08 19:48:00,055 - INFO - 提取callId: 4986, callTime: 15.953125ms
2025-07-08 19:48:00,055 - INFO - 提取callId: 2583, callTime: 14.528125ms
2025-07-08 19:48:00,055 - INFO - 提取callId: 3106, callTime: 13.668437ms
2025-07-08 19:48:00,055 - INFO - 提取callId: 3259, callTime: 13.082656ms
2025-07-08 19:48:00,055 - INFO - 提取callId: 2735, callTime: 12.703906ms
2025-07-08 19:48:00,055 - INFO - 提取callId: 2619, callTime: 12.024375ms
2025-07-08 19:48:00,055 - INFO - 提取callId: 5370, callTime: 11.815156ms
2025-07-08 19:48:00,055 - INFO - 提取callId: 5382, callTime: 11.792031ms
2025-07-08 19:48:00,055 - INFO - 提取callId: 3271, callTime: 11.345938ms
2025-07-08 19:48:00,056 - INFO - 提取callId: 3299, callTime: 11.308594ms
2025-07-08 19:48:00,056 - INFO - 提取callId: 5374, callTime: 11.147032ms
2025-07-08 19:48:00,056 - INFO - 提取callId: 1235, callTime: 10.999532ms
2025-07-08 19:48:00,056 - INFO - 提取callId: 5174, callTime: 10.875156ms
2025-07-08 19:48:00,056 - INFO - 提取callId: 3226, callTime: 10.774843ms
2025-07-08 19:48:00,056 - INFO - 提取callId: 5386, callTime: 10.706406ms
2025-07-08 19:48:00,056 - INFO - 提取callId: 5306, callTime: 10.592031ms
2025-07-08 19:48:00,056 - INFO - 提取callId: 4227, callTime: 10.550468ms
2025-07-08 19:48:00,056 - INFO - 提取callId: 4571, callTime: 10.544531ms
2025-07-08 19:48:00,057 - INFO - 提取callId: 2891, callTime: 10.530781ms
2025-07-08 19:48:00,057 - INFO - 提取callId: 4267, callTime: 10.389688ms
2025-07-08 19:48:00,057 - INFO - 提取callId: 2663, callTime: 10.386875ms
2025-07-08 19:48:00,057 - INFO - 提取callId: 4471, callTime: 10.368907ms
2025-07-08 19:48:00,057 - INFO - 提取到 40 个event相关的callId
2025-07-08 19:48:00,057 - INFO - 处理 callId=2579, callTime=46.254688ms, rank=1
2025-07-08 19:48:00,057 - INFO - 执行详细分析: callId=2579
2025-07-08 19:48:00,069 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\1-2579.txt
2025-07-08 19:48:00,069 - INFO - 处理 callId=3267, callTime=45.353907ms, rank=2
2025-07-08 19:48:00,069 - INFO - 执行详细分析: callId=3267
2025-07-08 19:48:00,081 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\2-3267.txt
2025-07-08 19:48:00,081 - INFO - 处理 callId=4978, callTime=36.2275ms, rank=3
2025-07-08 19:48:00,081 - INFO - 执行详细分析: callId=4978
2025-07-08 19:48:00,095 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\3-4978.txt
2025-07-08 19:48:00,095 - INFO - 处理 callId=3263, callTime=31.381875ms, rank=4
2025-07-08 19:48:00,095 - INFO - 执行详细分析: callId=3263
2025-07-08 19:48:00,108 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\4-3263.txt
2025-07-08 19:48:00,108 - INFO - 处理 callId=3255, callTime=27.335ms, rank=5
2025-07-08 19:48:00,108 - INFO - 执行详细分析: callId=3255
2025-07-08 19:48:00,120 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\5-3255.txt
2025-07-08 19:48:00,120 - INFO - 处理 callId=1367, callTime=25.683282ms, rank=6
2025-07-08 19:48:00,120 - INFO - 执行详细分析: callId=1367
2025-07-08 19:48:00,140 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\6-1367.txt
2025-07-08 19:48:00,140 - INFO - 处理 callId=3279, callTime=23.660937ms, rank=7
2025-07-08 19:48:00,140 - INFO - 执行详细分析: callId=3279
2025-07-08 19:48:00,151 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\7-3279.txt
2025-07-08 19:48:00,151 - INFO - 处理 callId=1969, callTime=21.7975ms, rank=8
2025-07-08 19:48:00,152 - INFO - 执行详细分析: callId=1969
2025-07-08 19:48:00,171 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\8-1969.txt
2025-07-08 19:48:00,171 - INFO - 处理 callId=5415, callTime=21.239687ms, rank=9
2025-07-08 19:48:00,171 - INFO - 执行详细分析: callId=5415
2025-07-08 19:48:00,184 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\9-5415.txt
2025-07-08 19:48:00,184 - INFO - 处理 callId=5398, callTime=21.078281ms, rank=10
2025-07-08 19:48:00,184 - INFO - 执行详细分析: callId=5398
2025-07-08 19:48:00,197 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\10-5398.txt
2025-07-08 19:48:00,197 - INFO - 处理 callId=5390, callTime=19.03125ms, rank=11
2025-07-08 19:48:00,197 - INFO - 执行详细分析: callId=5390
2025-07-08 19:48:00,210 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\11-5390.txt
2025-07-08 19:48:00,210 - INFO - 处理 callId=16, callTime=18.732344ms, rank=12
2025-07-08 19:48:00,210 - INFO - 执行详细分析: callId=16
2025-07-08 19:48:00,223 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\12-16.txt
2025-07-08 19:48:00,223 - INFO - 处理 callId=3295, callTime=18.559844ms, rank=13
2025-07-08 19:48:00,223 - INFO - 执行详细分析: callId=3295
2025-07-08 19:48:00,234 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\13-3295.txt
2025-07-08 19:48:00,235 - INFO - 处理 callId=5394, callTime=18.12125ms, rank=14
2025-07-08 19:48:00,235 - INFO - 执行详细分析: callId=5394
2025-07-08 19:48:00,247 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\14-5394.txt
2025-07-08 19:48:00,247 - INFO - 处理 callId=4966, callTime=17.475938ms, rank=15
2025-07-08 19:48:00,247 - INFO - 执行详细分析: callId=4966
2025-07-08 19:48:00,259 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\15-4966.txt
2025-07-08 19:48:00,260 - INFO - 处理 callId=2938, callTime=17.421562ms, rank=16
2025-07-08 19:48:00,260 - INFO - 执行详细分析: callId=2938
2025-07-08 19:48:00,270 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\16-2938.txt
2025-07-08 19:48:00,270 - INFO - 处理 callId=3030, callTime=16.717031ms, rank=17
2025-07-08 19:48:00,270 - INFO - 执行详细分析: callId=3030
2025-07-08 19:48:00,281 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\17-3030.txt
2025-07-08 19:48:00,281 - INFO - 处理 callId=5090, callTime=16.239688ms, rank=18
2025-07-08 19:48:00,281 - INFO - 执行详细分析: callId=5090
2025-07-08 19:48:00,294 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\18-5090.txt
2025-07-08 19:48:00,294 - INFO - 处理 callId=4986, callTime=15.953125ms, rank=19
2025-07-08 19:48:00,295 - INFO - 执行详细分析: callId=4986
2025-07-08 19:48:00,307 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\19-4986.txt
2025-07-08 19:48:00,307 - INFO - 处理 callId=2583, callTime=14.528125ms, rank=20
2025-07-08 19:48:00,307 - INFO - 执行详细分析: callId=2583
2025-07-08 19:48:00,318 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\20-2583.txt
2025-07-08 19:48:00,318 - INFO - 处理 callId=3106, callTime=13.668437ms, rank=21
2025-07-08 19:48:00,318 - INFO - 执行详细分析: callId=3106
2025-07-08 19:48:00,328 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\21-3106.txt
2025-07-08 19:48:00,329 - INFO - 处理 callId=3259, callTime=13.082656ms, rank=22
2025-07-08 19:48:00,329 - INFO - 执行详细分析: callId=3259
2025-07-08 19:48:00,340 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\22-3259.txt
2025-07-08 19:48:00,340 - INFO - 处理 callId=2735, callTime=12.703906ms, rank=23
2025-07-08 19:48:00,340 - INFO - 执行详细分析: callId=2735
2025-07-08 19:48:00,351 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\23-2735.txt
2025-07-08 19:48:00,351 - INFO - 处理 callId=2619, callTime=12.024375ms, rank=24
2025-07-08 19:48:00,352 - INFO - 执行详细分析: callId=2619
2025-07-08 19:48:00,362 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\24-2619.txt
2025-07-08 19:48:00,362 - INFO - 处理 callId=5370, callTime=11.815156ms, rank=25
2025-07-08 19:48:00,362 - INFO - 执行详细分析: callId=5370
2025-07-08 19:48:00,375 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\25-5370.txt
2025-07-08 19:48:00,376 - INFO - 处理 callId=5382, callTime=11.792031ms, rank=26
2025-07-08 19:48:00,376 - INFO - 执行详细分析: callId=5382
2025-07-08 19:48:00,388 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\26-5382.txt
2025-07-08 19:48:00,389 - INFO - 处理 callId=3271, callTime=11.345938ms, rank=27
2025-07-08 19:48:00,389 - INFO - 执行详细分析: callId=3271
2025-07-08 19:48:00,400 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\27-3271.txt
2025-07-08 19:48:00,400 - INFO - 处理 callId=3299, callTime=11.308594ms, rank=28
2025-07-08 19:48:00,400 - INFO - 执行详细分析: callId=3299
2025-07-08 19:48:00,412 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\28-3299.txt
2025-07-08 19:48:00,412 - INFO - 处理 callId=5374, callTime=11.147032ms, rank=29
2025-07-08 19:48:00,412 - INFO - 执行详细分析: callId=5374
2025-07-08 19:48:00,425 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\29-5374.txt
2025-07-08 19:48:00,425 - INFO - 处理 callId=1235, callTime=10.999532ms, rank=30
2025-07-08 19:48:00,425 - INFO - 执行详细分析: callId=1235
2025-07-08 19:48:00,436 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\30-1235.txt
2025-07-08 19:48:00,436 - INFO - 处理 callId=5174, callTime=10.875156ms, rank=31
2025-07-08 19:48:00,436 - INFO - 执行详细分析: callId=5174
2025-07-08 19:48:00,449 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\31-5174.txt
2025-07-08 19:48:00,449 - INFO - 处理 callId=3226, callTime=10.774843ms, rank=32
2025-07-08 19:48:00,449 - INFO - 执行详细分析: callId=3226
2025-07-08 19:48:00,461 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\32-3226.txt
2025-07-08 19:48:00,461 - INFO - 处理 callId=5386, callTime=10.706406ms, rank=33
2025-07-08 19:48:00,461 - INFO - 执行详细分析: callId=5386
2025-07-08 19:48:00,474 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\33-5386.txt
2025-07-08 19:48:00,474 - INFO - 处理 callId=5306, callTime=10.592031ms, rank=34
2025-07-08 19:48:00,474 - INFO - 执行详细分析: callId=5306
2025-07-08 19:48:00,487 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\34-5306.txt
2025-07-08 19:48:00,487 - INFO - 处理 callId=4227, callTime=10.550468ms, rank=35
2025-07-08 19:48:00,487 - INFO - 执行详细分析: callId=4227
2025-07-08 19:48:00,500 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\35-4227.txt
2025-07-08 19:48:00,500 - INFO - 处理 callId=4571, callTime=10.544531ms, rank=36
2025-07-08 19:48:00,500 - INFO - 执行详细分析: callId=4571
2025-07-08 19:48:00,521 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\36-4571.txt
2025-07-08 19:48:00,521 - INFO - 处理 callId=2891, callTime=10.530781ms, rank=37
2025-07-08 19:48:00,521 - INFO - 执行详细分析: callId=2891
2025-07-08 19:48:00,533 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\37-2891.txt
2025-07-08 19:48:00,533 - INFO - 处理 callId=4267, callTime=10.389688ms, rank=38
2025-07-08 19:48:00,533 - INFO - 执行详细分析: callId=4267
2025-07-08 19:48:00,546 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\38-4267.txt
2025-07-08 19:48:00,546 - INFO - 处理 callId=2663, callTime=10.386875ms, rank=39
2025-07-08 19:48:00,546 - INFO - 执行详细分析: callId=2663
2025-07-08 19:48:00,558 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\39-2663.txt
2025-07-08 19:48:00,559 - INFO - 处理 callId=4471, callTime=10.368907ms, rank=40
2025-07-08 19:48:00,559 - INFO - 执行详细分析: callId=4471
2025-07-08 19:48:00,572 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161004_wps插入\40-4471.txt
2025-07-08 19:48:00,572 - INFO - 完成处理文件: perfmon-raster-20250707_161004_wps插入.dat
2025-07-08 19:48:00,572 - INFO - 处理进度: 25/30
2025-07-08 19:48:00,573 - INFO - 开始处理文件: perfmon-raster-20250707_161420_wps滚动文档.dat
2025-07-08 19:48:00,573 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-raster-20250707_161420_wps滚动文档.dat
2025-07-08 19:48:00,641 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-raster-20250707_161420_wps滚动文档.dat.txt
2025-07-08 19:48:00,642 - INFO - 找到event函数: E:\wpsskia\qt\source\qtbase\src\widgets\kernel\qwidget.cpp:9682 event
2025-07-08 19:48:00,642 - INFO - 提取callId: 7562, callTime: 48.382968ms
2025-07-08 19:48:00,642 - INFO - 提取callId: 5169, callTime: 26.912187ms
2025-07-08 19:48:00,642 - INFO - 提取callId: 14, callTime: 24.833438ms
2025-07-08 19:48:00,642 - INFO - 提取callId: 6350, callTime: 24.585938ms
2025-07-08 19:48:00,642 - INFO - 提取callId: 7973, callTime: 23.112969ms
2025-07-08 19:48:00,642 - INFO - 提取callId: 6952, callTime: 21.162656ms
2025-07-08 19:48:00,642 - INFO - 提取callId: 1743, callTime: 19.088594ms
2025-07-08 19:48:00,643 - INFO - 提取callId: 11903, callTime: 17.975781ms
2025-07-08 19:48:00,643 - INFO - 提取callId: 2618, callTime: 17.180782ms
2025-07-08 19:48:00,643 - INFO - 提取callId: 7977, callTime: 16.775156ms
2025-07-08 19:48:00,643 - INFO - 提取callId: 450, callTime: 16.104375ms
2025-07-08 19:48:00,643 - INFO - 提取callId: 9515, callTime: 15.535312ms
2025-07-08 19:48:00,643 - INFO - 提取callId: 1303, callTime: 15.512188ms
2025-07-08 19:48:00,643 - INFO - 提取callId: 5908, callTime: 15.361718ms
2025-07-08 19:48:00,643 - INFO - 提取callId: 7845, callTime: 15.358593ms
2025-07-08 19:48:00,643 - INFO - 提取callId: 2201, callTime: 15.227187ms
2025-07-08 19:48:00,644 - INFO - 提取callId: 8730, callTime: 15.090312ms
2025-07-08 19:48:00,644 - INFO - 提取callId: 7981, callTime: 14.52625ms
2025-07-08 19:48:00,644 - INFO - 提取callId: 9067, callTime: 14.466875ms
2025-07-08 19:48:00,644 - INFO - 提取callId: 9898, callTime: 14.129843ms
2025-07-08 19:48:00,644 - INFO - 提取callId: 10197, callTime: 14.001406ms
2025-07-08 19:48:00,644 - INFO - 提取callId: 7566, callTime: 13.944218ms
2025-07-08 19:48:00,644 - INFO - 提取callId: 8435, callTime: 13.844688ms
2025-07-08 19:48:00,644 - INFO - 提取callId: 10532, callTime: 13.82ms
2025-07-08 19:48:00,644 - INFO - 提取callId: 8454, callTime: 13.794531ms
2025-07-08 19:48:00,645 - INFO - 提取callId: 10389, callTime: 13.703594ms
2025-07-08 19:48:00,645 - INFO - 提取callId: 9519, callTime: 13.695ms
2025-07-08 19:48:00,645 - INFO - 提取callId: 10496, callTime: 13.678438ms
2025-07-08 19:48:00,645 - INFO - 提取callId: 9511, callTime: 13.582813ms
2025-07-08 19:48:00,645 - INFO - 提取callId: 8069, callTime: 13.560156ms
2025-07-08 19:48:00,645 - INFO - 提取callId: 9075, callTime: 13.474375ms
2025-07-08 19:48:00,645 - INFO - 提取callId: 9019, callTime: 13.418282ms
2025-07-08 19:48:00,645 - INFO - 提取callId: 9902, callTime: 13.3975ms
2025-07-08 19:48:00,646 - INFO - 提取callId: 9071, callTime: 13.370625ms
2025-07-08 19:48:00,646 - INFO - 提取callId: 10141, callTime: 13.342812ms
2025-07-08 19:48:00,646 - INFO - 提取callId: 10145, callTime: 13.342344ms
2025-07-08 19:48:00,646 - INFO - 提取callId: 10133, callTime: 13.277031ms
2025-07-08 19:48:00,646 - INFO - 提取callId: 10229, callTime: 13.245781ms
2025-07-08 19:48:00,646 - INFO - 提取callId: 9023, callTime: 13.239688ms
2025-07-08 19:48:00,646 - INFO - 提取callId: 8901, callTime: 13.215156ms
2025-07-08 19:48:00,646 - INFO - 提取到 40 个event相关的callId
2025-07-08 19:48:00,646 - INFO - 处理 callId=7562, callTime=48.382968ms, rank=1
2025-07-08 19:48:00,646 - INFO - 执行详细分析: callId=7562
2025-07-08 19:48:00,662 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\1-7562.txt
2025-07-08 19:48:00,663 - INFO - 处理 callId=5169, callTime=26.912187ms, rank=2
2025-07-08 19:48:00,663 - INFO - 执行详细分析: callId=5169
2025-07-08 19:48:00,683 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\2-5169.txt
2025-07-08 19:48:00,683 - INFO - 处理 callId=14, callTime=24.833438ms, rank=3
2025-07-08 19:48:00,683 - INFO - 执行详细分析: callId=14
2025-07-08 19:48:00,700 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\3-14.txt
2025-07-08 19:48:00,700 - INFO - 处理 callId=6350, callTime=24.585938ms, rank=4
2025-07-08 19:48:00,700 - INFO - 执行详细分析: callId=6350
2025-07-08 19:48:00,724 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\4-6350.txt
2025-07-08 19:48:00,724 - INFO - 处理 callId=7973, callTime=23.112969ms, rank=5
2025-07-08 19:48:00,724 - INFO - 执行详细分析: callId=7973
2025-07-08 19:48:00,740 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\5-7973.txt
2025-07-08 19:48:00,741 - INFO - 处理 callId=6952, callTime=21.162656ms, rank=6
2025-07-08 19:48:00,741 - INFO - 执行详细分析: callId=6952
2025-07-08 19:48:00,765 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\6-6952.txt
2025-07-08 19:48:00,765 - INFO - 处理 callId=1743, callTime=19.088594ms, rank=7
2025-07-08 19:48:00,766 - INFO - 执行详细分析: callId=1743
2025-07-08 19:48:00,784 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\7-1743.txt
2025-07-08 19:48:00,784 - INFO - 处理 callId=11903, callTime=17.975781ms, rank=8
2025-07-08 19:48:00,784 - INFO - 执行详细分析: callId=11903
2025-07-08 19:48:00,802 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\8-11903.txt
2025-07-08 19:48:00,802 - INFO - 处理 callId=2618, callTime=17.180782ms, rank=9
2025-07-08 19:48:00,803 - INFO - 执行详细分析: callId=2618
2025-07-08 19:48:00,820 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\9-2618.txt
2025-07-08 19:48:00,820 - INFO - 处理 callId=7977, callTime=16.775156ms, rank=10
2025-07-08 19:48:00,820 - INFO - 执行详细分析: callId=7977
2025-07-08 19:48:00,835 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\10-7977.txt
2025-07-08 19:48:00,835 - INFO - 处理 callId=450, callTime=16.104375ms, rank=11
2025-07-08 19:48:00,835 - INFO - 执行详细分析: callId=450
2025-07-08 19:48:00,851 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\11-450.txt
2025-07-08 19:48:00,852 - INFO - 处理 callId=9515, callTime=15.535312ms, rank=12
2025-07-08 19:48:00,852 - INFO - 执行详细分析: callId=9515
2025-07-08 19:48:00,869 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\12-9515.txt
2025-07-08 19:48:00,869 - INFO - 处理 callId=1303, callTime=15.512188ms, rank=13
2025-07-08 19:48:00,869 - INFO - 执行详细分析: callId=1303
2025-07-08 19:48:00,886 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\13-1303.txt
2025-07-08 19:48:00,886 - INFO - 处理 callId=5908, callTime=15.361718ms, rank=14
2025-07-08 19:48:00,886 - INFO - 执行详细分析: callId=5908
2025-07-08 19:48:00,904 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\14-5908.txt
2025-07-08 19:48:00,904 - INFO - 处理 callId=7845, callTime=15.358593ms, rank=15
2025-07-08 19:48:00,904 - INFO - 执行详细分析: callId=7845
2025-07-08 19:48:00,919 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\15-7845.txt
2025-07-08 19:48:00,920 - INFO - 处理 callId=2201, callTime=15.227187ms, rank=16
2025-07-08 19:48:00,920 - INFO - 执行详细分析: callId=2201
2025-07-08 19:48:00,937 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\16-2201.txt
2025-07-08 19:48:00,937 - INFO - 处理 callId=8730, callTime=15.090312ms, rank=17
2025-07-08 19:48:00,938 - INFO - 执行详细分析: callId=8730
2025-07-08 19:48:00,954 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\17-8730.txt
2025-07-08 19:48:00,954 - INFO - 处理 callId=7981, callTime=14.52625ms, rank=18
2025-07-08 19:48:00,954 - INFO - 执行详细分析: callId=7981
2025-07-08 19:48:00,969 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\18-7981.txt
2025-07-08 19:48:00,969 - INFO - 处理 callId=9067, callTime=14.466875ms, rank=19
2025-07-08 19:48:00,970 - INFO - 执行详细分析: callId=9067
2025-07-08 19:48:00,987 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\19-9067.txt
2025-07-08 19:48:00,987 - INFO - 处理 callId=9898, callTime=14.129843ms, rank=20
2025-07-08 19:48:00,987 - INFO - 执行详细分析: callId=9898
2025-07-08 19:48:01,003 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\20-9898.txt
2025-07-08 19:48:01,003 - INFO - 处理 callId=10197, callTime=14.001406ms, rank=21
2025-07-08 19:48:01,004 - INFO - 执行详细分析: callId=10197
2025-07-08 19:48:01,020 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\21-10197.txt
2025-07-08 19:48:01,021 - INFO - 处理 callId=7566, callTime=13.944218ms, rank=22
2025-07-08 19:48:01,021 - INFO - 执行详细分析: callId=7566
2025-07-08 19:48:01,036 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\22-7566.txt
2025-07-08 19:48:01,036 - INFO - 处理 callId=8435, callTime=13.844688ms, rank=23
2025-07-08 19:48:01,036 - INFO - 执行详细分析: callId=8435
2025-07-08 19:48:01,052 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\23-8435.txt
2025-07-08 19:48:01,052 - INFO - 处理 callId=10532, callTime=13.82ms, rank=24
2025-07-08 19:48:01,052 - INFO - 执行详细分析: callId=10532
2025-07-08 19:48:01,070 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\24-10532.txt
2025-07-08 19:48:01,070 - INFO - 处理 callId=8454, callTime=13.794531ms, rank=25
2025-07-08 19:48:01,070 - INFO - 执行详细分析: callId=8454
2025-07-08 19:48:01,086 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\25-8454.txt
2025-07-08 19:48:01,086 - INFO - 处理 callId=10389, callTime=13.703594ms, rank=26
2025-07-08 19:48:01,086 - INFO - 执行详细分析: callId=10389
2025-07-08 19:48:01,104 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\26-10389.txt
2025-07-08 19:48:01,104 - INFO - 处理 callId=9519, callTime=13.695ms, rank=27
2025-07-08 19:48:01,104 - INFO - 执行详细分析: callId=9519
2025-07-08 19:48:01,121 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\27-9519.txt
2025-07-08 19:48:01,122 - INFO - 处理 callId=10496, callTime=13.678438ms, rank=28
2025-07-08 19:48:01,122 - INFO - 执行详细分析: callId=10496
2025-07-08 19:48:01,139 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\28-10496.txt
2025-07-08 19:48:01,140 - INFO - 处理 callId=9511, callTime=13.582813ms, rank=29
2025-07-08 19:48:01,140 - INFO - 执行详细分析: callId=9511
2025-07-08 19:48:01,156 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\29-9511.txt
2025-07-08 19:48:01,156 - INFO - 处理 callId=8069, callTime=13.560156ms, rank=30
2025-07-08 19:48:01,156 - INFO - 执行详细分析: callId=8069
2025-07-08 19:48:01,172 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\30-8069.txt
2025-07-08 19:48:01,172 - INFO - 处理 callId=9075, callTime=13.474375ms, rank=31
2025-07-08 19:48:01,173 - INFO - 执行详细分析: callId=9075
2025-07-08 19:48:01,189 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\31-9075.txt
2025-07-08 19:48:01,189 - INFO - 处理 callId=9019, callTime=13.418282ms, rank=32
2025-07-08 19:48:01,189 - INFO - 执行详细分析: callId=9019
2025-07-08 19:48:01,204 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\32-9019.txt
2025-07-08 19:48:01,204 - INFO - 处理 callId=9902, callTime=13.3975ms, rank=33
2025-07-08 19:48:01,204 - INFO - 执行详细分析: callId=9902
2025-07-08 19:48:01,221 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\33-9902.txt
2025-07-08 19:48:01,221 - INFO - 处理 callId=9071, callTime=13.370625ms, rank=34
2025-07-08 19:48:01,221 - INFO - 执行详细分析: callId=9071
2025-07-08 19:48:01,237 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\34-9071.txt
2025-07-08 19:48:01,238 - INFO - 处理 callId=10141, callTime=13.342812ms, rank=35
2025-07-08 19:48:01,238 - INFO - 执行详细分析: callId=10141
2025-07-08 19:48:01,254 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\35-10141.txt
2025-07-08 19:48:01,255 - INFO - 处理 callId=10145, callTime=13.342344ms, rank=36
2025-07-08 19:48:01,255 - INFO - 执行详细分析: callId=10145
2025-07-08 19:48:01,272 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\36-10145.txt
2025-07-08 19:48:01,272 - INFO - 处理 callId=10133, callTime=13.277031ms, rank=37
2025-07-08 19:48:01,272 - INFO - 执行详细分析: callId=10133
2025-07-08 19:48:01,289 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\37-10133.txt
2025-07-08 19:48:01,289 - INFO - 处理 callId=10229, callTime=13.245781ms, rank=38
2025-07-08 19:48:01,289 - INFO - 执行详细分析: callId=10229
2025-07-08 19:48:01,306 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\38-10229.txt
2025-07-08 19:48:01,306 - INFO - 处理 callId=9023, callTime=13.239688ms, rank=39
2025-07-08 19:48:01,306 - INFO - 执行详细分析: callId=9023
2025-07-08 19:48:01,322 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\39-9023.txt
2025-07-08 19:48:01,322 - INFO - 处理 callId=8901, callTime=13.215156ms, rank=40
2025-07-08 19:48:01,322 - INFO - 执行详细分析: callId=8901
2025-07-08 19:48:01,339 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161420_wps滚动文档\40-8901.txt
2025-07-08 19:48:01,339 - INFO - 完成处理文件: perfmon-raster-20250707_161420_wps滚动文档.dat
2025-07-08 19:48:01,339 - INFO - 处理进度: 26/30
2025-07-08 19:48:01,339 - INFO - 开始处理文件: perfmon-raster-20250707_161533_wps缩放文档.dat
2025-07-08 19:48:01,339 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon-raster-20250707_161533_wps缩放文档.dat
2025-07-08 19:48:01,371 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon-raster-20250707_161533_wps缩放文档.dat.txt
2025-07-08 19:48:01,372 - INFO - 找到event函数: E:\wpsskia\qt\source\qtbase\src\widgets\kernel\qwidget.cpp:9682 event
2025-07-08 19:48:01,372 - INFO - 提取callId: 6543, callTime: 124.294687ms
2025-07-08 19:48:01,372 - INFO - 提取callId: 6742, callTime: 123.20875ms
2025-07-08 19:48:01,372 - INFO - 提取callId: 6746, callTime: 121.937813ms
2025-07-08 19:48:01,372 - INFO - 提取callId: 8386, callTime: 119.441094ms
2025-07-08 19:48:01,372 - INFO - 提取callId: 6754, callTime: 119.121406ms
2025-07-08 19:48:01,372 - INFO - 提取callId: 6750, callTime: 118.21875ms
2025-07-08 19:48:01,372 - INFO - 提取callId: 7910, callTime: 118.119844ms
2025-07-08 19:48:01,372 - INFO - 提取callId: 8398, callTime: 115.611719ms
2025-07-08 19:48:01,372 - INFO - 提取callId: 8402, callTime: 115.163281ms
2025-07-08 19:48:01,372 - INFO - 提取callId: 7914, callTime: 115.1625ms
2025-07-08 19:48:01,373 - INFO - 提取callId: 8394, callTime: 115.12625ms
2025-07-08 19:48:01,373 - INFO - 提取callId: 8406, callTime: 114.860469ms
2025-07-08 19:48:01,373 - INFO - 提取callId: 7926, callTime: 114.774063ms
2025-07-08 19:48:01,373 - INFO - 提取callId: 7922, callTime: 114.66625ms
2025-07-08 19:48:01,373 - INFO - 提取callId: 8390, callTime: 114.225ms
2025-07-08 19:48:01,373 - INFO - 提取callId: 7918, callTime: 114.03ms
2025-07-08 19:48:01,373 - INFO - 提取callId: 6547, callTime: 94.828906ms
2025-07-08 19:48:01,373 - INFO - 提取callId: 6595, callTime: 88.405781ms
2025-07-08 19:48:01,373 - INFO - 提取callId: 6727, callTime: 87.218125ms
2025-07-08 19:48:01,373 - INFO - 提取callId: 8338, callTime: 63.540782ms
2025-07-08 19:48:01,373 - INFO - 提取callId: 6758, callTime: 58.021562ms
2025-07-08 19:48:01,373 - INFO - 提取callId: 7874, callTime: 57.287656ms
2025-07-08 19:48:01,373 - INFO - 提取callId: 8318, callTime: 33.174531ms
2025-07-08 19:48:01,373 - INFO - 提取callId: 6762, callTime: 31.607813ms
2025-07-08 19:48:01,374 - INFO - 提取callId: 7870, callTime: 31.196875ms
2025-07-08 19:48:01,374 - INFO - 提取callId: 7930, callTime: 30.995ms
2025-07-08 19:48:01,374 - INFO - 提取callId: 8174, callTime: 30.357031ms
2025-07-08 19:48:01,374 - INFO - 提取callId: 8298, callTime: 27.485625ms
2025-07-08 19:48:01,374 - INFO - 提取callId: 4762, callTime: 26.09625ms
2025-07-08 19:48:01,374 - INFO - 提取callId: 7866, callTime: 25.883906ms
2025-07-08 19:48:01,374 - INFO - 提取callId: 6766, callTime: 25.517968ms
2025-07-08 19:48:01,374 - INFO - 提取callId: 7934, callTime: 25.117187ms
2025-07-08 19:48:01,374 - INFO - 提取callId: 14, callTime: 24.7025ms
2025-07-08 19:48:01,374 - INFO - 提取callId: 5933, callTime: 23.242344ms
2025-07-08 19:48:01,374 - INFO - 提取callId: 8230, callTime: 21.69375ms
2025-07-08 19:48:01,374 - INFO - 提取callId: 8274, callTime: 21.649687ms
2025-07-08 19:48:01,374 - INFO - 提取callId: 7998, callTime: 21.611875ms
2025-07-08 19:48:01,375 - INFO - 提取callId: 7722, callTime: 20.942813ms
2025-07-08 19:48:01,375 - INFO - 提取callId: 4345, callTime: 20.885938ms
2025-07-08 19:48:01,375 - INFO - 提取callId: 6770, callTime: 19.92875ms
2025-07-08 19:48:01,375 - INFO - 提取到 40 个event相关的callId
2025-07-08 19:48:01,375 - INFO - 处理 callId=6543, callTime=124.294687ms, rank=1
2025-07-08 19:48:01,375 - INFO - 执行详细分析: callId=6543
2025-07-08 19:48:01,390 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\1-6543.txt
2025-07-08 19:48:01,391 - INFO - 处理 callId=6742, callTime=123.20875ms, rank=2
2025-07-08 19:48:01,391 - INFO - 执行详细分析: callId=6742
2025-07-08 19:48:01,406 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\2-6742.txt
2025-07-08 19:48:01,407 - INFO - 处理 callId=6746, callTime=121.937813ms, rank=3
2025-07-08 19:48:01,407 - INFO - 执行详细分析: callId=6746
2025-07-08 19:48:01,421 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\3-6746.txt
2025-07-08 19:48:01,421 - INFO - 处理 callId=8386, callTime=119.441094ms, rank=4
2025-07-08 19:48:01,422 - INFO - 执行详细分析: callId=8386
2025-07-08 19:48:01,437 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\4-8386.txt
2025-07-08 19:48:01,438 - INFO - 处理 callId=6754, callTime=119.121406ms, rank=5
2025-07-08 19:48:01,438 - INFO - 执行详细分析: callId=6754
2025-07-08 19:48:01,453 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\5-6754.txt
2025-07-08 19:48:01,453 - INFO - 处理 callId=6750, callTime=118.21875ms, rank=6
2025-07-08 19:48:01,453 - INFO - 执行详细分析: callId=6750
2025-07-08 19:48:01,468 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\6-6750.txt
2025-07-08 19:48:01,468 - INFO - 处理 callId=7910, callTime=118.119844ms, rank=7
2025-07-08 19:48:01,468 - INFO - 执行详细分析: callId=7910
2025-07-08 19:48:01,484 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\7-7910.txt
2025-07-08 19:48:01,484 - INFO - 处理 callId=8398, callTime=115.611719ms, rank=8
2025-07-08 19:48:01,485 - INFO - 执行详细分析: callId=8398
2025-07-08 19:48:01,501 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\8-8398.txt
2025-07-08 19:48:01,502 - INFO - 处理 callId=8402, callTime=115.163281ms, rank=9
2025-07-08 19:48:01,502 - INFO - 执行详细分析: callId=8402
2025-07-08 19:48:01,518 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\9-8402.txt
2025-07-08 19:48:01,518 - INFO - 处理 callId=7914, callTime=115.1625ms, rank=10
2025-07-08 19:48:01,518 - INFO - 执行详细分析: callId=7914
2025-07-08 19:48:01,542 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\10-7914.txt
2025-07-08 19:48:01,542 - INFO - 处理 callId=8394, callTime=115.12625ms, rank=11
2025-07-08 19:48:01,542 - INFO - 执行详细分析: callId=8394
2025-07-08 19:48:01,558 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\11-8394.txt
2025-07-08 19:48:01,558 - INFO - 处理 callId=8406, callTime=114.860469ms, rank=12
2025-07-08 19:48:01,558 - INFO - 执行详细分析: callId=8406
2025-07-08 19:48:01,574 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\12-8406.txt
2025-07-08 19:48:01,574 - INFO - 处理 callId=7926, callTime=114.774063ms, rank=13
2025-07-08 19:48:01,574 - INFO - 执行详细分析: callId=7926
2025-07-08 19:48:01,590 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\13-7926.txt
2025-07-08 19:48:01,590 - INFO - 处理 callId=7922, callTime=114.66625ms, rank=14
2025-07-08 19:48:01,590 - INFO - 执行详细分析: callId=7922
2025-07-08 19:48:01,605 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\14-7922.txt
2025-07-08 19:48:01,605 - INFO - 处理 callId=8390, callTime=114.225ms, rank=15
2025-07-08 19:48:01,606 - INFO - 执行详细分析: callId=8390
2025-07-08 19:48:01,621 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\15-8390.txt
2025-07-08 19:48:01,621 - INFO - 处理 callId=7918, callTime=114.03ms, rank=16
2025-07-08 19:48:01,621 - INFO - 执行详细分析: callId=7918
2025-07-08 19:48:01,636 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\16-7918.txt
2025-07-08 19:48:01,637 - INFO - 处理 callId=6547, callTime=94.828906ms, rank=17
2025-07-08 19:48:01,637 - INFO - 执行详细分析: callId=6547
2025-07-08 19:48:01,651 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\17-6547.txt
2025-07-08 19:48:01,651 - INFO - 处理 callId=6595, callTime=88.405781ms, rank=18
2025-07-08 19:48:01,651 - INFO - 执行详细分析: callId=6595
2025-07-08 19:48:01,665 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\18-6595.txt
2025-07-08 19:48:01,665 - INFO - 处理 callId=6727, callTime=87.218125ms, rank=19
2025-07-08 19:48:01,666 - INFO - 执行详细分析: callId=6727
2025-07-08 19:48:01,680 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\19-6727.txt
2025-07-08 19:48:01,680 - INFO - 处理 callId=8338, callTime=63.540782ms, rank=20
2025-07-08 19:48:01,680 - INFO - 执行详细分析: callId=8338
2025-07-08 19:48:01,696 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\20-8338.txt
2025-07-08 19:48:01,696 - INFO - 处理 callId=6758, callTime=58.021562ms, rank=21
2025-07-08 19:48:01,696 - INFO - 执行详细分析: callId=6758
2025-07-08 19:48:01,712 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\21-6758.txt
2025-07-08 19:48:01,712 - INFO - 处理 callId=7874, callTime=57.287656ms, rank=22
2025-07-08 19:48:01,712 - INFO - 执行详细分析: callId=7874
2025-07-08 19:48:01,728 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\22-7874.txt
2025-07-08 19:48:01,728 - INFO - 处理 callId=8318, callTime=33.174531ms, rank=23
2025-07-08 19:48:01,728 - INFO - 执行详细分析: callId=8318
2025-07-08 19:48:01,745 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\23-8318.txt
2025-07-08 19:48:01,745 - INFO - 处理 callId=6762, callTime=31.607813ms, rank=24
2025-07-08 19:48:01,745 - INFO - 执行详细分析: callId=6762
2025-07-08 19:48:01,761 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\24-6762.txt
2025-07-08 19:48:01,761 - INFO - 处理 callId=7870, callTime=31.196875ms, rank=25
2025-07-08 19:48:01,761 - INFO - 执行详细分析: callId=7870
2025-07-08 19:48:01,779 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\25-7870.txt
2025-07-08 19:48:01,779 - INFO - 处理 callId=7930, callTime=30.995ms, rank=26
2025-07-08 19:48:01,780 - INFO - 执行详细分析: callId=7930
2025-07-08 19:48:01,797 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\26-7930.txt
2025-07-08 19:48:01,797 - INFO - 处理 callId=8174, callTime=30.357031ms, rank=27
2025-07-08 19:48:01,798 - INFO - 执行详细分析: callId=8174
2025-07-08 19:48:01,814 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\27-8174.txt
2025-07-08 19:48:01,815 - INFO - 处理 callId=8298, callTime=27.485625ms, rank=28
2025-07-08 19:48:01,815 - INFO - 执行详细分析: callId=8298
2025-07-08 19:48:01,832 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\28-8298.txt
2025-07-08 19:48:01,832 - INFO - 处理 callId=4762, callTime=26.09625ms, rank=29
2025-07-08 19:48:01,832 - INFO - 执行详细分析: callId=4762
2025-07-08 19:48:01,852 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\29-4762.txt
2025-07-08 19:48:01,853 - INFO - 处理 callId=7866, callTime=25.883906ms, rank=30
2025-07-08 19:48:01,853 - INFO - 执行详细分析: callId=7866
2025-07-08 19:48:01,869 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\30-7866.txt
2025-07-08 19:48:01,869 - INFO - 处理 callId=6766, callTime=25.517968ms, rank=31
2025-07-08 19:48:01,869 - INFO - 执行详细分析: callId=6766
2025-07-08 19:48:01,886 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\31-6766.txt
2025-07-08 19:48:01,886 - INFO - 处理 callId=7934, callTime=25.117187ms, rank=32
2025-07-08 19:48:01,886 - INFO - 执行详细分析: callId=7934
2025-07-08 19:48:01,902 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\32-7934.txt
2025-07-08 19:48:01,902 - INFO - 处理 callId=14, callTime=24.7025ms, rank=33
2025-07-08 19:48:01,902 - INFO - 执行详细分析: callId=14
2025-07-08 19:48:01,918 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\33-14.txt
2025-07-08 19:48:01,919 - INFO - 处理 callId=5933, callTime=23.242344ms, rank=34
2025-07-08 19:48:01,919 - INFO - 执行详细分析: callId=5933
2025-07-08 19:48:01,942 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\34-5933.txt
2025-07-08 19:48:01,942 - INFO - 处理 callId=8230, callTime=21.69375ms, rank=35
2025-07-08 19:48:01,942 - INFO - 执行详细分析: callId=8230
2025-07-08 19:48:01,958 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\35-8230.txt
2025-07-08 19:48:01,958 - INFO - 处理 callId=8274, callTime=21.649687ms, rank=36
2025-07-08 19:48:01,958 - INFO - 执行详细分析: callId=8274
2025-07-08 19:48:01,975 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\36-8274.txt
2025-07-08 19:48:01,975 - INFO - 处理 callId=7998, callTime=21.611875ms, rank=37
2025-07-08 19:48:01,975 - INFO - 执行详细分析: callId=7998
2025-07-08 19:48:01,993 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\37-7998.txt
2025-07-08 19:48:01,993 - INFO - 处理 callId=7722, callTime=20.942813ms, rank=38
2025-07-08 19:48:01,993 - INFO - 执行详细分析: callId=7722
2025-07-08 19:48:02,009 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\38-7722.txt
2025-07-08 19:48:02,010 - INFO - 处理 callId=4345, callTime=20.885938ms, rank=39
2025-07-08 19:48:02,010 - INFO - 执行详细分析: callId=4345
2025-07-08 19:48:02,030 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\39-4345.txt
2025-07-08 19:48:02,030 - INFO - 处理 callId=6770, callTime=19.92875ms, rank=40
2025-07-08 19:48:02,030 - INFO - 执行详细分析: callId=6770
2025-07-08 19:48:02,045 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon-raster-20250707_161533_wps缩放文档\40-6770.txt
2025-07-08 19:48:02,045 - INFO - 完成处理文件: perfmon-raster-20250707_161533_wps缩放文档.dat
2025-07-08 19:48:02,045 - INFO - 处理进度: 27/30
2025-07-08 19:48:02,045 - INFO - 开始处理文件: perfmon首页angle.dat
2025-07-08 19:48:02,046 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon首页angle.dat
2025-07-08 19:48:02,066 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon首页angle.dat.txt
2025-07-08 19:48:02,066 - INFO - 找到event函数: E:\wpsskia\qt\source\qtbase\src\widgets\kernel\qwidget.cpp:9682 event
2025-07-08 19:48:02,066 - INFO - 提取callId: 1652, callTime: 29.603594ms
2025-07-08 19:48:02,066 - INFO - 提取callId: 7611, callTime: 19.396407ms
2025-07-08 19:48:02,066 - INFO - 提取callId: 3194, callTime: 18.933281ms
2025-07-08 19:48:02,066 - INFO - 提取callId: 5638, callTime: 10.284688ms
2025-07-08 19:48:02,067 - INFO - 提取callId: 11048, callTime: 6.920782ms
2025-07-08 19:48:02,067 - INFO - 提取callId: 8932, callTime: 6.484844ms
2025-07-08 19:48:02,067 - INFO - 提取callId: 10100, callTime: 4.2475ms
2025-07-08 19:48:02,067 - INFO - 提取callId: 8779, callTime: 2.310156ms
2025-07-08 19:48:02,067 - INFO - 提取callId: 47, callTime: 0.002188ms
2025-07-08 19:48:02,067 - INFO - 提取callId: 50, callTime: 0.000938ms
2025-07-08 19:48:02,067 - INFO - 提取到 10 个event相关的callId
2025-07-08 19:48:02,067 - INFO - 处理 callId=1652, callTime=29.603594ms, rank=1
2025-07-08 19:48:02,067 - INFO - 执行详细分析: callId=1652
2025-07-08 19:48:02,112 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页angle\1-1652.txt
2025-07-08 19:48:02,112 - INFO - 处理 callId=7611, callTime=19.396407ms, rank=2
2025-07-08 19:48:02,112 - INFO - 执行详细分析: callId=7611
2025-07-08 19:48:02,148 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页angle\2-7611.txt
2025-07-08 19:48:02,148 - INFO - 处理 callId=3194, callTime=18.933281ms, rank=3
2025-07-08 19:48:02,149 - INFO - 执行详细分析: callId=3194
2025-07-08 19:48:02,218 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页angle\3-3194.txt
2025-07-08 19:48:02,218 - INFO - 处理 callId=5638, callTime=10.284688ms, rank=4
2025-07-08 19:48:02,219 - INFO - 执行详细分析: callId=5638
2025-07-08 19:48:02,284 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页angle\4-5638.txt
2025-07-08 19:48:02,284 - INFO - 处理 callId=11048, callTime=6.920782ms, rank=5
2025-07-08 19:48:02,284 - INFO - 执行详细分析: callId=11048
2025-07-08 19:48:02,317 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页angle\5-11048.txt
2025-07-08 19:48:02,318 - INFO - 处理 callId=8932, callTime=6.484844ms, rank=6
2025-07-08 19:48:02,318 - INFO - 执行详细分析: callId=8932
2025-07-08 19:48:02,355 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页angle\6-8932.txt
2025-07-08 19:48:02,355 - INFO - 处理 callId=10100, callTime=4.2475ms, rank=7
2025-07-08 19:48:02,356 - INFO - 执行详细分析: callId=10100
2025-07-08 19:48:02,395 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页angle\7-10100.txt
2025-07-08 19:48:02,395 - INFO - 处理 callId=8779, callTime=2.310156ms, rank=8
2025-07-08 19:48:02,395 - INFO - 执行详细分析: callId=8779
2025-07-08 19:48:02,412 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页angle\8-8779.txt
2025-07-08 19:48:02,412 - INFO - 处理 callId=47, callTime=0.002188ms, rank=9
2025-07-08 19:48:02,412 - INFO - 执行详细分析: callId=47
2025-07-08 19:48:02,421 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页angle\9-47.txt
2025-07-08 19:48:02,421 - INFO - 处理 callId=50, callTime=0.000938ms, rank=10
2025-07-08 19:48:02,421 - INFO - 执行详细分析: callId=50
2025-07-08 19:48:02,430 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页angle\10-50.txt
2025-07-08 19:48:02,430 - INFO - 完成处理文件: perfmon首页angle.dat
2025-07-08 19:48:02,430 - INFO - 处理进度: 28/30
2025-07-08 19:48:02,430 - INFO - 开始处理文件: perfmon首页raster.dat
2025-07-08 19:48:02,430 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon首页raster.dat
2025-07-08 19:48:02,441 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon首页raster.dat.txt
2025-07-08 19:48:02,441 - INFO - 找到event函数: E:\wpsskia\qt\source\qtbase\src\widgets\kernel\qwidget.cpp:9682 event
2025-07-08 19:48:02,441 - INFO - 提取callId: 14, callTime: 28.312969ms
2025-07-08 19:48:02,441 - INFO - 提取callId: 452, callTime: 18.853594ms
2025-07-08 19:48:02,441 - INFO - 提取callId: 892, callTime: 14.38125ms
2025-07-08 19:48:02,442 - INFO - 提取callId: 1743, callTime: 10.775469ms
2025-07-08 19:48:02,442 - INFO - 提取callId: 1309, callTime: 10.519219ms
2025-07-08 19:48:02,442 - INFO - 提取callId: 1730, callTime: 9.310781ms
2025-07-08 19:48:02,442 - INFO - 提取callId: 1726, callTime: 6.999844ms
2025-07-08 19:48:02,442 - INFO - 提取callId: 1734, callTime: 2.575938ms
2025-07-08 19:48:02,442 - INFO - 提取callId: 2, callTime: 0.002188ms
2025-07-08 19:48:02,442 - INFO - 提取callId: 5, callTime: 0.00125ms
2025-07-08 19:48:02,442 - INFO - 提取到 10 个event相关的callId
2025-07-08 19:48:02,442 - INFO - 处理 callId=14, callTime=28.312969ms, rank=1
2025-07-08 19:48:02,442 - INFO - 执行详细分析: callId=14
2025-07-08 19:48:02,458 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页raster\1-14.txt
2025-07-08 19:48:02,458 - INFO - 处理 callId=452, callTime=18.853594ms, rank=2
2025-07-08 19:48:02,458 - INFO - 执行详细分析: callId=452
2025-07-08 19:48:02,473 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页raster\2-452.txt
2025-07-08 19:48:02,474 - INFO - 处理 callId=892, callTime=14.38125ms, rank=3
2025-07-08 19:48:02,474 - INFO - 执行详细分析: callId=892
2025-07-08 19:48:02,489 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页raster\3-892.txt
2025-07-08 19:48:02,490 - INFO - 处理 callId=1743, callTime=10.775469ms, rank=4
2025-07-08 19:48:02,490 - INFO - 执行详细分析: callId=1743
2025-07-08 19:48:02,506 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页raster\4-1743.txt
2025-07-08 19:48:02,506 - INFO - 处理 callId=1309, callTime=10.519219ms, rank=5
2025-07-08 19:48:02,506 - INFO - 执行详细分析: callId=1309
2025-07-08 19:48:02,522 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页raster\5-1309.txt
2025-07-08 19:48:02,523 - INFO - 处理 callId=1730, callTime=9.310781ms, rank=6
2025-07-08 19:48:02,523 - INFO - 执行详细分析: callId=1730
2025-07-08 19:48:02,533 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页raster\6-1730.txt
2025-07-08 19:48:02,533 - INFO - 处理 callId=1726, callTime=6.999844ms, rank=7
2025-07-08 19:48:02,533 - INFO - 执行详细分析: callId=1726
2025-07-08 19:48:02,543 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页raster\7-1726.txt
2025-07-08 19:48:02,543 - INFO - 处理 callId=1734, callTime=2.575938ms, rank=8
2025-07-08 19:48:02,543 - INFO - 执行详细分析: callId=1734
2025-07-08 19:48:02,563 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页raster\8-1734.txt
2025-07-08 19:48:02,563 - INFO - 处理 callId=2, callTime=0.002188ms, rank=9
2025-07-08 19:48:02,563 - INFO - 执行详细分析: callId=2
2025-07-08 19:48:02,572 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页raster\9-2.txt
2025-07-08 19:48:02,572 - INFO - 处理 callId=5, callTime=0.00125ms, rank=10
2025-07-08 19:48:02,572 - INFO - 执行详细分析: callId=5
2025-07-08 19:48:02,581 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页raster\10-5.txt
2025-07-08 19:48:02,581 - INFO - 完成处理文件: perfmon首页raster.dat
2025-07-08 19:48:02,581 - INFO - 处理进度: 29/30
2025-07-08 19:48:02,581 - INFO - 开始处理文件: perfmon首页热启动angle.dat
2025-07-08 19:48:02,581 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon首页热启动angle.dat
2025-07-08 19:48:02,631 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon首页热启动angle.dat.txt
2025-07-08 19:48:02,631 - INFO - 找到event函数: E:\wpsskia\qt\source\qtbase\src\widgets\kernel\qwidget.cpp:9682 event
2025-07-08 19:48:02,631 - INFO - 提取callId: 1652, callTime: 27.532656ms
2025-07-08 19:48:02,631 - INFO - 提取callId: 3194, callTime: 19.805938ms
2025-07-08 19:48:02,631 - INFO - 提取callId: 36635, callTime: 16.836719ms
2025-07-08 19:48:02,631 - INFO - 提取callId: 6983, callTime: 13.34875ms
2025-07-08 19:48:02,632 - INFO - 提取callId: 11225, callTime: 12.137032ms
2025-07-08 19:48:02,632 - INFO - 提取callId: 16791, callTime: 9.78625ms
2025-07-08 19:48:02,632 - INFO - 提取callId: 39738, callTime: 9.242656ms
2025-07-08 19:48:02,632 - INFO - 提取callId: 5635, callTime: 8.707657ms
2025-07-08 19:48:02,632 - INFO - 提取callId: 18841, callTime: 8.673281ms
2025-07-08 19:48:02,632 - INFO - 提取callId: 27833, callTime: 7.42375ms
2025-07-08 19:48:02,632 - INFO - 提取callId: 20990, callTime: 7.07625ms
2025-07-08 19:48:02,632 - INFO - 提取callId: 30637, callTime: 7.015ms
2025-07-08 19:48:02,632 - INFO - 提取callId: 39140, callTime: 7.011875ms
2025-07-08 19:48:02,633 - INFO - 提取callId: 36327, callTime: 6.590938ms
2025-07-08 19:48:02,633 - INFO - 提取callId: 34711, callTime: 6.550156ms
2025-07-08 19:48:02,633 - INFO - 提取callId: 35481, callTime: 6.431719ms
2025-07-08 19:48:02,633 - INFO - 提取callId: 35865, callTime: 6.106406ms
2025-07-08 19:48:02,633 - INFO - 提取callId: 36481, callTime: 6.023125ms
2025-07-08 19:48:02,633 - INFO - 提取callId: 34403, callTime: 5.942187ms
2025-07-08 19:48:02,633 - INFO - 提取callId: 35019, callTime: 5.937656ms
2025-07-08 19:48:02,633 - INFO - 提取callId: 29707, callTime: 5.8525ms
2025-07-08 19:48:02,633 - INFO - 提取callId: 35173, callTime: 5.735625ms
2025-07-08 19:48:02,634 - INFO - 提取callId: 10057, callTime: 5.269688ms
2025-07-08 19:48:02,634 - INFO - 提取callId: 30831, callTime: 5.253907ms
2025-07-08 19:48:02,634 - INFO - 提取callId: 33403, callTime: 5.226093ms
2025-07-08 19:48:02,634 - INFO - 提取callId: 32633, callTime: 5.221094ms
2025-07-08 19:48:02,634 - INFO - 提取callId: 31555, callTime: 5.193906ms
2025-07-08 19:48:02,634 - INFO - 提取callId: 28264, callTime: 5.188125ms
2025-07-08 19:48:02,634 - INFO - 提取callId: 34095, callTime: 5.160625ms
2025-07-08 19:48:02,634 - INFO - 提取callId: 23032, callTime: 5.126406ms
2025-07-08 19:48:02,634 - INFO - 提取callId: 32325, callTime: 5.086562ms
2025-07-08 19:48:02,635 - INFO - 提取callId: 32171, callTime: 5.019844ms
2025-07-08 19:48:02,635 - INFO - 提取callId: 34249, callTime: 4.977344ms
2025-07-08 19:48:02,635 - INFO - 提取callId: 33787, callTime: 4.95ms
2025-07-08 19:48:02,635 - INFO - 提取callId: 34557, callTime: 4.92125ms
2025-07-08 19:48:02,635 - INFO - 提取callId: 9109, callTime: 4.917344ms
2025-07-08 19:48:02,635 - INFO - 提取callId: 22424, callTime: 4.895781ms
2025-07-08 19:48:02,635 - INFO - 提取callId: 31863, callTime: 4.824531ms
2025-07-08 19:48:02,635 - INFO - 提取callId: 33095, callTime: 4.805312ms
2025-07-08 19:48:02,635 - INFO - 提取callId: 33249, callTime: 4.777656ms
2025-07-08 19:48:02,635 - INFO - 提取到 40 个event相关的callId
2025-07-08 19:48:02,635 - INFO - 处理 callId=1652, callTime=27.532656ms, rank=1
2025-07-08 19:48:02,635 - INFO - 执行详细分析: callId=1652
2025-07-08 19:48:02,680 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\1-1652.txt
2025-07-08 19:48:02,680 - INFO - 处理 callId=3194, callTime=19.805938ms, rank=2
2025-07-08 19:48:02,680 - INFO - 执行详细分析: callId=3194
2025-07-08 19:48:02,749 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\2-3194.txt
2025-07-08 19:48:02,749 - INFO - 处理 callId=36635, callTime=16.836719ms, rank=3
2025-07-08 19:48:02,749 - INFO - 执行详细分析: callId=36635
2025-07-08 19:48:02,880 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\3-36635.txt
2025-07-08 19:48:02,880 - INFO - 处理 callId=6983, callTime=13.34875ms, rank=4
2025-07-08 19:48:02,881 - INFO - 执行详细分析: callId=6983
2025-07-08 19:48:02,947 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\4-6983.txt
2025-07-08 19:48:02,947 - INFO - 处理 callId=11225, callTime=12.137032ms, rank=5
2025-07-08 19:48:02,947 - INFO - 执行详细分析: callId=11225
2025-07-08 19:48:02,981 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\5-11225.txt
2025-07-08 19:48:02,981 - INFO - 处理 callId=16791, callTime=9.78625ms, rank=6
2025-07-08 19:48:02,981 - INFO - 执行详细分析: callId=16791
2025-07-08 19:48:03,070 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\6-16791.txt
2025-07-08 19:48:03,070 - INFO - 处理 callId=39738, callTime=9.242656ms, rank=7
2025-07-08 19:48:03,070 - INFO - 执行详细分析: callId=39738
2025-07-08 19:48:03,203 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\7-39738.txt
2025-07-08 19:48:03,203 - INFO - 处理 callId=5635, callTime=8.707657ms, rank=8
2025-07-08 19:48:03,203 - INFO - 执行详细分析: callId=5635
2025-07-08 19:48:03,241 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\8-5635.txt
2025-07-08 19:48:03,242 - INFO - 处理 callId=18841, callTime=8.673281ms, rank=9
2025-07-08 19:48:03,242 - INFO - 执行详细分析: callId=18841
2025-07-08 19:48:03,333 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\9-18841.txt
2025-07-08 19:48:03,333 - INFO - 处理 callId=27833, callTime=7.42375ms, rank=10
2025-07-08 19:48:03,333 - INFO - 执行详细分析: callId=27833
2025-07-08 19:48:03,372 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\10-27833.txt
2025-07-08 19:48:03,372 - INFO - 处理 callId=20990, callTime=7.07625ms, rank=11
2025-07-08 19:48:03,372 - INFO - 执行详细分析: callId=20990
2025-07-08 19:48:03,405 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\11-20990.txt
2025-07-08 19:48:03,405 - INFO - 处理 callId=30637, callTime=7.015ms, rank=12
2025-07-08 19:48:03,405 - INFO - 执行详细分析: callId=30637
2025-07-08 19:48:03,440 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\12-30637.txt
2025-07-08 19:48:03,440 - INFO - 处理 callId=39140, callTime=7.011875ms, rank=13
2025-07-08 19:48:03,440 - INFO - 执行详细分析: callId=39140
2025-07-08 19:48:03,481 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\13-39140.txt
2025-07-08 19:48:03,482 - INFO - 处理 callId=36327, callTime=6.590938ms, rank=14
2025-07-08 19:48:03,482 - INFO - 执行详细分析: callId=36327
2025-07-08 19:48:03,520 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\14-36327.txt
2025-07-08 19:48:03,521 - INFO - 处理 callId=34711, callTime=6.550156ms, rank=15
2025-07-08 19:48:03,521 - INFO - 执行详细分析: callId=34711
2025-07-08 19:48:03,558 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\15-34711.txt
2025-07-08 19:48:03,559 - INFO - 处理 callId=35481, callTime=6.431719ms, rank=16
2025-07-08 19:48:03,559 - INFO - 执行详细分析: callId=35481
2025-07-08 19:48:03,596 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\16-35481.txt
2025-07-08 19:48:03,597 - INFO - 处理 callId=35865, callTime=6.106406ms, rank=17
2025-07-08 19:48:03,597 - INFO - 执行详细分析: callId=35865
2025-07-08 19:48:03,634 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\17-35865.txt
2025-07-08 19:48:03,635 - INFO - 处理 callId=36481, callTime=6.023125ms, rank=18
2025-07-08 19:48:03,635 - INFO - 执行详细分析: callId=36481
2025-07-08 19:48:03,673 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\18-36481.txt
2025-07-08 19:48:03,673 - INFO - 处理 callId=34403, callTime=5.942187ms, rank=19
2025-07-08 19:48:03,673 - INFO - 执行详细分析: callId=34403
2025-07-08 19:48:03,710 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\19-34403.txt
2025-07-08 19:48:03,710 - INFO - 处理 callId=35019, callTime=5.937656ms, rank=20
2025-07-08 19:48:03,710 - INFO - 执行详细分析: callId=35019
2025-07-08 19:48:03,748 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\20-35019.txt
2025-07-08 19:48:03,748 - INFO - 处理 callId=29707, callTime=5.8525ms, rank=21
2025-07-08 19:48:03,748 - INFO - 执行详细分析: callId=29707
2025-07-08 19:48:03,781 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\21-29707.txt
2025-07-08 19:48:03,781 - INFO - 处理 callId=35173, callTime=5.735625ms, rank=22
2025-07-08 19:48:03,782 - INFO - 执行详细分析: callId=35173
2025-07-08 19:48:03,820 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\22-35173.txt
2025-07-08 19:48:03,820 - INFO - 处理 callId=10057, callTime=5.269688ms, rank=23
2025-07-08 19:48:03,820 - INFO - 执行详细分析: callId=10057
2025-07-08 19:48:03,858 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\23-10057.txt
2025-07-08 19:48:03,858 - INFO - 处理 callId=30831, callTime=5.253907ms, rank=24
2025-07-08 19:48:03,859 - INFO - 执行详细分析: callId=30831
2025-07-08 19:48:03,893 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\24-30831.txt
2025-07-08 19:48:03,893 - INFO - 处理 callId=33403, callTime=5.226093ms, rank=25
2025-07-08 19:48:03,893 - INFO - 执行详细分析: callId=33403
2025-07-08 19:48:03,930 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\25-33403.txt
2025-07-08 19:48:03,930 - INFO - 处理 callId=32633, callTime=5.221094ms, rank=26
2025-07-08 19:48:03,930 - INFO - 执行详细分析: callId=32633
2025-07-08 19:48:03,966 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\26-32633.txt
2025-07-08 19:48:03,966 - INFO - 处理 callId=31555, callTime=5.193906ms, rank=27
2025-07-08 19:48:03,966 - INFO - 执行详细分析: callId=31555
2025-07-08 19:48:04,001 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\27-31555.txt
2025-07-08 19:48:04,001 - INFO - 处理 callId=28264, callTime=5.188125ms, rank=28
2025-07-08 19:48:04,001 - INFO - 执行详细分析: callId=28264
2025-07-08 19:48:04,036 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\28-28264.txt
2025-07-08 19:48:04,036 - INFO - 处理 callId=34095, callTime=5.160625ms, rank=29
2025-07-08 19:48:04,036 - INFO - 执行详细分析: callId=34095
2025-07-08 19:48:04,072 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\29-34095.txt
2025-07-08 19:48:04,073 - INFO - 处理 callId=23032, callTime=5.126406ms, rank=30
2025-07-08 19:48:04,073 - INFO - 执行详细分析: callId=23032
2025-07-08 19:48:04,101 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\30-23032.txt
2025-07-08 19:48:04,101 - INFO - 处理 callId=32325, callTime=5.086562ms, rank=31
2025-07-08 19:48:04,101 - INFO - 执行详细分析: callId=32325
2025-07-08 19:48:04,137 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\31-32325.txt
2025-07-08 19:48:04,137 - INFO - 处理 callId=32171, callTime=5.019844ms, rank=32
2025-07-08 19:48:04,137 - INFO - 执行详细分析: callId=32171
2025-07-08 19:48:04,173 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\32-32171.txt
2025-07-08 19:48:04,173 - INFO - 处理 callId=34249, callTime=4.977344ms, rank=33
2025-07-08 19:48:04,173 - INFO - 执行详细分析: callId=34249
2025-07-08 19:48:04,209 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\33-34249.txt
2025-07-08 19:48:04,210 - INFO - 处理 callId=33787, callTime=4.95ms, rank=34
2025-07-08 19:48:04,210 - INFO - 执行详细分析: callId=33787
2025-07-08 19:48:04,246 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\34-33787.txt
2025-07-08 19:48:04,246 - INFO - 处理 callId=34557, callTime=4.92125ms, rank=35
2025-07-08 19:48:04,246 - INFO - 执行详细分析: callId=34557
2025-07-08 19:48:04,283 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\35-34557.txt
2025-07-08 19:48:04,283 - INFO - 处理 callId=9109, callTime=4.917344ms, rank=36
2025-07-08 19:48:04,283 - INFO - 执行详细分析: callId=9109
2025-07-08 19:48:04,322 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\36-9109.txt
2025-07-08 19:48:04,322 - INFO - 处理 callId=22424, callTime=4.895781ms, rank=37
2025-07-08 19:48:04,322 - INFO - 执行详细分析: callId=22424
2025-07-08 19:48:04,351 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\37-22424.txt
2025-07-08 19:48:04,351 - INFO - 处理 callId=31863, callTime=4.824531ms, rank=38
2025-07-08 19:48:04,351 - INFO - 执行详细分析: callId=31863
2025-07-08 19:48:04,387 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\38-31863.txt
2025-07-08 19:48:04,387 - INFO - 处理 callId=33095, callTime=4.805312ms, rank=39
2025-07-08 19:48:04,387 - INFO - 执行详细分析: callId=33095
2025-07-08 19:48:04,423 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\39-33095.txt
2025-07-08 19:48:04,423 - INFO - 处理 callId=33249, callTime=4.777656ms, rank=40
2025-07-08 19:48:04,423 - INFO - 执行详细分析: callId=33249
2025-07-08 19:48:04,460 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动angle\40-33249.txt
2025-07-08 19:48:04,460 - INFO - 完成处理文件: perfmon首页热启动angle.dat
2025-07-08 19:48:04,460 - INFO - 处理进度: 30/30
2025-07-08 19:48:04,460 - INFO - 开始处理文件: perfmon首页热启动raster.dat
2025-07-08 19:48:04,460 - INFO - 执行命令: D:\feature_0603\debug\wps_build\WPSOffice\office6\kperfviewer.exe -cmd stat -top_num 40 -mode stat C:\Users\<USER>\Desktop\ANALYZE\src_dat\perfmon首页热启动raster.dat
2025-07-08 19:48:04,473 - INFO - 保存统计结果: C:\Users\<USER>\Desktop\ANALYZE\output\stats\ANALYSE_perfmon首页热启动raster.dat.txt
2025-07-08 19:48:04,473 - INFO - 找到event函数: E:\wpsskia\qt\source\qtbase\src\widgets\kernel\qwidget.cpp:9682 event
2025-07-08 19:48:04,474 - INFO - 提取callId: 14, callTime: 28.390781ms
2025-07-08 19:48:04,474 - INFO - 提取callId: 452, callTime: 18.665469ms
2025-07-08 19:48:04,474 - INFO - 提取callId: 2172, callTime: 14.970156ms
2025-07-08 19:48:04,474 - INFO - 提取callId: 2176, callTime: 14.62375ms
2025-07-08 19:48:04,474 - INFO - 提取callId: 892, callTime: 13.984532ms
2025-07-08 19:48:04,474 - INFO - 提取callId: 1309, callTime: 13.832969ms
2025-07-08 19:48:04,474 - INFO - 提取callId: 1735, callTime: 10.122968ms
2025-07-08 19:48:04,474 - INFO - 提取callId: 2372, callTime: 9.395938ms
2025-07-08 19:48:04,475 - INFO - 提取callId: 2380, callTime: 8.84125ms
2025-07-08 19:48:04,475 - INFO - 提取callId: 2540, callTime: 8.0575ms
2025-07-08 19:48:04,475 - INFO - 提取callId: 2468, callTime: 7.527188ms
2025-07-08 19:48:04,475 - INFO - 提取callId: 2388, callTime: 7.320937ms
2025-07-08 19:48:04,475 - INFO - 提取callId: 2152, callTime: 7.163594ms
2025-07-08 19:48:04,475 - INFO - 提取callId: 2376, callTime: 6.843125ms
2025-07-08 19:48:04,475 - INFO - 提取callId: 2156, callTime: 6.677188ms
2025-07-08 19:48:04,475 - INFO - 提取callId: 2384, callTime: 6.6125ms
2025-07-08 19:48:04,475 - INFO - 提取callId: 2560, callTime: 6.124687ms
2025-07-08 19:48:04,475 - INFO - 提取callId: 2460, callTime: 6.107969ms
2025-07-08 19:48:04,476 - INFO - 提取callId: 2200, callTime: 6.0625ms
2025-07-08 19:48:04,476 - INFO - 提取callId: 2224, callTime: 5.937031ms
2025-07-08 19:48:04,476 - INFO - 提取callId: 2184, callTime: 5.609219ms
2025-07-08 19:48:04,476 - INFO - 提取callId: 2556, callTime: 5.60875ms
2025-07-08 19:48:04,476 - INFO - 提取callId: 2332, callTime: 5.601562ms
2025-07-08 19:48:04,476 - INFO - 提取callId: 2524, callTime: 5.422032ms
2025-07-08 19:48:04,476 - INFO - 提取callId: 2244, callTime: 5.225625ms
2025-07-08 19:48:04,476 - INFO - 提取callId: 2396, callTime: 5.0275ms
2025-07-08 19:48:04,476 - INFO - 提取callId: 2492, callTime: 4.910312ms
2025-07-08 19:48:04,476 - INFO - 提取callId: 2512, callTime: 4.9025ms
2025-07-08 19:48:04,476 - INFO - 提取callId: 2484, callTime: 4.842969ms
2025-07-08 19:48:04,476 - INFO - 提取callId: 2472, callTime: 4.785625ms
2025-07-08 19:48:04,476 - INFO - 提取callId: 2320, callTime: 4.729219ms
2025-07-08 19:48:04,476 - INFO - 提取callId: 2528, callTime: 4.669218ms
2025-07-08 19:48:04,477 - INFO - 提取callId: 2368, callTime: 4.625938ms
2025-07-08 19:48:04,477 - INFO - 提取callId: 2476, callTime: 4.528906ms
2025-07-08 19:48:04,477 - INFO - 提取callId: 2552, callTime: 4.489375ms
2025-07-08 19:48:04,477 - INFO - 提取callId: 2536, callTime: 4.432812ms
2025-07-08 19:48:04,477 - INFO - 提取callId: 2500, callTime: 4.403906ms
2025-07-08 19:48:04,477 - INFO - 提取callId: 1726, callTime: 4.239687ms
2025-07-08 19:48:04,477 - INFO - 提取callId: 2496, callTime: 4.23375ms
2025-07-08 19:48:04,477 - INFO - 提取callId: 2544, callTime: 4.1325ms
2025-07-08 19:48:04,477 - INFO - 提取到 40 个event相关的callId
2025-07-08 19:48:04,477 - INFO - 处理 callId=14, callTime=28.390781ms, rank=1
2025-07-08 19:48:04,477 - INFO - 执行详细分析: callId=14
2025-07-08 19:48:04,493 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\1-14.txt
2025-07-08 19:48:04,493 - INFO - 处理 callId=452, callTime=18.665469ms, rank=2
2025-07-08 19:48:04,493 - INFO - 执行详细分析: callId=452
2025-07-08 19:48:04,509 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\2-452.txt
2025-07-08 19:48:04,509 - INFO - 处理 callId=2172, callTime=14.970156ms, rank=3
2025-07-08 19:48:04,509 - INFO - 执行详细分析: callId=2172
2025-07-08 19:48:04,519 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\3-2172.txt
2025-07-08 19:48:04,520 - INFO - 处理 callId=2176, callTime=14.62375ms, rank=4
2025-07-08 19:48:04,520 - INFO - 执行详细分析: callId=2176
2025-07-08 19:48:04,530 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\4-2176.txt
2025-07-08 19:48:04,530 - INFO - 处理 callId=892, callTime=13.984532ms, rank=5
2025-07-08 19:48:04,531 - INFO - 执行详细分析: callId=892
2025-07-08 19:48:04,546 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\5-892.txt
2025-07-08 19:48:04,546 - INFO - 处理 callId=1309, callTime=13.832969ms, rank=6
2025-07-08 19:48:04,546 - INFO - 执行详细分析: callId=1309
2025-07-08 19:48:04,563 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\6-1309.txt
2025-07-08 19:48:04,564 - INFO - 处理 callId=1735, callTime=10.122968ms, rank=7
2025-07-08 19:48:04,564 - INFO - 执行详细分析: callId=1735
2025-07-08 19:48:04,580 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\7-1735.txt
2025-07-08 19:48:04,580 - INFO - 处理 callId=2372, callTime=9.395938ms, rank=8
2025-07-08 19:48:04,581 - INFO - 执行详细分析: callId=2372
2025-07-08 19:48:04,592 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\8-2372.txt
2025-07-08 19:48:04,592 - INFO - 处理 callId=2380, callTime=8.84125ms, rank=9
2025-07-08 19:48:04,592 - INFO - 执行详细分析: callId=2380
2025-07-08 19:48:04,611 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\9-2380.txt
2025-07-08 19:48:04,611 - INFO - 处理 callId=2540, callTime=8.0575ms, rank=10
2025-07-08 19:48:04,611 - INFO - 执行详细分析: callId=2540
2025-07-08 19:48:04,622 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\10-2540.txt
2025-07-08 19:48:04,622 - INFO - 处理 callId=2468, callTime=7.527188ms, rank=11
2025-07-08 19:48:04,622 - INFO - 执行详细分析: callId=2468
2025-07-08 19:48:04,633 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\11-2468.txt
2025-07-08 19:48:04,633 - INFO - 处理 callId=2388, callTime=7.320937ms, rank=12
2025-07-08 19:48:04,633 - INFO - 执行详细分析: callId=2388
2025-07-08 19:48:04,644 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\12-2388.txt
2025-07-08 19:48:04,645 - INFO - 处理 callId=2152, callTime=7.163594ms, rank=13
2025-07-08 19:48:04,645 - INFO - 执行详细分析: callId=2152
2025-07-08 19:48:04,655 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\13-2152.txt
2025-07-08 19:48:04,655 - INFO - 处理 callId=2376, callTime=6.843125ms, rank=14
2025-07-08 19:48:04,655 - INFO - 执行详细分析: callId=2376
2025-07-08 19:48:04,668 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\14-2376.txt
2025-07-08 19:48:04,668 - INFO - 处理 callId=2156, callTime=6.677188ms, rank=15
2025-07-08 19:48:04,668 - INFO - 执行详细分析: callId=2156
2025-07-08 19:48:04,679 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\15-2156.txt
2025-07-08 19:48:04,680 - INFO - 处理 callId=2384, callTime=6.6125ms, rank=16
2025-07-08 19:48:04,680 - INFO - 执行详细分析: callId=2384
2025-07-08 19:48:04,692 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\16-2384.txt
2025-07-08 19:48:04,692 - INFO - 处理 callId=2560, callTime=6.124687ms, rank=17
2025-07-08 19:48:04,692 - INFO - 执行详细分析: callId=2560
2025-07-08 19:48:04,704 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\17-2560.txt
2025-07-08 19:48:04,704 - INFO - 处理 callId=2460, callTime=6.107969ms, rank=18
2025-07-08 19:48:04,704 - INFO - 执行详细分析: callId=2460
2025-07-08 19:48:04,715 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\18-2460.txt
2025-07-08 19:48:04,715 - INFO - 处理 callId=2200, callTime=6.0625ms, rank=19
2025-07-08 19:48:04,716 - INFO - 执行详细分析: callId=2200
2025-07-08 19:48:04,727 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\19-2200.txt
2025-07-08 19:48:04,727 - INFO - 处理 callId=2224, callTime=5.937031ms, rank=20
2025-07-08 19:48:04,727 - INFO - 执行详细分析: callId=2224
2025-07-08 19:48:04,738 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\20-2224.txt
2025-07-08 19:48:04,738 - INFO - 处理 callId=2184, callTime=5.609219ms, rank=21
2025-07-08 19:48:04,738 - INFO - 执行详细分析: callId=2184
2025-07-08 19:48:04,749 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\21-2184.txt
2025-07-08 19:48:04,749 - INFO - 处理 callId=2556, callTime=5.60875ms, rank=22
2025-07-08 19:48:04,749 - INFO - 执行详细分析: callId=2556
2025-07-08 19:48:04,760 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\22-2556.txt
2025-07-08 19:48:04,760 - INFO - 处理 callId=2332, callTime=5.601562ms, rank=23
2025-07-08 19:48:04,760 - INFO - 执行详细分析: callId=2332
2025-07-08 19:48:04,770 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\23-2332.txt
2025-07-08 19:48:04,770 - INFO - 处理 callId=2524, callTime=5.422032ms, rank=24
2025-07-08 19:48:04,771 - INFO - 执行详细分析: callId=2524
2025-07-08 19:48:04,781 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\24-2524.txt
2025-07-08 19:48:04,781 - INFO - 处理 callId=2244, callTime=5.225625ms, rank=25
2025-07-08 19:48:04,781 - INFO - 执行详细分析: callId=2244
2025-07-08 19:48:04,793 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\25-2244.txt
2025-07-08 19:48:04,793 - INFO - 处理 callId=2396, callTime=5.0275ms, rank=26
2025-07-08 19:48:04,793 - INFO - 执行详细分析: callId=2396
2025-07-08 19:48:04,805 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\26-2396.txt
2025-07-08 19:48:04,805 - INFO - 处理 callId=2492, callTime=4.910312ms, rank=27
2025-07-08 19:48:04,805 - INFO - 执行详细分析: callId=2492
2025-07-08 19:48:04,816 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\27-2492.txt
2025-07-08 19:48:04,816 - INFO - 处理 callId=2512, callTime=4.9025ms, rank=28
2025-07-08 19:48:04,816 - INFO - 执行详细分析: callId=2512
2025-07-08 19:48:04,827 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\28-2512.txt
2025-07-08 19:48:04,827 - INFO - 处理 callId=2484, callTime=4.842969ms, rank=29
2025-07-08 19:48:04,827 - INFO - 执行详细分析: callId=2484
2025-07-08 19:48:04,837 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\29-2484.txt
2025-07-08 19:48:04,838 - INFO - 处理 callId=2472, callTime=4.785625ms, rank=30
2025-07-08 19:48:04,838 - INFO - 执行详细分析: callId=2472
2025-07-08 19:48:04,848 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\30-2472.txt
2025-07-08 19:48:04,848 - INFO - 处理 callId=2320, callTime=4.729219ms, rank=31
2025-07-08 19:48:04,848 - INFO - 执行详细分析: callId=2320
2025-07-08 19:48:04,859 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\31-2320.txt
2025-07-08 19:48:04,859 - INFO - 处理 callId=2528, callTime=4.669218ms, rank=32
2025-07-08 19:48:04,859 - INFO - 执行详细分析: callId=2528
2025-07-08 19:48:04,870 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\32-2528.txt
2025-07-08 19:48:04,870 - INFO - 处理 callId=2368, callTime=4.625938ms, rank=33
2025-07-08 19:48:04,870 - INFO - 执行详细分析: callId=2368
2025-07-08 19:48:04,881 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\33-2368.txt
2025-07-08 19:48:04,881 - INFO - 处理 callId=2476, callTime=4.528906ms, rank=34
2025-07-08 19:48:04,881 - INFO - 执行详细分析: callId=2476
2025-07-08 19:48:04,892 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\34-2476.txt
2025-07-08 19:48:04,893 - INFO - 处理 callId=2552, callTime=4.489375ms, rank=35
2025-07-08 19:48:04,893 - INFO - 执行详细分析: callId=2552
2025-07-08 19:48:04,904 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\35-2552.txt
2025-07-08 19:48:04,904 - INFO - 处理 callId=2536, callTime=4.432812ms, rank=36
2025-07-08 19:48:04,904 - INFO - 执行详细分析: callId=2536
2025-07-08 19:48:04,915 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\36-2536.txt
2025-07-08 19:48:04,915 - INFO - 处理 callId=2500, callTime=4.403906ms, rank=37
2025-07-08 19:48:04,915 - INFO - 执行详细分析: callId=2500
2025-07-08 19:48:04,926 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\37-2500.txt
2025-07-08 19:48:04,927 - INFO - 处理 callId=1726, callTime=4.239687ms, rank=38
2025-07-08 19:48:04,927 - INFO - 执行详细分析: callId=1726
2025-07-08 19:48:04,937 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\38-1726.txt
2025-07-08 19:48:04,937 - INFO - 处理 callId=2496, callTime=4.23375ms, rank=39
2025-07-08 19:48:04,938 - INFO - 执行详细分析: callId=2496
2025-07-08 19:48:04,950 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\39-2496.txt
2025-07-08 19:48:04,950 - INFO - 处理 callId=2544, callTime=4.1325ms, rank=40
2025-07-08 19:48:04,950 - INFO - 执行详细分析: callId=2544
2025-07-08 19:48:04,962 - INFO - 保存详细分析结果: C:\Users\<USER>\Desktop\ANALYZE\output\timeAnalyse\perfmon首页热启动raster\40-2544.txt
2025-07-08 19:48:04,963 - INFO - 完成处理文件: perfmon首页热启动raster.dat
2025-07-08 19:48:04,963 - INFO - 性能数据分析完成
